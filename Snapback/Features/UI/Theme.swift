import SwiftUI

/// A centralized theme for the Snapback app
/// This struct provides consistent styling across the app
struct SnapbackTheme {
    // MARK: - Colors

    /// Background colors
    struct Background {
        /// Main window background
        static let window = Color(NSColor.windowBackgroundColor)

        /// Card background
        static let card = Color(NSColor.windowBackgroundColor)

        /// Selected card background
        static let selectedCard = Color.accentColor.opacity(0.1)

        /// Warning background
        static let warning = Color.orange.opacity(0.1)

        /// Info background
        static let info = Color.blue.opacity(0.1)

    }

    /// Border colors
    struct Border {
        /// Default border color for cards
        static let card = Color.gray.opacity(0.2)

        /// Selected border color for cards
        static let selectedCard = Color.accentColor

        /// Warning border color
        static let warning = Color.orange

        /// Info border color
        static let info = Color.blue
    }

    /// Text colors
    struct Text {
        /// Primary text color
        static let primary = Color.primary

        /// Secondary text color
        static let secondary = Color.secondary

        /// Caption text color
        static let caption = Color.secondary

        /// Accent text color for interactive elements
        static let accent = Color.accentColor

        /// Error text color
        static let error = Color.red
    }

    // MARK: - Dimensions

    /// Border widths
    struct BorderWidth {
        /// Default border width
        static let normal: CGFloat = 1

        /// Selected border width
        static let selected: CGFloat = 2
    }

    /// Corner radii
    struct CornerRadius {
        /// Default corner radius for cards
        static let card: CGFloat = 8

        /// Small corner radius for badges and small elements
        static let small: CGFloat = 6

        /// Large corner radius for prominent elements
        static let large: CGFloat = 10
    }

    /// Padding values
    struct Padding {
        /// Standard padding for card content
        static let standard: CGFloat = 12

        /// Vertical padding for rows
        static let vertical: CGFloat = 8

        /// Horizontal padding for rows
        static let horizontal: CGFloat = 12

        /// Small padding for compact spacing
        static let small: CGFloat = 4

        /// Large padding for section spacing
        static let large: CGFloat = 16

        /// Extra large padding for prominent spacing
        static let extraLarge: CGFloat = 20

        /// Extra small padding for minimal spacing
        static let extraSmall: CGFloat = 2

        /// Content edge insets for settings sections
        static let section = EdgeInsets(top: 16, leading: 16, bottom: 16, trailing: 16)
    }

    // MARK: - Typography

    /// Font sizes
    struct FontSize {
        /// Title font size
        static let title: CGFloat = 20

        /// Heading font size
        static let heading: CGFloat = 15

        /// Body font size
        static let body: CGFloat = 13

        /// Caption font size
        static let caption: CGFloat = 11
    }

    /// Font weights
    struct FontWeight {
        /// Title font weight
        static let title = Font.Weight.semibold

        /// Heading font weight
        static let heading = Font.Weight.medium

        /// Body font weight
        static let body = Font.Weight.regular

        /// Caption font weight
        static let caption = Font.Weight.regular
    }

    // MARK: - Effects

    /// Shadow styles
    struct Shadow {
        /// Card shadow
        static let card = (
            color: Color.black.opacity(0.1), radius: CGFloat(5), x: CGFloat(0), y: CGFloat(2)
        )

        /// Button shadow
        static let button = (
            color: Color.black.opacity(0.2), radius: CGFloat(2), x: CGFloat(0), y: CGFloat(1)
        )

        /// Toast shadow
        static let toast = (
            color: Color.black.opacity(0.15), radius: CGFloat(8), x: CGFloat(0), y: CGFloat(4)
        )
    }

    /// Animation constants
    struct Animation {
        /// Standard animation duration
        static let standard = SwiftUI.Animation.easeInOut(duration: 0.2)

        /// Quick animation duration
        static let quick = SwiftUI.Animation.easeInOut(duration: 0.1)

        /// Slow animation duration
        static let slow = SwiftUI.Animation.easeInOut(duration: 0.3)
    }

    // MARK: - View Modifiers

    /// Card style modifier
    struct CardStyle: ViewModifier {
        var isSelected: Bool = false

        func body(content: Content) -> some View {
            content
                .background(isSelected ? Background.selectedCard : Background.card)
                .clipShape(RoundedRectangle(cornerRadius: CornerRadius.card))
                .overlay(
                    RoundedRectangle(cornerRadius: CornerRadius.card)
                        .strokeBorder(
                            isSelected ? Border.selectedCard : Border.card,
                            lineWidth: isSelected ? BorderWidth.selected : BorderWidth.normal
                        )
                )
        }
    }

    /// Section title style modifier
    struct SectionTitleStyle: ViewModifier {
        func body(content: Content) -> some View {
            content
                .font(.system(size: FontSize.heading, weight: FontWeight.heading))
                .foregroundColor(Text.primary)
                .padding(.top, Padding.vertical)
        }
    }

    /// Row style modifier
    struct RowStyle: ViewModifier {
        func body(content: Content) -> some View {
            content
                .padding(.vertical, Padding.vertical)
                .padding(.horizontal, Padding.horizontal)
        }
    }

    /// Caption style modifier
    struct CaptionStyle: ViewModifier {
        func body(content: Content) -> some View {
            content
                .font(.system(size: FontSize.caption))
                .foregroundColor(Text.caption)
                .frame(maxWidth: 350, alignment: .leading)
                .fixedSize(horizontal: false, vertical: true)
        }
    }
}

// MARK: - View Extensions

extension View {
    /// Apply the card style
    func snapbackCardStyle(isSelected: Bool = false) -> some View {
        self.modifier(SnapbackTheme.CardStyle(isSelected: isSelected))
    }

    /// Apply the section title style
    func snapbackSectionTitleStyle() -> some View {
        self.modifier(SnapbackTheme.SectionTitleStyle())
    }

    /// Apply the row style
    func snapbackRowStyle() -> some View {
        self.modifier(SnapbackTheme.RowStyle())
    }

    /// Apply the caption style
    func snapbackCaptionStyle() -> some View {
        self.modifier(SnapbackTheme.CaptionStyle())
    }

    /// Apply error text style
    func snapbackErrorStyle() -> some View {
        self.font(.system(size: SnapbackTheme.FontSize.body))
            .foregroundColor(SnapbackTheme.Text.error)
    }

    /// Apply accent text style
    func snapbackAccentStyle() -> some View {
        self.font(.system(size: SnapbackTheme.FontSize.body))
            .foregroundColor(SnapbackTheme.Text.accent)
    }

    /// Apply settings section container style
    func snapbackSettingsSectionStyle() -> some View {
        self.padding(SnapbackTheme.Padding.section)
            .background(SnapbackTheme.Background.card)
            .clipShape(RoundedRectangle(cornerRadius: SnapbackTheme.CornerRadius.card))
    }

    /// Apply card shadow
    func snapbackCardShadow() -> some View {
        let shadow = SnapbackTheme.Shadow.card
        return self.shadow(color: shadow.color, radius: shadow.radius, x: shadow.x, y: shadow.y)
    }

    /// Apply button shadow
    func snapbackButtonShadow() -> some View {
        let shadow = SnapbackTheme.Shadow.button
        return self.shadow(color: shadow.color, radius: shadow.radius, x: shadow.x, y: shadow.y)
    }

    /// Apply toast shadow
    func snapbackToastShadow() -> some View {
        let shadow = SnapbackTheme.Shadow.toast
        return self.shadow(color: shadow.color, radius: shadow.radius, x: shadow.x, y: shadow.y)
    }
}

// MARK: - Direct Style Functions

extension SnapbackTheme {
    /// Apply card styling directly to a view
    static func applyCardStyle(to content: some View, isSelected: Bool = false) -> some View {
        content
            .background(isSelected ? Background.selectedCard : Background.card)
            .clipShape(RoundedRectangle(cornerRadius: CornerRadius.card))
            .overlay(
                RoundedRectangle(cornerRadius: CornerRadius.card)
                    .strokeBorder(
                        isSelected ? Border.selectedCard : Border.card,
                        lineWidth: isSelected ? BorderWidth.selected : BorderWidth.normal
                    )
            )
    }
}
