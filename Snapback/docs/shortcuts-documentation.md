# Shortcuts Module Documentation

## Overview
The Shortcuts module provides comprehensive keyboard shortcut management for Snapback, including window management shortcuts, workspace shortcuts, conflict detection, and validation. It bridges multiple shortcut frameworks (KeyboardShortcuts, MASShortcut) and provides a unified interface for shortcut recording, validation, and execution.

## Directory Structure

```
Shortcuts/
├── Core Management
│   ├── ShortcutManager.swift
│   ├── ShortcutService.swift
│   ├── ShortcutDefaults.swift
│   └── ShortcutUtils.swift
├── Framework Integration
│   ├── KeyboardShortcutsBridge.swift
│   ├── MASShortcutManager.swift
│   ├── ShortcutConverter.swift
│   └── KeyboardShortcutNames.swift
├── Validation & Conflict Detection
│   ├── ShortcutValidator.swift
│   ├── SystemShortcutChecker.swift
│   └── ConflictingAppsChecker.swift
├── User Interface
│   └── ShortcutRecorder.swift
└── Documentation & Examples
    └── ShortcutManagerUsageExample.swift
```

## Core Management

### ShortcutManager.swift
**Type:** Swift Class (Singleton)  
**Purpose:** Central shortcut management system providing unified interface for all shortcut operations.

**Key Components:**
- `ShortcutManager.shared`: Singleton instance
- `ShortcutManagerProtocol`: Protocol defining core shortcut operations
- `Shortcut`: Core shortcut data structure with keyCode and modifiers
- `ShortcutValidationResult`: Validation result with conflict information
- `setShortcut(_:forName:)`: Sets shortcuts with validation
- `getShortcut(forName:)`: Retrieves stored shortcuts
- `validateShortcut(_:)`: Comprehensive shortcut validation
- `checkSystemConflicts(for:)`: System shortcut conflict detection
- `checkApplicationConflicts(for:)`: Application conflict detection
- `resetAllShortcuts()`: Resets all shortcuts to defaults
- Implementation delegation pattern for different shortcut frameworks

### ShortcutService.swift
**Type:** Swift Class  
**Purpose:** High-level service coordinating shortcut registration, workspace shortcuts, and window management shortcuts.

**Key Components:**
- Integration with WorkspaceService for dynamic workspace shortcuts
- `registerKeyboardShortcuts()`: Registers all application shortcuts
- `registerWindowManagementShortcuts()`: Window positioning shortcuts (left/right/top/bottom half, quarters, etc.)
- `registerWorkspaceShortcuts()`: Dynamic workspace restoration shortcuts
- `registerWorkspaceShortcutImmediately(for:)`: Immediate registration for new workspaces
- `updateShortcuts()`: Updates shortcuts when workspaces change
- `unregisterAllShortcuts()`: Cleanup for app termination
- Temporary shortcut handling for workspace creation flow
- Integration with WindowSnappingService for window management actions

### ShortcutDefaults.swift
**Type:** Swift Class (Singleton)  
**Purpose:** Manages default shortcut configurations and user customizations.

**Key Components:**
- `ShortcutConfig`: Codable shortcut configuration with display string generation
- `ShortcutDefaults.shared`: Singleton instance
- Default shortcut mappings for all window management actions:
  - **Left/Right/Top/Bottom Half**: Ctrl+Option+Arrow keys
  - **Quarters**: Ctrl+Option+Number pad or WASD keys
  - **Thirds**: Ctrl+Option+1/2/3 for left/center/right thirds
  - **Two-thirds**: Ctrl+Option+4/5/6 for left/center/right two-thirds
  - **Fullscreen**: Ctrl+Option+Return
- `getShortcut(for:)`: Retrieves shortcut with fallback to defaults
- `setShortcut(for:shortcut:)`: Saves custom shortcuts to UserDefaults
- `resetShortcut(for:)`: Resets individual shortcuts to defaults
- `resetAllShortcuts()`: Resets all shortcuts to defaults

### ShortcutUtils.swift
**Type:** Swift Utility Functions  
**Purpose:** Low-level utilities for shortcut formatting, key code conversion, and display.

**Key Components:**
- `formatShortcut(keyCode:modifiers:)`: Formats shortcuts for display with proper symbols (⌘⌥⌃⇧)
- `keycodeToString(_:)`: Converts key codes to character representations
- `isModifierKeyCode(_:)`: Identifies modifier keys
- TIS (Text Input Source) integration for keyboard layout handling
- Unicode character mapping for special keys
- Modifier flag symbol generation
- Key code validation and normalization
- Support for different keyboard layouts and languages

## Framework Integration

### KeyboardShortcutsBridge.swift
**Type:** Swift Class (Singleton)  
**Purpose:** Bridge between ShortcutManager and KeyboardShortcuts framework, providing unified interface.

**Key Components:**
- `KeyboardShortcutsBridge.shared`: Singleton instance
- `registerName(_:stringName:defaultShortcut:)`: Maps KeyboardShortcuts.Name to string names
- `registerShortcutHandler(for:action:)`: Registers shortcut handlers with KeyboardShortcuts
- `setShortcut(_:for:)`: Sets shortcuts in both frameworks simultaneously
- `getShortcutForName(_:)`: Retrieves shortcuts from KeyboardShortcuts
- `validateShortcut(_:)`: Validation using integrated conflict detection
- Notification handling for shortcut changes
- Prevention of infinite update loops between frameworks
- Menu refresh notifications for UI updates

### MASShortcutManager.swift
**Type:** Swift Class  
**Purpose:** MASShortcut framework integration with validation and conflict detection.

**Key Components:**
- Implementation of `ShortcutManagerProtocol` using MASShortcut
- `toMASShortcut(_:)`: Converts internal shortcuts to MASShortcut format
- `fromMASShortcut(_:)`: Converts MASShortcut to internal format
- `checkSystemConflicts(for:)`: System conflict detection using MASShortcutValidator
- `checkApplicationConflicts(for:)`: Application conflict detection
- Default shortcut setup for window management actions
- Integration with DefaultsManager for "Allow any keyboard shortcut" setting
- Bypass validation when user explicitly allows conflicts
- Comprehensive logging for debugging shortcut issues

### ShortcutConverter.swift
**Type:** Swift Utility Class  
**Purpose:** Converts between different shortcut formats (KeyboardShortcuts, MASShortcut, internal Shortcut).

**Key Components:**
- `toKeyboardShortcut(_:)`: Converts to KeyboardShortcuts.Shortcut
- `fromKeyboardShortcut(_:)`: Converts from KeyboardShortcuts.Shortcut
- `toMASShortcut(_:)`: Converts to MASShortcut
- `fromMASShortcut(_:)`: Converts from MASShortcut
- `keyboardShortcutToMASShortcut(_:)`: Direct conversion between frameworks
- `masShortcutToKeyboardShortcut(_:)`: Reverse conversion
- `shortcutToString(_:)`: String representation for display
- Handles key code and modifier flag conversions
- Null safety and error handling for invalid shortcuts

### KeyboardShortcutNames.swift
**Type:** Swift Extensions  
**Purpose:** Defines all KeyboardShortcuts.Name constants with default shortcuts.

**Key Components:**
- Window positioning shortcuts with default key combinations
- `allPredefinedShortcuts`: Array of all predefined shortcut names
- `workspaceShortcut(for:)`: Dynamic workspace shortcut name generation
- `actionName(from:)`: Extracts action names from shortcut names
- `getAllShortcuts()`: Returns all shortcuts including dynamic workspace shortcuts
- Default shortcut definitions:
  - **Halves**: Ctrl+Option+Arrow keys
  - **Quarters**: Ctrl+Option+WASD or number pad
  - **Thirds**: Ctrl+Option+1/2/3
  - **Save Workspace**: Ctrl+Option+S
- Integration with AppDelegate for workspace shortcut enumeration

## Validation & Conflict Detection

### ShortcutValidator.swift
**Type:** Swift Protocol & Classes
**Purpose:** Provides shortcut validation with system and application conflict detection.

**Key Components:**
- `ShortcutValidatorProtocol`: Protocol defining validation interface
- `DefaultShortcutValidator`: Full validation implementation with conflict checking
- `PassthroughShortcutValidator`: Bypass validator that allows any shortcut
- `isShortcutValid(_:)`: Main validation method
- `isShortcutConflictingWithSystem(_:)`: System shortcut conflict detection
- `isShortcutConflictingWithOtherApps(_:)`: Application conflict detection
- Integration with MASShortcutValidator for system conflict detection
- ConflictingAppsChecker integration for application conflicts
- Configurable validation bypass for power users

### SystemShortcutChecker.swift
**Type:** Swift Class (Singleton)
**Purpose:** Detects conflicts with macOS system shortcuts and active application shortcuts.

**Key Components:**
- `SystemShortcutChecker.shared`: Singleton instance
- `checkForSystemConflict(keyCode:modifiers:)`: Main system conflict detection
- Known system shortcuts database with descriptions
- Active application shortcut detection
- Global hotkey manager conflict detection
- Menu bar shortcut enumeration and checking
- Accessibility API integration for shortcut discovery
- Comprehensive system shortcut database covering:
  - **Spotlight**: Cmd+Space
  - **Mission Control**: F3, Ctrl+Up
  - **Application switching**: Cmd+Tab
  - **Window management**: Cmd+M, Cmd+H
  - **System preferences**: Various system shortcuts

### ConflictingAppsChecker.swift
**Type:** Swift Class (Singleton)
**Purpose:** Detects conflicts with other window management applications and problematic apps.

**Key Components:**
- `ConflictingAppsChecker.shared`: Singleton instance
- Known conflicting window management apps database:
  - **Rectangle**: com.knollsoft.Rectangle
  - **Spectacle**: com.divisiblebyzero.Spectacle
  - **Magnet**: com.crowdcafe.windowmagnet
  - **BetterSnapTool**: com.hegenberg.BetterSnapTool
  - **Moom**: com.manytricks.Moom
- `checkForConflictingApps()`: Detects running conflicting applications
- `checkForProblematicApps()`: Identifies apps that interfere with drag-to-snap
- `checkShortcutForConflicts(keyCode:modifiers:)`: Shortcut-specific conflict detection
- `getRunningConflictingApps()`: Cached list of running conflicting apps
- Java-based application detection (known to cause issues)
- macOS 15+ built-in tiling conflict detection (placeholder for future)

## User Interface

### ShortcutRecorder.swift
**Type:** SwiftUI Views
**Purpose:** SwiftUI components for shortcut recording and display in settings interfaces.

**Key Components:**
- `ShortcutRecorder`: Basic shortcut recorder view wrapping KeyboardShortcuts.Recorder
- `ShortcutRecorderRow`: Complete settings row with recorder, label, and reset options
- Real-time shortcut change handling with callbacks
- Integration with ShortcutConverter for format conversion
- Reset to default and clear shortcut options
- Menu-based additional options (reset, clear)
- Consistent styling with Snapback theme
- Accessibility support for keyboard navigation
- Label alignment and spacing for settings interfaces

## Documentation & Examples

### ShortcutManagerUsageExample.swift
**Type:** Swift Class
**Purpose:** Comprehensive usage examples and documentation for the shortcut system.

**Key Components:**
- `basicUsageExample()`: Basic shortcut registration and handling
- `validateShortcutExample()`: Shortcut validation with conflict detection
- `setShortcutExample()`: Setting and retrieving shortcuts
- `resetShortcutExample()`: Resetting shortcuts to defaults
- Code examples for common shortcut operations
- Integration patterns with KeyboardShortcutsBridge
- Best practices for shortcut management
- Error handling examples

## Integration and Dependencies

### Service Dependencies
- **LoggingService**: Comprehensive logging for debugging shortcut issues
- **DefaultsManager**: User preferences including "Allow any keyboard shortcut" setting
- **WorkspaceService**: Dynamic workspace shortcut registration
- **WindowSnappingService**: Window management action execution
- **AppDelegate**: Application lifecycle and workspace enumeration
- **ToastManager**: User feedback for shortcut conflicts and errors

### Key Relationships
1. **ShortcutService** → **KeyboardShortcutsBridge** → **KeyboardShortcuts framework**
2. **ShortcutManager** → **MASShortcutManager** → **MASShortcut framework**
3. **Validation** → **SystemShortcutChecker** + **ConflictingAppsChecker**
4. **UI Components** → **ShortcutRecorder** → **KeyboardShortcuts.Recorder**
5. **WorkspaceService** ↔ **ShortcutService** for dynamic workspace shortcuts

### Configuration Integration
- Default shortcuts defined in KeyboardShortcutNames and ShortcutDefaults
- User customizations stored in UserDefaults with JSON encoding
- "Allow any keyboard shortcut" setting bypasses all validation
- Conflict detection integrates with system APIs and running application detection
- Dynamic workspace shortcuts created/destroyed based on workspace changes

## Usage Patterns

### Basic Shortcut Registration
```swift
// Register window management shortcuts
ShortcutService.shared.registerWindowManagementShortcuts()

// Register workspace shortcuts
ShortcutService.shared.registerWorkspaceShortcuts()
```

### Custom Shortcut Handling
```swift
// Set a custom shortcut
let shortcut = Shortcut(keyCode: Int(kVK_ANSI_A), modifiers: [.command, .shift])
ShortcutManager.shared.setShortcut(shortcut, forName: "leftHalf")

// Validate before setting
let result = ShortcutManager.shared.validateShortcut(shortcut)
if result.isValid {
    // Safe to use
} else {
    // Handle conflicts
}
```

### Dynamic Workspace Shortcuts
```swift
// Register shortcut for new workspace
ShortcutService.shared.registerWorkspaceShortcutImmediately(for: workspace)

// Update all shortcuts when workspaces change
ShortcutService.shared.updateShortcuts()
```

### Conflict Detection
```swift
// Check for system conflicts
let (hasConflict, explanation) = ShortcutManager.shared.checkSystemConflicts(for: shortcut)
if hasConflict {
    print("Conflict: \(explanation ?? "Unknown")")
}

// Check for app conflicts
let conflictingApps = ConflictingAppsChecker.shared.getRunningConflictingApps()
```

## Key Features

### 🎹 **Comprehensive Shortcut Support**
- Window management shortcuts (halves, quarters, thirds, fullscreen)
- Dynamic workspace restoration shortcuts
- Custom shortcut recording and validation
- Multiple framework integration (KeyboardShortcuts, MASShortcut)

### ⚠️ **Advanced Conflict Detection**
- System shortcut conflict detection using macOS APIs
- Running application shortcut analysis
- Window management app conflict detection
- Configurable validation bypass for power users

### 🔄 **Dynamic Management**
- Automatic workspace shortcut registration/unregistration
- Real-time shortcut updates when workspaces change
- Temporary shortcut handling during workspace creation
- Menu refresh notifications for UI consistency

### 🎨 **User Interface Integration**
- SwiftUI shortcut recorder components
- Real-time validation feedback
- Reset to defaults functionality
- Consistent styling with app theme

### 🛠️ **Developer-Friendly**
- Comprehensive usage examples and documentation
- Protocol-based architecture for extensibility
- Unified interface across multiple shortcut frameworks
- Extensive logging for debugging

## Default Shortcut Mappings

### Window Management
- **Left Half**: Ctrl+Option+Left Arrow
- **Right Half**: Ctrl+Option+Right Arrow
- **Top Half**: Ctrl+Option+Up Arrow
- **Bottom Half**: Ctrl+Option+Down Arrow
- **Top Left Quarter**: Ctrl+Option+W
- **Top Right Quarter**: Ctrl+Option+E
- **Bottom Left Quarter**: Ctrl+Option+S
- **Bottom Right Quarter**: Ctrl+Option+D
- **Left Third**: Ctrl+Option+1
- **Center Third**: Ctrl+Option+2
- **Right Third**: Ctrl+Option+3
- **Left Two-Thirds**: Ctrl+Option+4
- **Center Two-Thirds**: Ctrl+Option+5
- **Right Two-Thirds**: Ctrl+Option+6
- **Fullscreen**: Ctrl+Option+Return

### Workspace Management
- **Save Workspace**: Ctrl+Option+S
- **Workspace Shortcuts**: User-defined per workspace

## Architecture Benefits

### 🏗️ **Modular Design**
- Clear separation between frameworks, validation, and UI
- Protocol-based interfaces for easy testing and extension
- Singleton pattern for consistent state management

### 🔒 **Robust Validation**
- Multi-layered conflict detection (system, apps, shortcuts)
- Graceful degradation when validation APIs unavailable
- User override capabilities for advanced users

### 📱 **Framework Agnostic**
- Unified interface regardless of underlying shortcut framework
- Easy migration between different shortcut libraries
- Consistent behavior across different macOS versions

This module provides a complete keyboard shortcut management solution that handles the complexities of macOS shortcut systems while providing a clean, unified interface for both users and developers.
