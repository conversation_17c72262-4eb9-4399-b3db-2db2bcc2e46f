import SwiftUI

struct DeviceManagementView: View {
    @StateObject private var licenseManager = LicenseManager.shared
    @State private var licenseStatus: LicenseAPIService.LicenseStatusResponse?
    @State private var isLoadingStatus = false
    @State private var statusError: String?
    @State private var showingRemoveConfirmation = false
    @State private var deviceToRemove: LicenseAPIService.DeviceInfo?
    @State private var isRemovingDevice = false

    /// Context flag indicating if this view is shown due to "max devices reached" error
    let isMaxDevicesReachedContext: Bool

    /// Initialize with context information
    init(isMaxDevicesReachedContext: Bool = false) {
        self.isMaxDevicesReachedContext = isMaxDevicesReachedContext
    }

    var body: some View {
        VStack(alignment: .leading, spacing: SnapbackTheme.Padding.standard) {
            // Device Usage Section
            Text("Device Management")
                .snapbackSectionTitleStyle()

            // Show explanatory text when in "max devices reached" context
            if isMaxDevicesReachedContext {
                GroupBox {
                    VStack(alignment: .leading, spacing: SnapbackTheme.Padding.small) {
                        HStack {
                            Image(systemName: "exclamationmark.triangle.fill")
                                .foregroundColor(.orange)
                            Text("Maximum Devices Reached")
                                .font(.system(size: SnapbackTheme.FontSize.body, weight: .medium))
                                .foregroundColor(.primary)
                        }

                        Text(
                            "You've reached the device limit for your license. Remove a device below to free up a slot for this device, or upgrade your license to add more device slots."
                        )
                        .snapbackCaptionStyle()
                        .fixedSize(horizontal: false, vertical: true)
                    }
                    .snapbackRowStyle()
                }
            }

            GroupBox {
                VStack(spacing: 0) {
                    if isLoadingStatus {
                        HStack {
                            ProgressView()
                                .scaleEffect(0.8)
                            Text("Loading device information...")
                                .snapbackCaptionStyle()
                            Spacer()
                        }
                        .snapbackRowStyle()
                    } else if let error = statusError {
                        VStack(alignment: .leading, spacing: SnapbackTheme.Padding.small) {
                            HStack {
                                Image.systemCompat("Warning")
                                    .foregroundColor(.orange)
                                    .frame(width: 16)

                                Text("Unable to load device information")
                                    .foregroundColor(.orange)

                                Spacer()
                            }

                            Text(error)
                                .snapbackCaptionStyle()
                                .foregroundColor(.red)
                        }
                        .snapbackRowStyle()
                    } else if let status = licenseStatus {
                        // Device Usage Summary
                        HStack {
                            Image.systemCompat("desktopcomputer")
                                .foregroundColor(.secondary)
                                .frame(width: 16)

                            Text("Device Usage")

                            Spacer()

                            Text("\(status.devicesUsed) of \(status.maxDevices) devices")
                                .foregroundColor(
                                    status.devicesUsed >= status.maxDevices ? .orange : .secondary
                                )
                                .font(.system(size: SnapbackTheme.FontSize.body, weight: .medium))
                        }
                        .snapbackRowStyle()

                        // Device List
                        if !status.devices.isEmpty {
                            Divider()

                            ForEach(Array(status.devices.enumerated()), id: \.offset) {
                                index, device in
                                VStack(spacing: 0) {
                                    if index > 0 {
                                        Divider()
                                    }

                                    deviceRow(device: device, canRemove: true)
                                }
                            }
                        }
                    } else {
                        VStack(alignment: .leading, spacing: SnapbackTheme.Padding.small) {
                            Text("Device Information")
                                .font(.system(size: SnapbackTheme.FontSize.body, weight: .medium))

                            Text(
                                "Load device information to view registered devices and manage device slots."
                            )
                            .snapbackCaptionStyle()

                            HStack {
                                Button("Load Device Information") {
                                    loadLicenseStatus()
                                }
                                .buttonStyle(.bordered)

                                Spacer()
                            }
                            .padding(.top, SnapbackTheme.Padding.small)
                        }
                        .snapbackRowStyle()
                    }
                }
            }
        }
        .onAppear {
            // Auto-load device information if we have a valid license
            print("🔍 DEVICE_MGMT_DEBUG: onAppear - License Status: \(licenseManager.licenseStatus)")
            print(
                "🔍 DEVICE_MGMT_DEBUG: onAppear - License Key: '\(licenseManager.licenseKey.isEmpty ? "EMPTY" : "HAS_KEY")'"
            )
            print(
                "🔍 DEVICE_MGMT_DEBUG: onAppear - isMaxDevicesReachedContext: \(isMaxDevicesReachedContext)"
            )

            if licenseManager.licenseStatus == .valid {
                print("🔍 DEVICE_MGMT_DEBUG: Auto-loading device information")
                loadLicenseStatus()
            } else {
                print(
                    "🔍 DEVICE_MGMT_DEBUG: Not auto-loading - license status is \(licenseManager.licenseStatus)"
                )
            }
        }
        .alert("Remove Device", isPresented: $showingRemoveConfirmation) {
            Button("Cancel", role: .cancel) {}
            Button("Remove", role: .destructive) {
                if let device = deviceToRemove {
                    removeDevice(device)
                }
            }
        } message: {
            if deviceToRemove != nil {
                Text(
                    "Are you sure you want to remove this device? This will free up a device slot for your license."
                )
            }
        }
    }

    // MARK: - Device Row View

    private func deviceRow(device: LicenseAPIService.DeviceInfo, canRemove: Bool) -> some View {
        // Debug logging for remove button logic
        let shouldShowRemoveButton = canRemove && (!device.isActive || isMaxDevicesReachedContext)
        print(
            "🔍 REMOVE_BTN_DEBUG: Device '\(device.deviceType ?? "Unknown")' - canRemove: \(canRemove), isActive: \(device.isActive), maxDevicesContext: \(isMaxDevicesReachedContext), shouldShow: \(shouldShowRemoveButton)"
        )

        return HStack(spacing: SnapbackTheme.Padding.standard) {
            // Device icon
            Image.systemCompat(deviceIcon(for: device))
                .foregroundColor(device.isActive ? .green : SnapbackTheme.Text.secondary)
                .frame(width: 20, height: 20)

            VStack(alignment: .leading, spacing: SnapbackTheme.Padding.small) {
                // Device information
                HStack {
                    VStack(alignment: .leading, spacing: 2) {
                        // Main device info line: "MacBook Pro 14-Inch, Nov 2024 • 24 GB"
                        HStack(spacing: SnapbackTheme.Padding.small) {
                            if let deviceType = device.deviceType, !deviceType.isEmpty {
                                // Convert multi-line device type to single line
                                let singleLineDeviceType = deviceType.replacingOccurrences(
                                    of: "\n", with: " ")
                                Text(singleLineDeviceType)
                                    .font(
                                        .system(size: SnapbackTheme.FontSize.body, weight: .medium)
                                    )
                                    .foregroundColor(.primary)
                            }

                            if let totalMemory = device.totalMemory, !totalMemory.isEmpty {
                                Text("•")
                                    .font(
                                        .system(size: SnapbackTheme.FontSize.body, weight: .medium)
                                    )
                                    .foregroundColor(.primary)

                                Text(totalMemory)
                                    .font(
                                        .system(size: SnapbackTheme.FontSize.body, weight: .medium)
                                    )
                                    .foregroundColor(.primary)
                            }
                        }

                        // Second line: Last seen info
                        Text("Last seen: \(formatDate(device.lastSeen))")
                            .snapbackCaptionStyle()
                    }

                    Spacer()

                    // Always show remove button for all devices
                    let shouldShowRemoveButton = canRemove

                    if shouldShowRemoveButton {
                        Button("Remove") {
                            deviceToRemove = device
                            showingRemoveConfirmation = true
                        }
                        .buttonStyle(.bordered)
                        .foregroundColor(.red)
                        .disabled(isRemovingDevice)
                    }
                }
            }
        }
        .snapbackRowStyle()
    }

    // MARK: - Helper Methods

    private func loadLicenseStatus() {
        guard !licenseManager.licenseKey.isEmpty else {
            statusError = "No license key available"
            return
        }

        let startTime = Date()
        print("🔍 DEVICE_LOAD_TIMING: loadLicenseStatus() started at \(startTime)")

        // Debug API configuration
        let config = LicenseAPIService.shared.getConfigurationSummary()
        print("🔍 API_CONFIG: \(config)")

        isLoadingStatus = true
        statusError = nil

        Task {
            print("🔍 DEVICE_LOAD_TIMING: Task started")
            do {
                let cleanedKey = licenseManager.licenseKey.replacingOccurrences(of: "-", with: "")
                print("🔍 DEVICE_LOAD_TIMING: License key cleaned: \(cleanedKey.prefix(8))...")

                // Skip metadata update for faster device loading - it's not required for status
                // Only update metadata if we have a device token and want to ensure latest info
                let config = LicenseAPIConfiguration.shared
                let hasToken = config.getDeviceToken() != nil
                print("🔍 DEVICE_LOAD_TIMING: Has device token: \(hasToken)")

                if hasToken {
                    print(
                        "🔍 DEVICE_LOAD_TIMING: Skipping updateDeviceMetadata() for faster loading")
                    // Optionally update metadata in background without blocking the UI
                    Task.detached {
                        do {
                            let metadataStartTime = Date()
                            let _ = try await LicenseAPIService.shared.updateDeviceMetadata()
                            let metadataDuration = Date().timeIntervalSince(metadataStartTime)
                            print(
                                "🔍 DEVICE_LOAD_TIMING: Background updateDeviceMetadata() completed in \(String(format: "%.3f", metadataDuration))s"
                            )
                        } catch {
                            print(
                                "🔍 DEVICE_LOAD_TIMING: Background updateDeviceMetadata() failed - \(error)"
                            )
                        }
                    }
                } else {
                    print("🔍 DEVICE_LOAD_TIMING: No device token - skipping metadata update")
                }

                let statusStartTime = Date()
                print("🔍 DEVICE_LOAD_TIMING: Starting getLicenseStatus() at \(statusStartTime)")

                let status = try await LicenseAPIService.shared.getLicenseStatus(
                    licenseKey: cleanedKey)

                let statusEndTime = Date()
                let statusDuration = statusEndTime.timeIntervalSince(statusStartTime)
                print(
                    "🔍 DEVICE_LOAD_TIMING: getLicenseStatus() completed in \(String(format: "%.3f", statusDuration))s"
                )

                await MainActor.run {
                    let totalEndTime = Date()
                    let totalDuration = totalEndTime.timeIntervalSince(startTime)
                    print(
                        "🔍 DEVICE_LOAD_TIMING: Total loadLicenseStatus() completed in \(String(format: "%.3f", totalDuration))s"
                    )

                    self.licenseStatus = status
                    self.isLoadingStatus = false
                }
            } catch {
                await MainActor.run {
                    let totalEndTime = Date()
                    let totalDuration = totalEndTime.timeIntervalSince(startTime)
                    print(
                        "🔍 DEVICE_LOAD_TIMING: Total loadLicenseStatus() failed in \(String(format: "%.3f", totalDuration))s - \(error)"
                    )

                    self.statusError = error.localizedDescription
                    self.isLoadingStatus = false
                }
            }
        }
    }

    /// Update device metadata to ensure it's available on the server
    private func updateDeviceMetadata() async throws {
        print("🔍 DEVICE_LOAD_TIMING: updateDeviceMetadata() wrapper called")
        // Use the dedicated device metadata update endpoint
        let _ = try await LicenseAPIService.shared.updateDeviceMetadata()
        print("🔍 DEVICE_LOAD_TIMING: updateDeviceMetadata() wrapper completed")
    }

    private func removeDevice(_ device: LicenseAPIService.DeviceInfo) {
        isRemovingDevice = true

        Task {
            do {
                let success = try await LicenseAPIService.shared.removeDevice(deviceId: device.id)

                await MainActor.run {
                    if success {
                        // Reload the license status to reflect the change
                        loadLicenseStatus()
                    }
                    self.isRemovingDevice = false
                    self.deviceToRemove = nil
                }
            } catch {
                await MainActor.run {
                    self.statusError = "Failed to remove device: \(error.localizedDescription)"
                    self.isRemovingDevice = false
                    self.deviceToRemove = nil
                }
            }
        }
    }

    private func deviceIcon(for device: LicenseAPIService.DeviceInfo) -> String {
        // Use device type to determine appropriate icon
        let baseIcon: String

        if let deviceType = device.deviceType?.lowercased() {
            switch deviceType {
            case let type where type.contains("macbook"):
                baseIcon = "laptopcomputer"
            case let type where type.contains("imac"):
                baseIcon = "desktopcomputer"
            case let type where type.contains("mac mini"):
                baseIcon = "macmini"
            case let type where type.contains("mac pro"):
                baseIcon = "macpro.gen3"
            case let type where type.contains("mac studio"):
                baseIcon = "macstudio"
            default:
                baseIcon = "desktopcomputer"
            }
        } else {
            baseIcon = "desktopcomputer"
        }

        // Add active indicator for current device
        if device.isActive {
            return baseIcon == "laptopcomputer"
                ? "laptopcomputer.and.arrow.down" : "desktopcomputer.and.arrow.down"
        } else {
            return baseIcon
        }
    }

    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
}

#Preview {
    DeviceManagementView()
        .frame(width: 600, height: 400)
        .background(SnapbackTheme.Background.window)
}
