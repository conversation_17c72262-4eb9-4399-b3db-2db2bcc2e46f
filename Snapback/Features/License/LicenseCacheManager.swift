// Import CommonCrypto for SHA256
import CommonCrypto
import Foundation
import Security

/// Manages secure caching and offline validation of license data
/// Implements industry best practices for license validation with graceful degradation
class LicenseCacheManager {
    static let shared = LicenseCacheManager()

    private let logger = LoggingService.shared
    private let serviceName = "LicenseCacheManager"

    // MARK: - Cache Configuration

    /// How long a cached license is considered fresh (no server check needed)
    private let freshCacheDuration: TimeInterval = 24 * 60 * 60  // 24 hours

    /// How long a cached license can be used when server is unavailable
    private let gracePeriodDuration: TimeInterval = 7 * 24 * 60 * 60  // 7 days

    /// Maximum time to wait for server validation before falling back to cache
    private let serverTimeoutDuration: TimeInterval = 10.0  // 10 seconds

    // MARK: - Cache Keys

    private enum CacheKeys {
        static let cachedLicenseData = "SnapbackCachedLicenseData"
        static let lastSuccessfulValidation = "SnapbackLastSuccessfulValidation"
        static let lastServerContact = "SnapbackLastServerContact"
        static let validationAttemptCount = "SnapbackValidationAttemptCount"
        static let gracePeriodStartDate = "SnapbackGracePeriodStartDate"
        static let registeredUserEmail = "SnapbackRegisteredUserEmail"  // Backup email storage
        static let registeredUserName = "SnapbackRegisteredUserName"  // Backup name storage
    }

    // MARK: - Cached License Data Structure

    struct CachedLicenseData: Codable {
        let licenseKey: String
        let licenseInfo: LicenseInfo
        let licenseStatus: LicenseStatus
        let validationDate: Date
        let serverResponse: LicenseAPIService.LicenseValidationResponse?
        let checksum: String  // For integrity verification

        init(
            licenseKey: String, licenseInfo: LicenseInfo, licenseStatus: LicenseStatus,
            validationDate: Date, serverResponse: LicenseAPIService.LicenseValidationResponse?
        ) {
            self.licenseKey = licenseKey
            self.licenseInfo = licenseInfo
            self.licenseStatus = licenseStatus
            self.validationDate = validationDate
            self.serverResponse = serverResponse
            self.checksum = Self.calculateChecksum(
                licenseKey: licenseKey,
                licenseInfo: licenseInfo,
                validationDate: validationDate)
        }

        /// Calculate integrity checksum for cached data
        private static func calculateChecksum(
            licenseKey: String, licenseInfo: LicenseInfo,
            validationDate: Date
        ) -> String {
            let data =
                "\(licenseKey)\(licenseInfo.licenseType)\(validationDate.timeIntervalSince1970)"
            return data.sha256
        }

        /// Verify data integrity
        var isIntegrityValid: Bool {
            let expectedChecksum = Self.calculateChecksum(
                licenseKey: licenseKey,
                licenseInfo: licenseInfo,
                validationDate: validationDate)
            return checksum == expectedChecksum
        }
    }

    // MARK: - Validation Result Types

    enum ValidationStrategy {
        case serverOnly  // Force server validation
        case cacheFirst  // Use cache if fresh, otherwise server
        case offlineOnly  // Use cache only (for testing)
        case gracefulDegradation  // Smart fallback strategy
    }

    struct ValidationResult {
        let licenseStatus: LicenseStatus
        let licenseInfo: LicenseInfo?
        let source: ValidationSource
        let isGracePeriod: Bool
        let remainingGraceDays: Int?
        let shouldRetryLater: Bool
        let errorMessage: String?

        enum ValidationSource {
            case server
            case freshCache
            case gracePeriodCache
            case expired
        }
    }

    private init() {
        logger.info("LicenseCacheManager initialized", service: serviceName)
    }

    // MARK: - Public Interface

    /// Validate license using multi-tier strategy
    func validateLicense(
        licenseKey: String,
        strategy: ValidationStrategy = .gracefulDegradation,
        forceServerCheck: Bool = false
    ) async -> ValidationResult {

        logger.info("🔍 Starting license validation - strategy: \(strategy)", service: serviceName)

        // Check if license system is disabled
        if FeatureFlags.isLicenseSystemDisabled {
            return ValidationResult(
                licenseStatus: .valid,
                licenseInfo: createPlaceholderLicenseInfo(),
                source: .server,
                isGracePeriod: false,
                remainingGraceDays: nil,
                shouldRetryLater: false,
                errorMessage: nil
            )
        }

        switch strategy {
        case .serverOnly:
            return await validateWithServer(licenseKey: licenseKey)

        case .cacheFirst:
            return await validateCacheFirst(licenseKey: licenseKey)

        case .offlineOnly:
            return validateOfflineOnly(licenseKey: licenseKey)

        case .gracefulDegradation:
            return await validateWithGracefulDegradation(
                licenseKey: licenseKey, forceServerCheck: forceServerCheck)
        }
    }

    /// Cache successful license validation
    func cacheLicenseValidation(
        licenseKey: String,
        licenseInfo: LicenseInfo,
        licenseStatus: LicenseStatus,
        serverResponse: LicenseAPIService.LicenseValidationResponse?
    ) {
        let cachedData = CachedLicenseData(
            licenseKey: licenseKey,
            licenseInfo: licenseInfo,
            licenseStatus: licenseStatus,
            validationDate: Date(),
            serverResponse: serverResponse
        )

        do {
            let data = try JSONEncoder().encode(cachedData)
            UserDefaults.standard.set(data, forKey: CacheKeys.cachedLicenseData)
            UserDefaults.standard.set(
                Date().timeIntervalSince1970, forKey: CacheKeys.lastSuccessfulValidation)
            UserDefaults.standard.set(
                Date().timeIntervalSince1970, forKey: CacheKeys.lastServerContact)
            UserDefaults.standard.set(0, forKey: CacheKeys.validationAttemptCount)  // Reset attempt count
            UserDefaults.standard.removeObject(forKey: CacheKeys.gracePeriodStartDate)  // Clear grace period

            // Store email and name separately as backup for UI consistency
            if !licenseInfo.email.isEmpty {
                UserDefaults.standard.set(licenseInfo.email, forKey: CacheKeys.registeredUserEmail)
                logger.debug("📧 Stored backup email: \(licenseInfo.email)", service: serviceName)
            }
            if !licenseInfo.registeredUser.isEmpty {
                UserDefaults.standard.set(
                    licenseInfo.registeredUser, forKey: CacheKeys.registeredUserName)
                logger.debug(
                    "👤 Stored backup registered user: \(licenseInfo.registeredUser)",
                    service: serviceName)
            }

            UserDefaults.standard.synchronize()

            logger.info(
                "✅ License validation cached successfully with backup user info",
                service: serviceName)
        } catch {
            logger.error("❌ Failed to cache license validation: \(error)", service: serviceName)
        }
    }

    /// Clear all cached license data
    func clearCache() {
        let keys = [
            CacheKeys.cachedLicenseData,
            CacheKeys.lastSuccessfulValidation,
            CacheKeys.lastServerContact,
            CacheKeys.validationAttemptCount,
            CacheKeys.gracePeriodStartDate,
            CacheKeys.registeredUserEmail,
            CacheKeys.registeredUserName,
        ]

        for key in keys {
            UserDefaults.standard.removeObject(forKey: key)
        }
        UserDefaults.standard.synchronize()

        logger.info("🗑️ License cache cleared", service: serviceName)
    }

    /// Get cache status for debugging
    func getCacheStatus() -> [String: Any] {
        let cachedData = loadCachedLicenseData()
        let lastValidation = UserDefaults.standard.double(
            forKey: CacheKeys.lastSuccessfulValidation)
        let lastServerContact = UserDefaults.standard.double(forKey: CacheKeys.lastServerContact)
        let attemptCount = UserDefaults.standard.integer(forKey: CacheKeys.validationAttemptCount)

        return [
            "hasCachedData": cachedData != nil,
            "cacheAge": cachedData != nil
                ? Date().timeIntervalSince(cachedData!.validationDate) : 0,
            "lastValidation": lastValidation > 0
                ? Date(timeIntervalSince1970: lastValidation) : "Never",
            "lastServerContact": lastServerContact > 0
                ? Date(timeIntervalSince1970: lastServerContact) : "Never",
            "attemptCount": attemptCount,
            "isInGracePeriod": isInGracePeriod(),
            "backupEmail": getBackupEmail() ?? "None",
            "backupRegisteredUser": getBackupRegisteredUser() ?? "None",
        ]
    }
}

// MARK: - String Extension for Checksum
extension String {
    var sha256: String {
        let data = Data(self.utf8)
        let hash = data.withUnsafeBytes { bytes in
            var hash = [UInt8](repeating: 0, count: Int(CC_SHA256_DIGEST_LENGTH))
            CC_SHA256(bytes.bindMemory(to: UInt8.self).baseAddress, CC_LONG(data.count), &hash)
            return hash
        }
        return hash.map { String(format: "%02x", $0) }.joined()
    }
}

// MARK: - Private Validation Methods
extension LicenseCacheManager {

    /// Validate with server only (no cache fallback)
    private func validateWithServer(licenseKey: String) async -> ValidationResult {
        logger.info("🌐 Attempting server-only validation", service: serviceName)

        do {
            let response = try await withTimeout(serverTimeoutDuration) {
                try await LicenseAPIService.shared.validateLicenseAndRegisterDevice(
                    licenseKey: licenseKey.replacingOccurrences(of: "-", with: ""),
                    includeDeviceMetadata: false
                )
            }

            if response.valid, let licenseInfo = createLicenseInfo(from: response) {
                // Cache successful validation
                cacheLicenseValidation(
                    licenseKey: licenseKey,
                    licenseInfo: licenseInfo,
                    licenseStatus: .valid,
                    serverResponse: response
                )

                return ValidationResult(
                    licenseStatus: .valid,
                    licenseInfo: licenseInfo,
                    source: .server,
                    isGracePeriod: false,
                    remainingGraceDays: nil,
                    shouldRetryLater: false,
                    errorMessage: nil
                )
            } else {
                return ValidationResult(
                    licenseStatus: .invalid,
                    licenseInfo: nil,
                    source: .server,
                    isGracePeriod: false,
                    remainingGraceDays: nil,
                    shouldRetryLater: false,
                    errorMessage: response.message ?? "Invalid license"
                )
            }
        } catch {
            logger.warning("🌐 Server validation failed: \(error)", service: serviceName)
            incrementValidationAttemptCount()

            return ValidationResult(
                licenseStatus: .invalid,
                licenseInfo: nil,
                source: .server,
                isGracePeriod: false,
                remainingGraceDays: nil,
                shouldRetryLater: true,
                errorMessage: "Server unavailable: \(error.localizedDescription)"
            )
        }
    }

    /// Validate cache first, fallback to server if needed
    private func validateCacheFirst(licenseKey: String) async -> ValidationResult {
        logger.info("💾 Attempting cache-first validation", service: serviceName)

        // Check if we have fresh cached data
        if let cachedData = loadCachedLicenseData(),
            cachedData.licenseKey == licenseKey,
            cachedData.isIntegrityValid,
            isCacheFresh(cachedData.validationDate)
        {

            logger.info("✅ Using fresh cached license data", service: serviceName)
            return ValidationResult(
                licenseStatus: cachedData.licenseStatus,
                licenseInfo: cachedData.licenseInfo,
                source: .freshCache,
                isGracePeriod: false,
                remainingGraceDays: nil,
                shouldRetryLater: false,
                errorMessage: nil
            )
        }

        // Cache is stale, try server validation
        return await validateWithServer(licenseKey: licenseKey)
    }

    /// Validate using offline cache only
    private func validateOfflineOnly(licenseKey: String) -> ValidationResult {
        logger.info("📱 Attempting offline-only validation", service: serviceName)

        guard let cachedData = loadCachedLicenseData(),
            cachedData.licenseKey == licenseKey,
            cachedData.isIntegrityValid
        else {

            return ValidationResult(
                licenseStatus: .unlicensed,
                licenseInfo: nil,
                source: .expired,
                isGracePeriod: false,
                remainingGraceDays: nil,
                shouldRetryLater: true,
                errorMessage: "No valid cached license data"
            )
        }

        let isInGrace = isInGracePeriod()
        let remainingDays = calculateRemainingGraceDays()

        if isCacheFresh(cachedData.validationDate) || isInGrace {
            let source: ValidationResult.ValidationSource =
                isCacheFresh(cachedData.validationDate) ? .freshCache : .gracePeriodCache

            return ValidationResult(
                licenseStatus: cachedData.licenseStatus,
                licenseInfo: cachedData.licenseInfo,
                source: source,
                isGracePeriod: isInGrace,
                remainingGraceDays: remainingDays,
                shouldRetryLater: true,
                errorMessage: isInGrace ? "Using cached license (server unavailable)" : nil
            )
        } else {
            return ValidationResult(
                licenseStatus: .expired,
                licenseInfo: nil,
                source: .expired,
                isGracePeriod: false,
                remainingGraceDays: 0,
                shouldRetryLater: true,
                errorMessage: "Cached license expired and server unavailable"
            )
        }
    }

    /// Graceful degradation strategy - the recommended approach
    private func validateWithGracefulDegradation(licenseKey: String, forceServerCheck: Bool) async
        -> ValidationResult
    {
        logger.info("🎯 Attempting graceful degradation validation", service: serviceName)

        // Load cached data first
        let cachedData = loadCachedLicenseData()
        let hasFreshCache =
            cachedData != nil && cachedData!.licenseKey == licenseKey
            && cachedData!.isIntegrityValid && isCacheFresh(cachedData!.validationDate)

        // If we have fresh cache and not forcing server check, use it
        if hasFreshCache && !forceServerCheck {
            logger.info("✅ Using fresh cached data (graceful degradation)", service: serviceName)
            return ValidationResult(
                licenseStatus: cachedData!.licenseStatus,
                licenseInfo: cachedData!.licenseInfo,
                source: .freshCache,
                isGracePeriod: false,
                remainingGraceDays: nil,
                shouldRetryLater: false,
                errorMessage: nil
            )
        }

        // Try server validation with timeout
        logger.info("🌐 Attempting server validation with fallback", service: serviceName)

        do {
            let response = try await withTimeout(serverTimeoutDuration) {
                try await LicenseAPIService.shared.validateLicenseAndRegisterDevice(
                    licenseKey: licenseKey.replacingOccurrences(of: "-", with: ""),
                    includeDeviceMetadata: forceServerCheck
                )
            }

            if response.valid, let licenseInfo = createLicenseInfo(from: response) {
                // Cache successful validation
                cacheLicenseValidation(
                    licenseKey: licenseKey,
                    licenseInfo: licenseInfo,
                    licenseStatus: .valid,
                    serverResponse: response
                )

                logger.info("✅ Server validation successful", service: serviceName)
                return ValidationResult(
                    licenseStatus: .valid,
                    licenseInfo: licenseInfo,
                    source: .server,
                    isGracePeriod: false,
                    remainingGraceDays: nil,
                    shouldRetryLater: false,
                    errorMessage: nil
                )
            } else {
                // Server says license is invalid - don't use cache
                logger.warning(
                    "❌ Server validation failed: \(response.message ?? "invalid")",
                    service: serviceName)
                return ValidationResult(
                    licenseStatus: .invalid,
                    licenseInfo: nil,
                    source: .server,
                    isGracePeriod: false,
                    remainingGraceDays: nil,
                    shouldRetryLater: false,
                    errorMessage: response.message ?? "Invalid license"
                )
            }
        } catch {
            // Server is unavailable - fall back to cached data with grace period
            logger.warning(
                "🌐 Server unavailable, falling back to cache: \(error)", service: serviceName)
            incrementValidationAttemptCount()

            return handleServerUnavailableFallback(licenseKey: licenseKey, cachedData: cachedData)
        }
    }

    /// Handle fallback when server is unavailable
    private func handleServerUnavailableFallback(licenseKey: String, cachedData: CachedLicenseData?)
        -> ValidationResult
    {
        guard let cachedData = cachedData,
            cachedData.licenseKey == licenseKey,
            cachedData.isIntegrityValid
        else {

            logger.warning("❌ No valid cached data available for fallback", service: serviceName)
            return ValidationResult(
                licenseStatus: .unlicensed,
                licenseInfo: nil,
                source: .expired,
                isGracePeriod: false,
                remainingGraceDays: nil,
                shouldRetryLater: true,
                errorMessage: "Server unavailable and no cached license data"
            )
        }

        // Start grace period if not already started
        if !isInGracePeriod() {
            startGracePeriod()
        }

        let remainingDays = calculateRemainingGraceDays()

        if remainingDays > 0 {
            logger.info(
                "⏰ Using cached license in grace period (\(remainingDays) days remaining)",
                service: serviceName)
            return ValidationResult(
                licenseStatus: cachedData.licenseStatus,
                licenseInfo: cachedData.licenseInfo,
                source: .gracePeriodCache,
                isGracePeriod: true,
                remainingGraceDays: remainingDays,
                shouldRetryLater: true,
                errorMessage:
                    "Server unavailable - using cached license (\(remainingDays) days remaining)"
            )
        } else {
            logger.warning("⏰ Grace period expired", service: serviceName)
            return ValidationResult(
                licenseStatus: .expired,
                licenseInfo: nil,
                source: .expired,
                isGracePeriod: false,
                remainingGraceDays: 0,
                shouldRetryLater: true,
                errorMessage: "Server unavailable and grace period expired"
            )
        }
    }

    // MARK: - Helper Methods

    /// Load cached license data from UserDefaults
    private func loadCachedLicenseData() -> CachedLicenseData? {
        guard let data = UserDefaults.standard.data(forKey: CacheKeys.cachedLicenseData) else {
            return nil
        }

        do {
            let cachedData = try JSONDecoder().decode(CachedLicenseData.self, from: data)
            return cachedData.isIntegrityValid ? cachedData : nil
        } catch {
            logger.warning("Failed to decode cached license data: \(error)", service: serviceName)
            return nil
        }
    }

    /// Check if cached data is still fresh (within freshCacheDuration)
    private func isCacheFresh(_ validationDate: Date) -> Bool {
        return Date().timeIntervalSince(validationDate) < freshCacheDuration
    }

    /// Check if we're currently in grace period
    private func isInGracePeriod() -> Bool {
        guard
            let gracePeriodStart = UserDefaults.standard.object(
                forKey: CacheKeys.gracePeriodStartDate) as? Date
        else {
            return false
        }

        return Date().timeIntervalSince(gracePeriodStart) < gracePeriodDuration
    }

    /// Start grace period
    private func startGracePeriod() {
        UserDefaults.standard.set(Date(), forKey: CacheKeys.gracePeriodStartDate)
        UserDefaults.standard.synchronize()
        logger.info("⏰ Grace period started", service: serviceName)
    }

    /// Calculate remaining grace period days
    private func calculateRemainingGraceDays() -> Int {
        guard
            let gracePeriodStart = UserDefaults.standard.object(
                forKey: CacheKeys.gracePeriodStartDate) as? Date
        else {
            return 0
        }

        let elapsed = Date().timeIntervalSince(gracePeriodStart)
        let remaining = gracePeriodDuration - elapsed
        return max(0, Int(remaining / (24 * 60 * 60)))
    }

    /// Increment validation attempt count
    private func incrementValidationAttemptCount() {
        let currentCount = UserDefaults.standard.integer(forKey: CacheKeys.validationAttemptCount)
        UserDefaults.standard.set(currentCount + 1, forKey: CacheKeys.validationAttemptCount)
        UserDefaults.standard.synchronize()
    }

    /// Create LicenseInfo from server response with backup email/name fallback
    private func createLicenseInfo(from response: LicenseAPIService.LicenseValidationResponse)
        -> LicenseInfo?
    {
        // Use server response first, then fall back to cached backup values
        let email = response.effectiveEmail ?? getBackupEmail() ?? ""
        let registeredUser = !email.isEmpty ? email : (getBackupRegisteredUser() ?? "Licensed User")

        return LicenseInfo(
            licenseType: response.effectiveLicenseType ?? "Pro",
            registeredUser: registeredUser,
            email: email,
            expirationDate: response.effectiveExpiresAt,
            features: [
                "All Workspace Features",
                "Unlimited Workspaces",
                "Window Management",
                "Keyboard Shortcuts",
                "CloudKit Sync",
            ]
        )
    }

    /// Get backup email from UserDefaults (for UI consistency when cache is unavailable)
    func getBackupEmail() -> String? {
        return UserDefaults.standard.string(forKey: CacheKeys.registeredUserEmail)
    }

    /// Get backup registered user name from UserDefaults
    func getBackupRegisteredUser() -> String? {
        return UserDefaults.standard.string(forKey: CacheKeys.registeredUserName)
    }

    /// Create LicenseInfo with backup data when server response is unavailable
    func createLicenseInfoFromBackup(licenseType: String = "Standard", expirationDate: Date? = nil)
        -> LicenseInfo?
    {
        guard let email = getBackupEmail() else {
            return nil  // No backup data available
        }

        let registeredUser = getBackupRegisteredUser() ?? email

        logger.debug("📧 Creating license info from backup data: \(email)", service: serviceName)

        return LicenseInfo(
            licenseType: licenseType,
            registeredUser: registeredUser,
            email: email,
            expirationDate: expirationDate,
            features: [
                "All Workspace Features",
                "Unlimited Workspaces",
                "Window Management",
                "Keyboard Shortcuts",
                "CloudKit Sync",
            ]
        )
    }

    /// Create placeholder license info for disabled license system
    private func createPlaceholderLicenseInfo() -> LicenseInfo {
        return LicenseInfo(
            licenseType: "Full Access",
            registeredUser: "Licensed User",
            email: "",
            expirationDate: nil,
            features: [
                "All Workspace Features",
                "Unlimited Workspaces",
                "Window Management",
                "Keyboard Shortcuts",
                "CloudKit Sync",
            ]
        )
    }

    /// Timeout wrapper for async operations
    private func withTimeout<T>(_ timeout: TimeInterval, operation: @escaping () async throws -> T)
        async throws -> T
    {
        return try await withThrowingTaskGroup(of: T.self) { group in
            group.addTask {
                try await operation()
            }

            group.addTask {
                try await Task.sleep(nanoseconds: UInt64(timeout * 1_000_000_000))
                throw LicenseAPIError.networkError("Request timeout")
            }

            guard let result = try await group.next() else {
                throw LicenseAPIError.networkError("No result from timeout group")
            }

            group.cancelAll()
            return result
        }
    }
}
