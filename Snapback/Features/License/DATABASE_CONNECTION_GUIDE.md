# Database Connection Guide

## 🎯 Goal
Connect Snapback to your live API server so that license data gets saved to your database.

## ✅ Changes Made
1. **Production API integration** with development and production environments
2. **Fixed test files** to work with the singleton architecture
3. **Created test tools** to verify the connection

## 🚀 Steps to Connect to Live API

### 1. Make Sure Your API Server is Running
```bash
# Your API server should be running on:
http://localhost:3000

# Test manually:
curl http://localhost:3000/api/payments/pricing
```

### 2. Restart Snapback App
**Important:** The `LicenseAPIService` singleton initializes its network manager at startup. Environment changes require an app restart to take effect.

### 3. Add Test Code to Your App
Add this to your `AppDelegate.swift` in `applicationDidFinishLaunching`:

```swift
// Add this for testing
Task {
    await QuickAPITest.testConnection()
    await QuickAPITest.testTrialCreation()
}
```

### 4. Check Xcode Console
After restarting and running the test, you should see:

**✅ Success (Connected to Live API):**
```
🔍 Testing API Connection...
Environment: Development Server
Base URL: http://localhost:3000/api
Using Mock: false
✅ API Connection Successful!
🎉 Using LIVE API server!

🧪 Testing Trial Creation...
✅ Trial Created!
License Key: TRIAL-XXXX-XXXX-XXXX
Email: <EMAIL>
🎉 Data saved to your database!
```

**❌ Problem (Still Using Mock):**
```
🔍 Testing API Connection...
Environment: Development Server
Base URL: http://localhost:3000/api
Using Mock: true
⚠️  Using MOCK data
⚠️  MOCK data - NOT saved to database!
🔄 Restart app to connect to live API
```

## 🔧 Troubleshooting

### If Not Connecting to Live API:
1. **Restart the app** (most common issue)
2. Check that `LicenseAPIConfiguration.swift` line 44 shows `.development` for DEBUG builds
3. Verify no environment variables are overriding the setting

### If API Connection Fails:
1. **Check your API server** is running on `http://localhost:3000`
2. **Test manually:** `curl http://localhost:3000/api/payments/pricing`
3. **Check server logs** for incoming requests
4. **Verify CORS settings** if needed

### If Trial Creation Fails:
1. Check your API server's `/api/licenses/create` endpoint
2. Verify the request format matches your server expectations
3. Check server logs for error details

## 🧪 Advanced Testing

For more detailed testing, use the comprehensive test:

```swift
let connectionTest = LiveAPIConnectionTest()
Task {
    await connectionTest.runConnectionTest()
    await connectionTest.testTrialCreation()
}
```

## 📋 Expected Database Behavior

Once connected to the live API:
- **Trial creation** should save to your database
- **License validation** should query your database
- **Pricing requests** should return your configured pricing

## 🎉 Success Indicators

You'll know it's working when:
1. Console shows "🎉 Using LIVE API server!"
2. Console shows "🎉 Data saved to your database!"
3. You can see the data in your database
4. Your API server logs show incoming requests

## 🔄 Environment Switching

To switch between development and production environments:
1. Use environment variables or configuration overrides
2. Restart the app

## 📞 Need Help?

If you're still not seeing data in your database:
1. Run the test code and share the console output
2. Check your API server logs
3. Verify your database connection in the API server
