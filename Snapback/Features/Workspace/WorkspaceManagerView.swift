import Foundation
import KeyboardShortcuts
import SwiftUI

/// A view that displays a keyboard shortcut as a static tag
struct ShortcutTag: View {
    let shortcut: KeyboardShortcuts.Shortcut?

    var body: some View {
        if let shortcut = shortcut {
            Text(shortcut.description)
                .font(.system(.body, design: .monospaced))
                .tracking(1.2)  // Add letter spacing
                .padding(.horizontal, 8)  // Increased horizontal padding
                .padding(.vertical, 3)  // Increased vertical padding
                .background(Color(NSColor.windowBackgroundColor))
                .foregroundColor(SnapbackTheme.Text.primary)
                .cornerRadius(4)
        } else {
            Text("None")
                .font(.system(.body, design: .monospaced))
                .tracking(1.2)  // Add letter spacing
                .padding(.horizontal, 8)  // Increased horizontal padding
                .padding(.vertical, 3)  // Increased vertical padding
                .background(Color(NSColor.windowBackgroundColor))
                .foregroundColor(SnapbackTheme.Text.secondary)
                .cornerRadius(4)
        }
    }
}

struct WorkspaceManagerView: View {
    // Use the service's published property directly
    @EnvironmentObject var workspaceService: WorkspaceService

    // State for UI interaction
    @State private var selectedWorkspaceID: Workspace.ID?  // Select by ID
    @State private var refreshTrigger = UUID()
    @State private var showingDeleteAlert = false
    @State private var showProgress = false
    @State private var toastMessage: String?
    @State private var toastType: ToastType = .info

    // Reference to the AppDelegate to access the WindowManager
    private var appDelegate: AppDelegate? {
        NSApp.delegate as? AppDelegate
    }

    // Computed property to get the selected workspace object from the service
    private var selectedWorkspace: Workspace? {
        guard let selectedID = selectedWorkspaceID else { return nil }
        return workspaceService.workspaces.first { $0.id == selectedID }
    }

    enum ToastType { case success, error, info }

    // MARK: - Body
    var body: some View {
        Group {
            if #available(macOS 13.0, *) {
                NavigationStack {
                    mainContentView
                        .navigationTitle("Workspaces")
                        .alert(
                            "Delete Workspace", isPresented: $showingDeleteAlert,
                            presenting: selectedWorkspaceID
                        ) { id in
                            deleteAlertButtons(id: id)
                        } message: { id in
                            deleteAlertMessage(id: id)
                        }
                }
            } else {
                NavigationView {
                    mainContentView
                        .navigationTitle("Workspaces")
                        .alert(
                            "Delete Workspace", isPresented: $showingDeleteAlert,
                            presenting: selectedWorkspaceID
                        ) { id in
                            deleteAlertButtons(id: id)
                        } message: { id in
                            deleteAlertMessage(id: id)
                        }
                }
            }
        }
        .overlay(progressAndToastOverlay)
        .id(refreshTrigger)
        .onReceive(
            NotificationCenter.default.publisher(
                for: DisplayChangeMonitorService.displayConfigurationChanged)
        ) { _ in
            // Refresh UI when display configuration changes (using existing DisplayChangeMonitorService notification)
            refreshTrigger = UUID()
        }
    }

    // MARK: - Computed View Properties
    @ViewBuilder
    private var mainContentView: some View {
        if workspaceService.workspaces.isEmpty {
            UniversalContentUnavailableView(
                "No Workspaces Saved",
                systemImage: "Window",
                dynamicDescription: {
                    let shortcutText =
                        KeyboardShortcuts.getShortcut(for: .saveWorkspace)?.description
                        ?? "Ctrl+Option+S"
                    return Text(
                        "Save your current window layout using \(shortcutText) or the menu bar.")
                }
            )
        } else {
            workspaceListView
        }
    }

    private var workspaceListView: some View {
        VStack(spacing: 0) {
            // FREEMIUM MODEL: Show workspace count indicator
            workspaceCountHeader

            ScrollView {
                LazyVStack(spacing: SnapbackTheme.Padding.standard) {
                    ForEach(filteredWorkspaces) { workspace in
                        WorkspaceCard(  // Pass data to the card
                            workspace: workspace,
                            onDelete: { deleteWorkspaceWithConfirmation(workspace: workspace) },
                            onEdit: {
                                print(
                                    "[WorkspaceManagerView] Edit button clicked for workspace: \(workspace.name)"
                                )
                                // Use the direct method to open the edit window
                                openEditWindow(for: workspace)
                            }
                        )
                        .environmentObject(workspaceService)
                    }
                }
                .padding()
            }

            // Import/Export buttons at the bottom using SettingsFooter for consistency
            SettingsFooter {
                HStack(spacing: SnapbackTheme.Padding.standard) {
                    Spacer()

                    Button("Import") {
                        importWorkspaces()
                    }
                    .keyboardShortcut(.cancelAction)
                    .padding(.vertical, SnapbackTheme.Padding.vertical)
                    .padding(.horizontal, SnapbackTheme.Padding.horizontal)

                    Button("Export") {
                        exportWorkspaces()
                    }
                    .keyboardShortcut(.defaultAction)
                    .padding(.vertical, SnapbackTheme.Padding.vertical)
                    .padding(.horizontal, SnapbackTheme.Padding.horizontal)
                }
            }
        }
    }

    @ViewBuilder
    private var progressAndToastOverlay: some View {
        Group {
            if showProgress {
                ProgressView()
                    .scaleEffect(1.5)
                    .padding()
                    .background(.ultraThinMaterial)
                    .cornerRadius(SnapbackTheme.CornerRadius.large)
            }
            if let message = toastMessage {
                VStack {
                    Spacer()
                    ToastView(message: message, type: toastType)
                }
                .padding(.bottom, SnapbackTheme.Padding.extraLarge)
                .transition(.move(edge: .bottom))
                .animation(SnapbackTheme.Animation.standard, value: toastMessage)
            }
        }
    }

    // MARK: - Alert Helper Functions
    @ViewBuilder
    private func deleteAlertButtons(id: Workspace.ID?) -> some View {
        Button("Delete", role: .destructive) { confirmDelete(id: id) }
        Button("Cancel", role: .cancel) {}
    }

    private func deleteAlertMessage(id: Workspace.ID?) -> some View {
        let name = workspaceService.workspaces.first { $0.id == id }?.name ?? "this workspace"
        return Text("Are you sure you want to delete '\(name)'?")
    }

    // MARK: - Computed Properties

    /// Get filtered workspaces based on license status
    private var filteredWorkspaces: [Workspace] {
        let allWorkspaces = workspaceService.workspaces
        let licenseManager = LicenseManager.shared

        // If license system is disabled or user has unlimited workspaces, show all
        if !licenseManager.isLicenseSystemEnabled || licenseManager.hasUnlimitedWorkspaces {
            return allWorkspaces
        }

        // For free users, limit to maxFreeWorkspaces
        let maxCount = LicenseManager.maxFreeWorkspaces
        return Array(allWorkspaces.prefix(maxCount))
    }

    // MARK: - Helper Methods

    /// Open external purchase URL for license upgrade
    private func openPurchaseURL() {
        // Use centralized website configuration for purchase URL
        WebsiteConfiguration.shared.openPurchasePage()
    }

    // MARK: - Freemium UI Components

    /// Shows workspace count and limits for free users
    @ViewBuilder
    private var workspaceCountHeader: some View {
        let licenseManager = LicenseManager.shared

        if licenseManager.isLicenseSystemEnabled && !licenseManager.hasUnlimitedWorkspaces {
            let currentCount = workspaceService.workspaces.count
            let maxCount = LicenseManager.maxFreeWorkspaces
            let isAtLimit = currentCount >= maxCount

            VStack(spacing: 8) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Workspace Usage")
                            .font(.system(size: SnapbackTheme.FontSize.body, weight: .semibold))

                        HStack {
                            Text("\(currentCount) of \(maxCount) workspaces used")
                                .font(.system(size: SnapbackTheme.FontSize.body))
                                .foregroundColor(isAtLimit ? .orange : .primary)

                            if isAtLimit {
                                Image.systemCompat("exclamationmark.triangle.fill")
                                    .foregroundColor(.orange)
                                    .font(.caption)
                            }
                        }
                    }

                    Spacer()

                    Button("Upgrade for Unlimited") {
                        openPurchaseURL()
                    }
                    .buttonStyle(.borderedProminent)
                }

                if isAtLimit {
                    Text(
                        "You've reached the free workspace limit. Delete a workspace or upgrade to save more."
                    )
                    .font(.system(size: SnapbackTheme.FontSize.caption))
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .leading)
                }
            }
            .padding(SnapbackTheme.Padding.standard)
            .background(
                RoundedRectangle(cornerRadius: SnapbackTheme.CornerRadius.card)
                    .fill(isAtLimit ? Color.orange.opacity(0.1) : Color.blue.opacity(0.1))
            )
            .padding(.horizontal, SnapbackTheme.Padding.standard)
            .padding(.top, SnapbackTheme.Padding.small)
        }
    }

    // MARK: - Action Functions
    func exportWorkspaces() {
        showProgress = true
        let savePanel = NSSavePanel()
        savePanel.nameFieldStringValue = "Snapback_Workspaces.json"
        savePanel.allowedContentTypes = [.json]
        if savePanel.runModal() == .OK, let url = savePanel.url {
            do {
                let data = try JSONEncoder().encode(workspaceService.workspaces)
                try data.write(to: url)
                showToast(message: "Workspaces exported successfully!", type: .success)
            } catch {
                showToast(message: "Export failed: \(error.localizedDescription)", type: .error)
            }
        }
        showProgress = false
    }

    func importWorkspaces() {
        showProgress = true
        let openPanel = NSOpenPanel()
        openPanel.allowedContentTypes = [.json]
        openPanel.allowsMultipleSelection = false
        if openPanel.runModal() == .OK, let url = openPanel.url {
            do {
                let data = try Data(contentsOf: url)
                let importedWorkspaces = try JSONDecoder().decode([Workspace].self, from: data)
                for workspace in importedWorkspaces { workspaceService.addWorkspace(workspace) }
                showToast(
                    message: "\(importedWorkspaces.count) workspaces imported successfully!",
                    type: .success)
            } catch {
                showToast(message: "Import failed: \(error.localizedDescription)", type: .error)
            }
        }
        showProgress = false
    }

    private func showToast(message: String, type: ToastType) {
        toastMessage = message
        toastType = type
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) { toastMessage = nil }
    }

    private func deleteWorkspaceWithConfirmation(workspace: Workspace) {
        selectedWorkspaceID = workspace.id
        showingDeleteAlert = true
    }

    func confirmDelete(id: Workspace.ID?) {
        guard let idToDelete = id else { return }
        workspaceService.deleteWorkspace(id: idToDelete)
        selectedWorkspaceID = nil
    }

    func deleteWorkspace(at offsets: IndexSet) {
        let idsToDelete = offsets.map { workspaceService.workspaces[$0].id }
        for id in idsToDelete { workspaceService.deleteWorkspace(id: id) }
        selectedWorkspaceID = nil
    }

    // Direct method to open edit window
    func openEditWindow(for workspace: Workspace) {
        print("[WorkspaceManagerView] openEditWindow called for workspace: \(workspace.name)")

        // Create the window directly
        let editWorkspaceView = EditWorkspaceView(workspace: workspace)
            .environmentObject(workspaceService)
            .environmentObject(WindowDismissalManager.shared)

        let editWorkspaceWindow = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 600, height: 700),
            styleMask: [.titled, .closable, .miniaturizable, .resizable, .fullSizeContentView],
            backing: .buffered, defer: false
        )

        let hostingView = NSHostingView(rootView: editWorkspaceView)
        editWorkspaceWindow.contentView = hostingView
        editWorkspaceWindow.title = "Edit Workspace"
        editWorkspaceWindow.isReleasedWhenClosed = true
        editWorkspaceWindow.level = .floating  // Ensure window appears above other apps

        // Use intelligent positioning instead of simple center()
        WindowPositioningService.shared.positionWindow(
            editWorkspaceWindow,
            preferredSize: NSSize(width: 600, height: 700),
            context: .workspaceManager
        )

        let windowController = NSWindowController(window: editWorkspaceWindow)
        windowController.showWindow(nil)

        editWorkspaceWindow.makeKeyAndOrderFront(nil)
        NSApp.activate(ignoringOtherApps: true)

        print("[WorkspaceManagerView] Edit window should now be visible")
    }
}

// Workspace card component
struct WorkspaceCard: View {
    let workspace: Workspace
    let onDelete: () -> Void
    var onEdit: (() -> Void)? = nil

    @EnvironmentObject var workspaceService: WorkspaceService

    // Visual styling based on display status using existing DisplayChangeMonitorService
    private var visualStyle: WorkspaceVisualStyle {
        return WorkspaceVisualStyle(for: workspace)
    }
    @State private var isHovering = false

    var body: some View {
        VStack(spacing: 0) {
            HStack {
                // Left side: Icon and name
                Image.systemCompat("inset.filled.leadinghalf.toptrailing.bottomtrailing.rectangle")
                    .foregroundColor(.accentColor).font(
                        .title2)
                // Keep name in a VStack in case we add subtitle later
                VStack(alignment: .leading) {
                    HStack(spacing: 8) {
                        Text(workspace.name).font(.headline).foregroundColor(.primary)

                        // Status badge
                        if let badgeText = visualStyle.badgeText {
                            StatusBadge(text: badgeText, color: visualStyle.badgeColor)
                        }
                    }
                }

                Spacer(minLength: 50)  // Pushes controls to the far right with minimum spacing

                // Right side: Shortcut tag - fixed at the far right
                if let id = workspace.id {
                    let shortcutName = KeyboardShortcuts.Name.workspaceShortcut(for: id)
                    let shortcut = KeyboardShortcuts.getShortcut(for: shortcutName)

                    ShortcutTag(shortcut: shortcut)
                        .frame(width: 120)
                        .padding(.trailing, 8)
                }

                // Menu button (3 vertical dots)
                Menu {
                    Button {
                        workspaceService.triggerRestoreWorkspace(workspace: workspace)
                    } label: {
                        Label("Restore Workspace", systemImage: "arrow.clockwise")
                    }
                    .disabled(!visualStyle.canRestore)

                    Divider()

                    if let editAction = onEdit {
                        Button(action: editAction) {
                            Label("Edit Workspace", systemImage: "pencil")
                        }
                    }

                    Button(action: onDelete) {
                        Label("Delete Workspace", systemImage: "trash")
                    }
                } label: {
                    ZStack {
                        Rectangle()
                            .fill(Color.gray.opacity(0.1))
                            .frame(width: 30, height: 30)
                            .cornerRadius(4)

                        Text("⋮")  // Vertical ellipsis character
                            .font(.system(size: 18, weight: .bold))
                            .foregroundColor(.secondary)
                    }
                }
                .menuStyle(.borderlessButton)
                .menuIndicator(.hidden)
                .fixedSize()  // This ensures the menu only activates when clicking on the icon
            }
            .padding()
            .frame(maxWidth: .infinity)
            .background(Color(NSColor.windowBackgroundColor))
            .clipShape(RoundedRectangle(cornerRadius: SnapbackTheme.CornerRadius.card))
            .overlay(
                RoundedRectangle(cornerRadius: SnapbackTheme.CornerRadius.card).strokeBorder(
                    SnapbackTheme.Border.card,
                    lineWidth: SnapbackTheme.BorderWidth.normal))
        }
        .buttonStyle(.plain)
        .onHover { hovering in
            withAnimation(.easeInOut(duration: 0.2)) {
                isHovering = hovering
            }
        }
        .opacity(visualStyle.opacity)
        .saturation(visualStyle.saturation)
        .help(visualStyle.statusDescription)
        .contextMenu {
            Button {
                workspaceService.triggerRestoreWorkspace(workspace: workspace)
            } label: {
                CompatLabel("Restore Workspace", systemImage: "arrow.clockwise")
            }
            .disabled(!visualStyle.canRestore)

            if let editAction = onEdit {
                Button(action: editAction) { CompatLabel("Edit Workspace", systemImage: "pencil") }
            }

            Button(action: onDelete) { CompatLabel("Delete Workspace", systemImage: "trash") }
        }
    }
}

// Toast message component
struct ToastView: View {
    let message: String
    let type: WorkspaceManagerView.ToastType
    var backgroundColor: Color {
        switch type {
        case .success: .green
        case .error: .red
        case .info: .blue
        }
    }
    var body: some View {
        Text(message)
            .font(.system(size: SnapbackTheme.FontSize.body))
            .foregroundColor(.white)
            .padding(SnapbackTheme.Padding.standard)
            .background(backgroundColor)
            .clipShape(RoundedRectangle(cornerRadius: SnapbackTheme.CornerRadius.card))
            .snapbackToastShadow()
    }
}

// Status badge component for workspace cards
struct StatusBadge: View {
    let text: String
    let color: Color

    var body: some View {
        Text(text)
            .font(
                .system(
                    size: SnapbackTheme.FontSize.caption, weight: SnapbackTheme.FontWeight.heading)
            )
            .foregroundColor(.white)
            .padding(.horizontal, SnapbackTheme.Padding.vertical)
            .padding(.vertical, SnapbackTheme.Padding.small)
            .background(color)
            .clipShape(RoundedRectangle(cornerRadius: SnapbackTheme.CornerRadius.small))
            .shadow(color: color.opacity(0.3), radius: 2, x: 0, y: 1)
    }
}

// Preview
#Preview {
    let snappingService = WindowSnappingService()
    let workspaceService = WorkspaceService(snappingService: snappingService)
    workspaceService.workspaces = [
        Workspace(
            id: UUID(), name: "Development Setup Long Name", windowInfos: [], shortcutKeyCode: 18,
            shortcutModifiers: NSEvent.ModifierFlags.command.rawValue),  // Cmd+1
        Workspace(
            id: UUID(), name: "Design", windowInfos: [], shortcutKeyCode: 8,
            shortcutModifiers: NSEvent.ModifierFlags.command.union(.shift).rawValue),  // Shift+Cmd+C
        Workspace(id: UUID(), name: "Comms", windowInfos: []),  // No shortcut
    ]
    return WorkspaceManagerView().environmentObject(workspaceService)
}
