# Window Management Module Documentation

## Overview
The Window Management module is a comprehensive system for handling window positioning, snapping, and movement operations in Snapback. It provides both programmatic window management and drag-to-snap functionality, with support for multi-display setups and various window positioning modes.

## Directory Structure

```
WindowManagement/
├── Core Files
│   ├── AdjacentScreens.swift
│   ├── CGRectExtensions.swift
│   ├── DisplayScaleManager.swift
│   ├── ScreenDetectionService.swift
│   ├── SnapArea.swift
│   ├── SnapAreaModel.swift
│   ├── SnappingManager.swift
│   ├── SubsequentExecutionMode.swift
│   ├── WindowCalculationService.swift
│   ├── WindowManager.swift
│   ├── WindowMover.swift
│   ├── WindowPositioningService.swift
│   └── WindowSnappingService.swift
└── WindowCalculation/
    ├── AlmostMaximizeCalculation.swift
    ├── Calculation.swift
    ├── ChangeSizeCalculation.swift
    ├── LeftRightHalfCalculation.swift
    ├── SpecifiedCalculation.swift
    ├── StandardPositionCalculation.swift
    ├── TopBottomHalfCalculation.swift
    └── WindowCalculation.swift
```

## Core Files

### AdjacentScreens.swift
**Type:** Swift Class  
**Purpose:** Represents adjacent screens in a multi-display setup for cross-monitor window operations.

**Key Components:**
- `AdjacentScreens` class: Contains `prev`, `current`, and `next` NSScreen references
- Used by screen detection services to enable window movement across displays
- Essential for multi-monitor window management workflows

### CGRectExtensions.swift
**Type:** Swift Extensions  
**Purpose:** Provides coordinate system conversions and geometric utilities for window positioning.

**Key Components:**
- `CGRect.center`: Returns center point of rectangle
- `CGRect.screenFlipped`: Converts between Cocoa and AX coordinate systems
- `CGRect.isLandscape`: Determines if rectangle is wider than tall
- `CGRect.numSharedEdges(withRect:)`: Counts shared edges between rectangles
- `CGPoint.screenFlipped`: Point coordinate system conversion
- Critical for accurate window positioning across different coordinate systems

### DisplayScaleManager.swift
**Type:** Swift Class (Singleton)  
**Purpose:** Handles scale factor conversions between displays with different resolutions.

**Key Components:**
- `DisplayScaleManager.shared`: Singleton instance
- `convertRect(rect:fromScreen:toScreen:)`: Converts rectangles between different display scales
- `convertPoint(point:fromScreen:toScreen:)`: Converts points between displays
- Handles complex scenarios like vertical display arrangements with different scale factors
- Essential for proper window positioning on mixed-resolution setups

### ScreenDetectionService.swift
**Type:** Swift Class  
**Purpose:** Detects and manages screen configurations, following Rectangle's approach.

**Key Components:**
- Screen detection using window-based approach
- `adjacent(toFrameOfScreen:screens:)`: Finds adjacent screens for multi-display operations
- Handles both single and multi-display configurations
- Provides screen geometry and arrangement information
- Critical for determining target screens for window operations

### SnapArea.swift & SnapAreaModel.swift
**Type:** Swift Structs/Classes  
**Purpose:** Define snap regions and manage drag-to-snap configurations.

**Key Components:**
- `SnapArea`: Represents a screen region where windows can be snapped
- `Directional` enum: Defines 9 snap regions (corners, edges, center)
- `SnapAreaConfig`: Configuration for each snap area
- `SnapAreaModel.instance`: Singleton managing landscape/portrait configurations
- UserDefaults integration for persistent snap area customization
- Default configurations for both landscape and portrait orientations

### SnappingManager.swift
**Type:** Swift Class  
**Purpose:** Manages drag-to-snap functionality and cursor-based window snapping.

**Key Components:**
- Cursor position tracking and snap area detection
- `snapAreaContainingCursor(priorSnapArea:)`: Determines current snap area
- Handles drag-to-snap visual feedback and window positioning
- Integrates with SnapAreaModel for configuration-based snapping
- Supports both landscape and portrait screen orientations

### SubsequentExecutionMode.swift
**Type:** Swift Enum  
**Purpose:** Defines behavior when window actions are executed multiple times.

**Key Components:**
- `.none`: Stay on current display
- `.acrossMonitor`: Move to next display without resizing
- `.acrossAndResize`: Move to next display and resize if single display
- `.resize`: Resize on current display
- `.cycleMonitor`: Cycle through displays
- Used by calculation classes to determine multi-execution behavior

### WindowCalculationService.swift
**Type:** Swift Class
**Purpose:** Coordinates window position calculations by delegating to specialized calculation classes.

**Key Components:**
- Manages instances of all calculation classes (StandardPosition, AlmostMaximize, ChangeSize, etc.)
- `calculateWindowRect(for:window:screen:visibleFrameOnly:)`: Main calculation entry point
- Routes different WindowDirection types to appropriate calculation classes
- Integrates with ScreenDetectionService for multi-display support
- Provides unified interface for all window positioning calculations

### WindowManager.swift
**Type:** Swift Class (ObservableObject, NSWindowDelegate)
**Purpose:** Manages presentation of secondary windows (Settings, Workspace Manager, etc.).

**Key Components:**
- `settingsWindow`: Main settings window instance
- `workspaceManagerWindow`: Workspace management interface
- `saveWorkspaceWindowController`: Save workspace popup controller
- `editWorkspaceWindowController`: Edit workspace popup controller
- `aboutWindow`: About dialog window
- `openSettings()`, `openWorkspaceManager()`: Window presentation methods
- `showSaveWorkspacePopup(windowInfos:)`: Workspace save dialog
- `showEditWorkspacePopup(workspace:)`: Workspace edit dialog
- Window lifecycle management and positioning via WindowPositioningService

### WindowMover.swift
**Type:** Swift Class
**Purpose:** Handles the actual movement and resizing of windows using Accessibility APIs.

**Key Components:**
- `moveWindow(_:to:on:frameAdjustment:)`: Main window movement method
- Integrates with WindowCalculationService for target frame calculation
- Handles coordinate system conversions (screenFlipped)
- Applies frame adjustments for fine-tuning
- Uses AccessibilityElement for window manipulation
- Includes debug overlay management for development

### WindowPositioningService.swift
**Type:** Swift Class (Singleton)
**Purpose:** Positions application windows on the main display with intelligent placement.

**Key Components:**
- `WindowPositioningService.shared`: Singleton instance
- `positionWindow(_:preferredSize:context:)`: Main positioning method
- `WindowContext` enum: Tracks how windows were triggered (menuBar, keyboard, etc.)
- Centers windows horizontally, snaps to top to avoid dock
- Uses WindowLayoutManager for display information
- Ensures windows stay within effective screen bounds

### WindowSnappingService.swift
**Type:** Swift Class
**Purpose:** Provides high-level window snapping functionality for frontmost windows.

**Key Components:**
- `snapFrontmostWindow(to:)`: Main public interface for window snapping
- `SnapPosition` enum: Defines available snap positions
- Integrates with ScreenDetectionService for target screen detection
- Uses WindowMover for actual window positioning
- Includes error handling for common scenarios (no frontmost window, no target screen)
- Supports freemium model (snapping available to all users)

## WindowCalculation Subfolder

The WindowCalculation subfolder contains specialized calculation classes that handle different types of window positioning operations.

### Calculation.swift
**Type:** Swift Protocol & Structs
**Purpose:** Defines base protocol and parameter structures for all window calculations.

**Key Components:**
- `Calculation` protocol: Base interface for all calculation classes
- `RectCalculationParameters`: Input parameters for rectangle calculations
- `RectResult`: Result structure containing calculated rectangle and optional sub-action
- `WindowCalculationParameters`: Higher-level parameters including screen information
- `WindowCalculationResult`: Complete result with screen and action information
- `UsableScreens`: Helper for managing multi-screen scenarios

### WindowCalculation.swift
**Type:** Swift Class
**Purpose:** Base class for window calculations, providing common functionality.

**Key Components:**
- Inherits from `Calculation` protocol
- `calculate(_:)`: Converts WindowCalculationParameters to WindowCalculationResult
- `calculateRect(_:)`: Abstract method for subclasses to implement
- Provides default implementation returning null rect
- Base class for all specialized calculation implementations

### StandardPositionCalculation.swift
**Type:** Swift Class
**Purpose:** Handles standard window positioning operations (halves, quarters, maximize).

**Key Components:**
- Inherits from `WindowCalculation`
- Implements Rectangle's positioning algorithms exactly
- Supports: leftHalf, rightHalf, topHalf, bottomHalf, quarters, maximize
- Handles coordinate system conversions for secondary displays
- Includes extensive logging for debugging positioning issues
- Accounts for screen scale factors in multi-display setups

### LeftRightHalfCalculation.swift
**Type:** Swift Class
**Purpose:** Specialized calculation for left/right half positioning with multi-display and resize support.

**Key Components:**
- Inherits from `WindowCalculation`
- `calculateAcrossDisplays(_:)`: Moves windows across displays
- `calculateResize(_:)`: Cycles through half → two-thirds → third sizing
- Respects SubsequentExecutionMode settings
- Supports progressive resizing (half → 2/3 → 1/3 → half)
- Handles both single and multi-display scenarios

### TopBottomHalfCalculation.swift
**Type:** Swift Class
**Purpose:** Specialized calculation for top/bottom half positioning with multi-display support.

**Key Components:**
- Inherits from `WindowCalculation`
- Similar structure to LeftRightHalfCalculation but for vertical positioning
- `calculateAcrossDisplays(_:)`: Cross-display movement for top/bottom halves
- `calculateResize(_:)`: Progressive vertical sizing
- Handles topHalf and bottomHalf actions with SubsequentExecutionMode support

### AlmostMaximizeCalculation.swift
**Type:** Swift Class
**Purpose:** Calculates "almost maximize" positioning (slightly smaller than full screen).

**Key Components:**
- Inherits from `WindowCalculation`
- Uses configurable height/width percentages (almostMaximizeHeight/Width)
- Centers window within visible screen frame
- Provides alternative to full maximize for better visual appearance
- Respects screen visible frame (accounts for dock/menu bar)

### ChangeSizeCalculation.swift
**Type:** Swift Class
**Purpose:** Handles window size changes (larger, smaller, width adjustments).

**Key Components:**
- Inherits from `WindowCalculation`
- Implements `ChangeWindowDimensionCalculation` protocol
- Supports: larger, smaller, largerWidth, smallerWidth actions
- Uses configurable size offset (sizeOffsetAbs)
- Maintains window position while changing dimensions
- Includes curtain change size option for specific behaviors

### SpecifiedCalculation.swift
**Type:** Swift Class
**Purpose:** Positions windows to user-specified dimensions and centers them.

**Key Components:**
- Inherits from `WindowCalculation`
- Uses configurable specifiedHeight and specifiedWidth values
- Supports both absolute pixel values and percentage-based sizing
- Centers window within visible screen frame
- Allows precise control over window dimensions

## Integration and Dependencies

### Service Dependencies
- **LoggingService**: Used throughout for debugging and monitoring
- **DefaultsManager**: Provides user preferences and configuration
- **ScreenDetectionService**: Screen detection and multi-display management
- **AccessibilityElement**: Low-level window manipulation via Accessibility APIs
- **WindowLayoutManager**: Display information and layout management

### Key Relationships
1. **WindowSnappingService** → **WindowMover** → **WindowCalculationService** → **Specific Calculations**
2. **SnappingManager** → **SnapAreaModel** → **SnapArea** configurations
3. **DisplayScaleManager** ↔ **All calculation classes** for multi-display support
4. **WindowManager** → **WindowPositioningService** for application window placement

### Configuration Integration
- User preferences stored in DefaultsManager affect calculation behavior
- SnapAreaModel provides persistent drag-to-snap configurations
- SubsequentExecutionMode controls multi-execution behavior across calculations
- Scale factors and display arrangements handled automatically

## Usage Patterns

### Programmatic Window Management
```swift
// Snap frontmost window to left half
WindowSnappingService.shared.snapFrontmostWindow(to: .leftHalf)
```

### Drag-to-Snap
- Managed automatically by SnappingManager
- Configured via SnapAreaModel
- Visual feedback provided during drag operations

### Application Window Management
```swift
// Position settings window intelligently
WindowPositioningService.shared.positionWindow(window, context: .menuBar)
```

This module provides a complete window management solution with support for both user-initiated actions and programmatic control, designed to work seamlessly across single and multi-display configurations.
