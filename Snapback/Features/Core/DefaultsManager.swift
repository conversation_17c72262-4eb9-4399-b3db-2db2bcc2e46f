import Foundation

class DefaultsManager {
    static let shared = DefaultsManager()

    private init() {}

    // MARK: - Window Sizing Defaults

    enum Keys {
        // Window sizing
        static let gapSize = "gapSize"
        static let sizeOffset = "sizeOffset"
        static let minimumWindowWidth = "minimumWindowWidth"
        static let minimumWindowHeight = "minimumWindowHeight"
        static let almostMaximizeHeight = "almostMaximizeHeight"
        static let almostMaximizeWidth = "almostMaximizeWidth"
        static let specifiedHeight = "specifiedHeight"
        static let specifiedWidth = "specifiedWidth"
        static let curtainChangeSize = "curtainChangeSize"

        // Multi-display
        static let subsequentExecutionMode = "subsequentExecutionMode"

        // Stage Manager
        static let stageSize = "stageSize"
        static let stageEnabled = "stageEnabled"

        // Note: Removed allowAnyShortcut - following professional app approach

        // Window Management
        static let windowManagementEnabled = "windowManagementEnabled"

        // Toast Notifications
        static let showToastNotifications = "showToastNotifications"
        // License
        static let licenseKey = "SnapbackLicenseKey"
        static let licenseStatus = "SnapbackLicenseStatus"
        static let licenseInfo = "SnapbackLicenseInfo"
        static let lastLicenseValidation = "SnapbackLastLicenseValidation"
    }

    // Window gap size in pixels
    var gapSize: CGFloat {
        let value = UserDefaults.standard.float(forKey: Keys.gapSize)
        return value <= 0 ? 5.0 : CGFloat(value)
    }

    // Size change increment in pixels
    var sizeOffset: CGFloat {
        let value = UserDefaults.standard.float(forKey: Keys.sizeOffset)
        return value <= 0 ? 30.0 : CGFloat(value)
    }

    // Minimum window dimensions (as percentage of screen)
    var minimumWindowWidth: CGFloat {
        let value = UserDefaults.standard.float(forKey: Keys.minimumWindowWidth)
        return (value <= 0 || value > 1) ? 0.25 : CGFloat(value)
    }

    var minimumWindowHeight: CGFloat {
        let value = UserDefaults.standard.float(forKey: Keys.minimumWindowHeight)
        return (value <= 0 || value > 1) ? 0.25 : CGFloat(value)
    }

    // Almost maximize dimensions
    var almostMaximizeHeight: CGFloat {
        let value = UserDefaults.standard.float(forKey: Keys.almostMaximizeHeight)
        return (value <= 0 || value > 1) ? 0.9 : CGFloat(value)
    }

    var almostMaximizeWidth: CGFloat {
        let value = UserDefaults.standard.float(forKey: Keys.almostMaximizeWidth)
        return (value <= 0 || value > 1) ? 0.9 : CGFloat(value)
    }

    // Specified size dimensions
    var specifiedHeight: CGFloat {
        return CGFloat(UserDefaults.standard.float(forKey: Keys.specifiedHeight))
    }

    var specifiedWidth: CGFloat {
        return CGFloat(UserDefaults.standard.float(forKey: Keys.specifiedWidth))
    }

    // Whether to maintain window position against screen edges when resizing
    var curtainChangeSize: Bool {
        return UserDefaults.standard.bool(forKey: Keys.curtainChangeSize)
    }

    // MARK: - Multi-Display Defaults

    // Mode for subsequent executions of the same action
    var subsequentExecutionMode: SubsequentExecutionMode {
        let rawValue = UserDefaults.standard.integer(forKey: Keys.subsequentExecutionMode)
        return SubsequentExecutionMode(rawValue: rawValue) ?? .defaultMode
    }

    // MARK: - Stage Manager Defaults

    // Size of the Stage Manager strip (percentage or pixels)
    var stageSize: CGFloat {
        let value = UserDefaults.standard.float(forKey: Keys.stageSize)
        return CGFloat(value)
    }

    // Whether Stage Manager is enabled
    var stageEnabled: Bool {
        return UserDefaults.standard.bool(forKey: Keys.stageEnabled)
    }

    // MARK: - Shortcut Defaults
    // Note: Removed allowAnyShortcut setting - following professional app approach

    // MARK: - Window Management Defaults

    // Whether window management features are enabled (defaults to true)
    var windowManagementEnabled: Bool {
        // Check if the key exists, if not return true (default enabled)
        if UserDefaults.standard.object(forKey: Keys.windowManagementEnabled) == nil {
            return true
        }
        return UserDefaults.standard.bool(forKey: Keys.windowManagementEnabled)
    }

    // Set whether window management features are enabled
    func setWindowManagementEnabled(_ value: Bool) {
        UserDefaults.standard.set(value, forKey: Keys.windowManagementEnabled)
    }

    // MARK: - Toast Notification Defaults

    // Whether toast notifications are enabled (defaults to true)
    var showToastNotifications: Bool {
        // Check if the key exists, if not return true (default enabled)
        if UserDefaults.standard.object(forKey: Keys.showToastNotifications) == nil {
            return true
        }
        return UserDefaults.standard.bool(forKey: Keys.showToastNotifications)
    }

    // Set whether toast notifications are enabled
    func setShowToastNotifications(_ value: Bool) {
        UserDefaults.standard.set(value, forKey: Keys.showToastNotifications)
    }
}
