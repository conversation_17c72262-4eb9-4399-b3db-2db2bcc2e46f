# Workspace Module Documentation

## Overview
The Workspace module is the core functionality of Snapback, providing comprehensive workspace save/restore capabilities with CloudKit sync, display arrangement change detection, and intelligent window positioning. It handles capturing current window states, managing workspace configurations, and preventing restoration when critical display arrangement changes are detected to ensure data safety.

## Directory Structure

```
Workspace/
├── Core Models & Services
│   ├── Workspace.swift
│   ├── WorkspaceService.swift
│   ├── WindowCaptureService.swift
│   └── AppSelectionItem.swift
├── Display Management
│   ├── DisplayArrangementStabilityService.swift
│   └── DisplayChangeMonitorService.swift
├── User Interface
│   ├── SaveWorkspaceView.swift
│   ├── EditWorkspaceView.swift
│   ├── WorkspaceFormView.swift
│   ├── WorkspaceManagerView.swift
│   ├── WorkspacePreview.swift
│   ├── WorkspaceSettingsRow.swift
│   └── WorkspacesSettingsView.swift
└── Import/Export
    └── WorkspaceImportExportService.swift
```

## Core Models & Services

### Workspace.swift
**Type:** Swift Structs & Models  
**Purpose:** Defines core data structures for workspace management and display arrangement tracking.

**Key Components:**
- `WindowInfo`: Captures window frame, monitor ID, app bundle identifier, fullscreen state, and z-order
- `CustomLayout`: Represents user-defined window layouts with unique identifiers
- `DisplayFingerprint`: Stable display identification using resolution, scale factor, and hardware characteristics
- `DisplayArrangementInfo`: Metadata for display arrangements using fingerprinting for stable matching
- `Workspace`: Main workspace model containing window infos, shortcuts, display arrangement, and per-workspace settings
- Codable implementations for CloudKit sync and persistence
- Stable display identification system to handle display ID changes across system restarts

### WorkspaceService.swift
**Type:** Swift Class (ObservableObject)  
**Purpose:** Central service managing workspace CRUD operations, restoration logic, and CloudKit integration.

**Key Components:**
- `@Published var workspaces`: Reactive workspace collection for UI updates
- `@Published var isRestoring`: Restoration status tracking
- `addWorkspace(_:)`: Creates new workspaces with freemium model enforcement
- `updateWorkspace(_:)`: Updates existing workspaces (available to all users)
- `deleteWorkspace(_:)`: Removes workspaces with confirmation
- `triggerRestoreWorkspace(workspace:)`: Main restoration entry point with critical change detection
- `restoreWorkspaceInternal(workspace:)`: Core restoration logic with error handling and prevention mechanism
- CloudKit sync integration for cross-device workspace sharing
- Freemium model: 3 free workspaces, unlimited for licensed users
- Display arrangement change detection and restoration prevention for data safety

### WindowCaptureService.swift
**Type:** Swift Class  
**Purpose:** Captures current window positions and app states using Core Graphics APIs.

**Key Components:**
- `getCurrentAppsAndWindowPositionsNormalized()`: Main capture entry point with display normalization
- `getCurrentAppsAndWindowPositions()`: Raw window capture using `CGWindowListCopyWindowInfo`
- `createConsistentUUID(from:)`: Creates stable UUIDs from display IDs for cross-session consistency
- Z-order tracking (frontmost window = 0, back windows = higher values)
- Fullscreen detection and handling
- Window filtering (excludes desktop elements, Snapback's own windows)
- Integration with WindowLayoutManager for display detection and frame normalization
- Bundle identifier resolution for app identification

### AppSelectionItem.swift
**Type:** Swift Class (ObservableObject)  
**Purpose:** Represents selectable app windows in workspace save/edit interfaces.

**Key Components:**
- `@Published var isSelected`: Reactive selection state for UI binding
- `windowInfo`: Associated WindowInfo data
- `appName`: Computed app name from bundle identifier
- `previewColor`: App-specific color for visual identification
- `displayNameView`: SwiftUI view for app name display with window details
- `appIcon`: App icon retrieval and caching
- Equatable implementation for list management
- Integration with AppColorUtility for consistent app coloring

## Display Management

### Display Change Detection System
**Type:** Swift Classes & Enums
**Purpose:** Comprehensive display arrangement change detection and workspace restoration prevention system.

**Key Components:**
- `DisplayChangeMonitorService`: Monitors display arrangement changes and identifies critical changes
- `DisplayChangeType`: Categorizes changes (display added/removed, main display changed, position changed)
- Critical change detection for workspace restoration prevention
- Fingerprint-based display matching for stable identification

### DisplayArrangementStabilityService.swift
**Type:** Swift Class (Singleton)
**Purpose:** Distinguishes genuine display changes from false positives caused by system restarts or temporary fluctuations.

**Key Components:**
- `DisplayArrangementStabilityService.shared`: Singleton instance
- `isGenuineDisplayChange(savedArrangement:currentArrangement:)`: Main analysis method
- `DisplayChangeAnalysis`: Analysis result with change type, confidence, and reasons
- `DisplayChangeType`: Categorizes changes (genuine, potential restart, system instability)
- Post-startup grace period handling to avoid false positives
- Rapid change detection for system instability identification
- Confidence scoring for change reliability assessment
- Recent arrangement history tracking for stability analysis

### DisplayChangeMonitorService.swift
**Type:** Swift Class (Singleton)
**Purpose:** Monitors system display configuration changes and provides change analysis.

**Key Components:**
- `DisplayChangeMonitorService.shared`: Singleton instance
- `hasCriticalChanges(since:)`: Checks for significant display changes
- `analyzeChanges(since:)`: Detailed change analysis using stability service
- `getCurrentArrangement()`: Captures current display arrangement
- System display configuration change notifications
- Integration with DisplayArrangementStabilityService for analysis
- Critical change detection for workspace restoration decisions
- Consistent change detection regardless of UI state

## User Interface

### SaveWorkspaceView.swift
**Type:** SwiftUI View
**Purpose:** Modal interface for saving new workspaces with app selection and configuration.

**Key Components:**
- App selection interface with toggle controls and visual feedback
- Workspace naming with validation and duplicate detection
- Keyboard shortcut recording with conflict detection
- Custom layout creation and naming
- Per-workspace settings (close non-workspace windows)
- Display arrangement capture and storage
- App filtering (excludes Snapback's own windows)
- Freemium model integration (workspace limit enforcement)
- Real-time app selection counting and validation

### EditWorkspaceView.swift
**Type:** SwiftUI View
**Purpose:** Modal interface for editing existing workspaces.

**Key Components:**
- Reuses WorkspaceFormView for consistent UI
- Pre-populates form with existing workspace data
- "Update Current Positions" functionality for refreshing window positions
- Workspace deletion with confirmation
- Shortcut modification with conflict checking
- App selection updates with current window states
- Integration with WorkspaceService for updates

### WorkspaceFormView.swift
**Type:** SwiftUI View
**Purpose:** Reusable form component for workspace save/edit operations.

**Key Components:**
- Unified form interface for save and edit modes
- App selection list with search and filtering
- Keyboard shortcut recorder with validation
- Custom layout management
- Per-workspace settings configuration
- Form validation and error handling
- Consistent styling with SnapbackTheme
- Accessibility support and keyboard navigation

### WorkspaceManagerView.swift
**Type:** SwiftUI View
**Purpose:** Main workspace management interface with list view and operations.

**Key Components:**
- Workspace list with search and filtering
- Workspace restoration with progress tracking
- Edit/delete operations with confirmations
- Import/export functionality
- Freemium model UI (upgrade prompts, workspace limits)
- Real-time workspace status updates
- Keyboard shortcuts for common operations
- Integration with WorkspaceService for all operations

### WorkspacePreview.swift
**Type:** SwiftUI View
**Purpose:** Visual preview of workspace window arrangements.

**Key Components:**
- Miniature display representation
- Window positioning visualization
- App-specific coloring for window identification
- Multi-display arrangement preview
- Proportional scaling for different screen sizes
- Interactive preview with hover states
- Integration with AppColorUtility for consistent colors

### WorkspaceSettingsRow.swift
**Type:** SwiftUI View
**Purpose:** Individual workspace row in settings with inline editing and shortcut management.

**Key Components:**
- Inline name editing with validation
- Keyboard shortcut display and modification
- Delete confirmation with alert
- Conflict detection and resolution
- Real-time shortcut validation
- Integration with WorkspaceService for updates
- Accessibility labels and keyboard navigation

### WorkspacesSettingsView.swift
**Type:** SwiftUI View
**Purpose:** Settings panel for workspace management and configuration.

**Key Components:**
- Workspace list management
- Global workspace settings
- Import/export controls
- Freemium model status and upgrade options
- Workspace limit indicators
- Bulk operations (delete all, reset shortcuts)
- Integration with licensing system

## Import/Export

### WorkspaceImportExportService.swift
**Type:** Swift Class
**Purpose:** Handles workspace data import/export with versioning and error handling.

**Key Components:**
- `WorkspaceExportData`: Structured export format with version and metadata
- `ImportResult`: Import result with workspace data and version information
- `exportWorkspaces(_:completion:)`: Exports workspaces to JSON with pretty formatting
- `importWorkspaces(from:completion:)`: Imports workspaces with format validation
- Legacy format support for backward compatibility
- Error handling with specific error types (file access, encoding, invalid format)
- Background processing for large workspace collections
- ISO8601 date encoding for cross-platform compatibility
- Duplicate workspace handling during import

## Integration and Dependencies

### Service Dependencies
- **LoggingService**: Comprehensive logging throughout workspace operations
- **DefaultsManager**: User preferences and configuration management
- **WindowLayoutManager**: Display detection and frame normalization
- **DisplayScaleManager**: Multi-display scale factor handling
- **ToastManager**: User feedback for operations and errors
- **LicenseManager**: Freemium model enforcement and upgrade prompts
- **CloudKit**: Cross-device workspace synchronization

### Key Relationships
1. **WorkspaceService** → **WindowCaptureService** → **WindowLayoutManager** for window capture
2. **WorkspaceService** → **DisplayChangeMonitorService** for critical change detection and restoration prevention
3. **UI Views** → **WorkspaceService** for all workspace operations
4. **DisplayChangeMonitorService** → **DisplayArrangementStabilityService** for change analysis
5. **Import/Export** ↔ **WorkspaceService** for data persistence

### Configuration Integration
- Freemium model with 3 free workspaces, unlimited for licensed users
- Per-workspace settings (close non-workspace windows, custom shortcuts)
- Display arrangement fingerprinting for stable cross-session identification
- CloudKit sync for cross-device workspace sharing
- Critical display change detection and restoration prevention for data safety

## Usage Patterns

### Workspace Creation
```swift
// Capture current windows and save as workspace
let windowInfos = WindowCaptureService.getCurrentAppsAndWindowPositionsNormalized()
let workspace = Workspace(name: "My Workspace", windowInfos: windowInfos)
workspaceService.addWorkspace(workspace)
```

### Workspace Restoration
```swift
// Restore workspace with critical change detection and prevention
workspaceService.triggerRestoreWorkspace(workspace: workspace)
```

### Display Change Handling
```swift
// Check for critical display changes
let hasChanges = DisplayChangeMonitorService.shared.hasCriticalChanges(since: savedArrangement)
if hasChanges {
    // Prevent restoration and notify user
}
```

### Import/Export Operations
```swift
// Export workspaces
WorkspaceImportExportService.shared.exportWorkspaces(workspaces) { result in
    // Handle export result
}

// Import workspaces
WorkspaceImportExportService.shared.importWorkspaces(from: url) { result in
    // Handle import result and merge with existing workspaces
}
```

## Key Features

### 🛡️ **Smart Prevention System**
- Automatic critical display arrangement change detection
- Workspace restoration prevention when display changes could cause issues
- Fingerprint-based display matching for stability across system restarts
- Data safety prioritization over automatic adjustments

### 💾 **Robust Persistence**
- CloudKit integration for cross-device sync
- Structured export/import with versioning
- Backward compatibility with legacy formats
- Error recovery and data validation

### 🎯 **Freemium Model**
- 3 free workspaces for all users
- Unlimited workspaces for licensed users
- Graceful upgrade prompts and limit enforcement
- Feature preservation during trial/license transitions

### 🖥️ **Multi-Display Support**
- Proportional window positioning across different display arrangements
- Scale factor handling for mixed-resolution setups
- Display fingerprinting for stable identification
- Intelligent window distribution when displays are added/removed

This module provides a complete workspace management solution designed to handle the complexities of modern multi-display setups while maintaining data integrity and providing a smooth user experience across different system configurations.
