# Distribution Configuration Guide

This guide explains how to configure Snapback for different distribution channels.

## Quick Configuration

### For Setapp Distribution (No License System)

1. Open `Snapback/Configuration/FeatureFlags.swift`
2. Find the `isLicenseSystemDisabled` property
3. Change the `#else` block to return `true`:

```swift
static let isLicenseSystemDisabled: Bool = {
    #if DEBUG
    // Debug builds can use environment variable
    if let envValue = ProcessInfo.processInfo.environment["SNAPBACK_DISABLE_LICENSE"] {
        return envValue.lowercased() == "true" || envValue == "1"
    }
    return false
    #else
    // 🚨 CHANGE THIS FOR SETAPP DISTRIBUTION 🚨
    return true  // ← Change this to true for Setapp
    #endif
}()
```

4. Clean and rebuild the project
5. Verify that:
   - All features work without license prompts
   - Settings has no License tab
   - No trial expiration alerts appear

### For Direct Sales Distribution (With License System)

1. Open `Snapback/Configuration/FeatureFlags.swift`
2. Ensure the `#else` block returns `false`:

```swift
static let isLicenseSystemDisabled: Bool = {
    #if DEBUG
    // Debug builds can use environment variable
    if let envValue = ProcessInfo.processInfo.environment["SNAPBACK_DISABLE_LICENSE"] {
        return envValue.lowercased() == "true" || envValue == "1"
    }
    return false
    #else
    // Standard license system for direct sales
    return false  // ← Keep this as false for direct sales
    #endif
}()
```

3. Clean and rebuild the project
4. Verify that:
   - License system prompts appear for unlicensed users
   - Settings includes License tab
   - Pro license validation works correctly
   - Features are restricted without valid license

## Development Testing

### Testing Without License System

Set environment variable in Xcode scheme:
```
SNAPBACK_DISABLE_LICENSE = true
```

Or in terminal:
```bash
export SNAPBACK_DISABLE_LICENSE=true
# Then run your debug build
```

### Testing With License System

Remove or set environment variable to false:
```
SNAPBACK_DISABLE_LICENSE = false
```

Or simply don't set the environment variable at all.

## Verification Checklist

### When License System is DISABLED ✅

- [ ] App launches without license prompts
- [ ] All workspace features work immediately
- [ ] Settings shows: General, Hotkeys (if enabled), Workspaces
- [ ] Settings does NOT show License tab
- [ ] Menu bar shows all options without restrictions
- [ ] Keyboard shortcuts work without license checks
- [ ] No trial expiration alerts appear
- [ ] Save workspace works without prompts

### When License System is ENABLED ✅

- [ ] App shows license prompts for unlicensed users
- [ ] Unlicensed users only see License tab in Settings
- [ ] Licensed/trial users see all tabs in Settings
- [ ] Menu bar shows limited options for unlicensed users
- [ ] Keyboard shortcuts require license
- [ ] Trial expiration alerts appear when appropriate
- [ ] Save workspace requires license

## Build Configurations

For more advanced setups, consider creating separate build configurations:

1. **Snapback-Direct** - License system enabled
2. **Snapback-Setapp** - License system disabled

This allows building both versions without code changes.

## Troubleshooting

### License System Not Disabled

- Check that you modified the `#else` block, not the `#if DEBUG` block
- Clean build folder (Product → Clean Build Folder)
- Rebuild the project completely
- Check that `FeatureFlags.isLicenseSystemDisabled` returns `true` in logs

### License System Not Enabled

- Ensure `#else` block returns `false`
- Remove any `SNAPBACK_DISABLE_LICENSE` environment variables
- Clean and rebuild
- Check that `FeatureFlags.isLicenseSystemDisabled` returns `false` in logs

### Environment Variable Not Working

- Environment variables only work in DEBUG builds
- Check Xcode scheme environment variables are set correctly
- Restart Xcode after changing environment variables
- Use `print(ProcessInfo.processInfo.environment)` to debug

## Support

If you encounter issues with the feature flag system:

1. Check the logs for "LICENSE SYSTEM DISABLED" messages
2. Verify the correct build configuration is being used
3. Test both debug and release builds
4. Ensure all license-related code paths are properly updated
