import CoreGraphics
import Foundation

/// Service to handle display arrangement stability and detect genuine changes
/// This service helps distinguish between real display changes and false positives
/// that can occur after system restarts or temporary display system fluctuations
class DisplayArrangementStabilityService {
    static let shared = DisplayArrangementStabilityService()

    private let logger = LoggingService.shared
    private let serviceName = "DisplayArrangementStabilityService"

    // Store recent display arrangements to detect stability
    private var recentArrangements: [DisplayArrangementSnapshot] = []
    private let maxSnapshotHistory = 5
    private let stabilityCheckInterval: TimeInterval = 2.0

    // Track system startup to handle restart scenarios
    private var systemStartupTime: Date?
    private let postStartupGracePeriod: TimeInterval = 30.0  // 30 seconds after startup

    private init() {
        // Record system startup time
        systemStartupTime = Date()

        logger.info(
            "DisplayArrangementStabilityService initialized at system startup",
            service: serviceName
        )
    }

    /// Check if a display arrangement change is genuine or a false positive
    func isGenuineDisplayChange(
        savedArrangement: DisplayArrangementInfo,
        currentArrangement: DisplayArrangementInfo
    ) -> DisplayChangeAnalysis {

        logger.debug(
            "=== DISPLAY CHANGE ANALYSIS ===",
            service: serviceName
        )

        // Create snapshot of current arrangement
        let currentSnapshot = DisplayArrangementSnapshot(
            arrangement: currentArrangement,
            timestamp: Date()
        )

        // Add to history
        addArrangementSnapshot(currentSnapshot)

        // Check if we're in post-startup grace period
        let isInGracePeriod = isInPostStartupGracePeriod()

        logger.debug(
            "In post-startup grace period: \(isInGracePeriod)",
            service: serviceName
        )

        // Check for identical monitor swapping scenario
        let identicalMonitorAnalysis = analyzeIdenticalMonitorSwapping(
            saved: savedArrangement,
            current: currentArrangement
        )

        if identicalMonitorAnalysis.isIdenticalMonitorSwap {
            logger.info(
                "Detected identical monitor swapping - treating as non-critical change",
                service: serviceName
            )

            return DisplayChangeAnalysis(
                changeType: .identicalMonitorSwap,
                confidence: 0.95,
                reasons: identicalMonitorAnalysis.reasons,
                isInGracePeriod: isInGracePeriod
            )
        }

        // Check for rapid changes (potential system instability)
        let hasRapidChanges = detectRapidChanges()

        logger.debug(
            "Rapid changes detected: \(hasRapidChanges)",
            service: serviceName
        )

        // Perform comprehensive analysis
        let analysis = analyzeDisplayChange(
            saved: savedArrangement,
            current: currentArrangement,
            isInGracePeriod: isInGracePeriod,
            hasRapidChanges: hasRapidChanges
        )

        logger.debug(
            "Display change analysis result: \(analysis.changeType.rawValue), confidence: \(String(format: "%.2f", analysis.confidence))",
            service: serviceName
        )

        return analysis
    }

    /// Detect rapid changes in display arrangement that might indicate system instability
    private func detectRapidChanges() -> Bool {
        guard recentArrangements.count >= 3 else { return false }

        let recentWindow = recentArrangements.suffix(3)
        let timeSpan = recentWindow.last!.timestamp.timeIntervalSince(recentWindow.first!.timestamp)

        // If we've seen 3 different arrangements in less than 10 seconds, consider it rapid
        if timeSpan < 10.0 {
            logger.debug(
                "Rapid changes detected: 3 arrangements in \(String(format: "%.1f", timeSpan)) seconds",
                service: serviceName
            )
            return true
        }

        return false
    }

    /// Add a new arrangement snapshot to the history
    private func addArrangementSnapshot(_ snapshot: DisplayArrangementSnapshot) {
        recentArrangements.append(snapshot)

        // Keep only recent snapshots
        if recentArrangements.count > maxSnapshotHistory {
            recentArrangements.removeFirst()
        }

        logger.debug(
            "Added arrangement snapshot, total history: \(recentArrangements.count)",
            service: serviceName
        )
    }

    /// Check if we're still in the post-startup grace period
    private func isInPostStartupGracePeriod() -> Bool {
        guard let startupTime = systemStartupTime else { return false }
        return Date().timeIntervalSince(startupTime) < postStartupGracePeriod
    }

    /// Perform comprehensive display change analysis
    private func analyzeDisplayChange(
        saved: DisplayArrangementInfo,
        current: DisplayArrangementInfo,
        isInGracePeriod: Bool,
        hasRapidChanges: Bool
    ) -> DisplayChangeAnalysis {

        var confidence: Float = 1.0
        var changeType: DisplayChangeType = .noChange
        var reasons: [String] = []

        // 1. Check display count changes
        let savedCount = saved.displayFingerprints.count
        let currentCount = current.displayFingerprints.count

        if savedCount != currentCount {
            changeType = savedCount < currentCount ? .displayAdded : .displayRemoved
            reasons.append("Display count changed: \(savedCount) → \(currentCount)")
            confidence = 0.95  // High confidence for count changes
        }

        // 2. Check for fingerprint changes (potential restart scenario)
        let savedFingerprintSet = Set(saved.displayFingerprints.map { $0.stableID })
        let currentFingerprintSet = Set(current.displayFingerprints.map { $0.stableID })
        let commonFingerprints = savedFingerprintSet.intersection(currentFingerprintSet)

        if commonFingerprints.count < savedFingerprintSet.count && savedCount == currentCount {
            // Same number of displays but different IDs - likely restart scenario
            if isInGracePeriod {
                changeType = .potentialRestart
                confidence = 0.3  // Low confidence during grace period
                reasons.append(
                    "Display IDs changed during startup grace period (potential restart)")
            } else {
                changeType = .displayReplaced
                confidence = 0.8
                reasons.append("Display IDs changed outside grace period")
            }
        } else if commonFingerprints.count < savedFingerprintSet.count {
            // Some displays missing - show change immediately but with appropriate confidence
            changeType = .displayRemoved
            confidence = 0.9
            reasons.append("Some displays are missing from saved arrangement")
        }

        // 3. Check main display changes (HIGHEST PRIORITY - overrides other changes)
        let savedMainID = saved.mainDisplayFingerprint?.stableID
        let currentMainID = current.mainDisplayFingerprint?.stableID

        logger.debug(
            "🔍 MAIN DISPLAY COMPARISON: Saved='\(savedMainID ?? "none")', Current='\(currentMainID ?? "none")', Equal=\(savedMainID == currentMainID)",
            service: serviceName
        )

        if savedMainID != currentMainID {
            // Main display changes are CRITICAL and override other change types
            // This is the most important change for workspace restoration
            let previousChangeType = changeType
            changeType = .mainDisplayChanged
            confidence = min(confidence, 0.9)

            reasons.append(
                "Main display changed: \(savedMainID ?? "none") → \(currentMainID ?? "none")")

            logger.warning(
                "🚨 MAIN DISPLAY CHANGE DETECTED in stability service: \(savedMainID ?? "none") → \(currentMainID ?? "none") (overriding previous change type: \(previousChangeType.rawValue))",
                service: serviceName
            )
        }

        // 4. Check position changes with enhanced tolerance
        if changeType == .noChange || changeType == .potentialRestart {
            let positionAnalysis = analyzePositionChanges(saved: saved, current: current)

            if positionAnalysis.hasSignificantChanges {
                if changeType == .potentialRestart {
                    // During potential restart, be more lenient with position changes
                    confidence = min(confidence, 0.4)
                    reasons.append("Position changes detected during potential restart scenario")
                } else {
                    changeType = .positionChanged
                    confidence = min(confidence, positionAnalysis.confidence)
                    reasons.append("Significant position changes detected")
                }
            } else if positionAnalysis.hasMinorChanges {
                // Detect minor changes too, but with lower confidence
                changeType = .positionChanged
                confidence = min(confidence, 0.6)  // Medium confidence for minor changes
                reasons.append("Minor position changes detected")
            }
        }

        // 5. Check for rapid changes (system instability)
        if hasRapidChanges {
            confidence *= 0.3  // Significantly reduce confidence for rapid changes
            reasons.append("Rapid changes detected - likely system instability")
        }

        // 6. Apply stability check if we have history
        if recentArrangements.count >= 2 {
            let stabilityFactor = calculateStabilityFactor()
            confidence *= stabilityFactor
            reasons.append("Stability factor applied: \(String(format: "%.2f", stabilityFactor))")
        }

        logger.debug(
            "Analysis reasons: \(reasons.joined(separator: "; "))",
            service: serviceName
        )

        return DisplayChangeAnalysis(
            changeType: changeType,
            confidence: confidence,
            reasons: reasons,
            isInGracePeriod: isInGracePeriod
        )
    }

    /// Analyze if the change represents identical monitor swapping
    private func analyzeIdenticalMonitorSwapping(
        saved: DisplayArrangementInfo,
        current: DisplayArrangementInfo
    ) -> (isIdenticalMonitorSwap: Bool, reasons: [String]) {

        var reasons: [String] = []

        // Must have same number of displays
        guard saved.displayFingerprints.count == current.displayFingerprints.count else {
            return (false, ["Different number of displays"])
        }

        // Must have at least 2 displays for swapping to be meaningful
        guard saved.displayFingerprints.count >= 2 else {
            return (false, ["Single display setup - no swapping possible"])
        }

        // Identify identical hardware groups in saved arrangement
        let savedIdenticalGroups = groupIdenticalHardware(saved.displayFingerprints)
        let currentIdenticalGroups = groupIdenticalHardware(current.displayFingerprints)

        // Must have identical hardware groups
        guard savedIdenticalGroups.count == currentIdenticalGroups.count else {
            return (false, ["Different hardware composition"])
        }

        // Check if we have any identical monitor groups (same model, no unique hardware ID)
        let hasIdenticalMonitors = savedIdenticalGroups.contains { group in
            group.count > 1 && group.first?.hasHardwareUniqueID == false
        }

        guard hasIdenticalMonitors else {
            return (
                false, ["No identical monitors detected - all displays have unique hardware IDs"]
            )
        }

        // For each group of identical monitors, check if they've just been rearranged
        var swapDetected = false
        for savedGroup in savedIdenticalGroups {
            guard savedGroup.count > 1 else { continue }

            // Find corresponding group in current arrangement
            guard
                let currentGroup = currentIdenticalGroups.first(where: { currentGroup in
                    currentGroup.count == savedGroup.count
                        && currentGroup.first?.isIdenticalHardware(to: savedGroup.first!) == true
                })
            else {
                return (false, ["Could not match identical monitor groups"])
            }

            // Verify physical compatibility for the group
            let physicallyCompatible = verifyPhysicalCompatibility(
                savedGroup: savedGroup,
                currentGroup: currentGroup
            )

            if !physicallyCompatible {
                return (
                    false,
                    ["Monitors are not physically compatible - genuine display change detected"]
                )
            }

            // Check if positions have changed within this group
            // Use arrays instead of Sets since CGPoint.Hashable requires macOS 15.0+
            let savedPositions = savedGroup.map { $0.relativePosition }
            let currentPositions = currentGroup.map { $0.relativePosition }

            // If positions are different but the same set of positions exists, it's a swap
            if !arePositionArraysEqual(savedPositions, currentPositions) {
                // Check if current positions match any permutation of saved positions
                let positionsMatch = arePositionsPermutation(savedPositions, currentPositions)
                if positionsMatch {
                    swapDetected = true
                    reasons.append("Identical monitors swapped positions within group")
                } else {
                    return (false, ["Position changes don't match swapping pattern"])
                }
            }
        }

        if swapDetected {
            reasons.append(
                "Detected identical monitor swapping - workspace should adapt to new arrangement")
            return (true, reasons)
        }

        return (false, ["No swapping pattern detected"])
    }

    /// Group displays by identical hardware characteristics
    private func groupIdenticalHardware(_ fingerprints: [DisplayFingerprint])
        -> [[DisplayFingerprint]]
    {
        var groups: [[DisplayFingerprint]] = []
        var processed: Set<Int> = []

        for (i, fingerprint) in fingerprints.enumerated() {
            guard !processed.contains(i) else { continue }

            var group = [fingerprint]
            processed.insert(i)

            // Find all other displays with identical hardware
            for (j, otherFingerprint) in fingerprints.enumerated() {
                guard i != j && !processed.contains(j) else { continue }

                if fingerprint.isIdenticalHardware(to: otherFingerprint) {
                    group.append(otherFingerprint)
                    processed.insert(j)
                }
            }

            groups.append(group)
        }

        return groups
    }

    /// Check if two arrays of positions represent a permutation (same positions, different assignment)
    private func arePositionsPermutation(_ positions1: [CGPoint], _ positions2: [CGPoint]) -> Bool {
        guard positions1.count == positions2.count else { return false }

        // For identical monitor swapping, the positions should be exactly the same set
        // but assigned to different monitors - check if all positions from array1 exist in array2
        for position1 in positions1 {
            let found = positions2.contains { position2 in
                arePointsEqual(position1, position2)
            }
            if !found {
                return false
            }
        }
        return true
    }

    /// Check if two position arrays are equal (element-wise comparison)
    private func arePositionArraysEqual(_ positions1: [CGPoint], _ positions2: [CGPoint]) -> Bool {
        guard positions1.count == positions2.count else { return false }

        for (index, position1) in positions1.enumerated() {
            if !arePointsEqual(position1, positions2[index]) {
                return false
            }
        }
        return true
    }

    /// Compare two CGPoints with tolerance for floating point precision
    private func arePointsEqual(_ point1: CGPoint, _ point2: CGPoint, tolerance: CGFloat = 1.0)
        -> Bool
    {
        return abs(point1.x - point2.x) < tolerance && abs(point1.y - point2.y) < tolerance
    }

    /// Verify that two groups of monitors are compatible for migration with physical preference
    private func verifyPhysicalCompatibility(
        savedGroup: [DisplayFingerprint],
        currentGroup: [DisplayFingerprint]
    ) -> Bool {

        // Groups must have same count
        guard savedGroup.count == currentGroup.count else { return false }

        // For single monitor groups, check migration compatibility
        if savedGroup.count == 1 {
            let migrationCompatible = savedGroup[0].isCompatibleForMigration(with: currentGroup[0])
            let physicallyMatching = savedGroup[0].hasMatchingPhysicalDimensions(
                with: currentGroup[0])

            // Log the compatibility details
            logger.debug(
                "Single monitor compatibility - Migration: \(migrationCompatible), Physical: \(physicallyMatching)",
                service: serviceName
            )

            // Migration compatibility is required, physical matching is preferred but not required
            return migrationCompatible
        }

        // For multiple monitors, verify that each saved monitor has a migration-compatible
        // counterpart in the current group (allowing for position swapping)
        for savedMonitor in savedGroup {
            let hasMigrationCompatibleMatch = currentGroup.contains { currentMonitor in
                savedMonitor.isCompatibleForMigration(with: currentMonitor)
            }

            if !hasMigrationCompatibleMatch {
                logger.debug(
                    "No migration-compatible match found for saved monitor: \(savedMonitor.name ?? "unknown")",
                    service: serviceName
                )
                return false
            }
        }

        // Check how many have matching physical dimensions (for logging)
        let physicalMatches = savedGroup.filter { savedMonitor in
            currentGroup.contains { currentMonitor in
                savedMonitor.hasMatchingPhysicalDimensions(with: currentMonitor)
            }
        }.count

        logger.debug(
            "Migration compatibility verified for monitor group of \(savedGroup.count) displays (\(physicalMatches) with matching physical dimensions)",
            service: serviceName
        )
        return true
    }

    /// Analyze position changes with enhanced logic
    private func analyzePositionChanges(
        saved: DisplayArrangementInfo,
        current: DisplayArrangementInfo
    ) -> PositionAnalysis {

        var maxPositionDiff: CGFloat = 0
        var significantChanges = 0
        var minorChanges = 0
        let significantTolerance: CGFloat = 200  // Significant changes threshold
        let minorTolerance: CGFloat = 50  // Minor changes threshold (more sensitive)

        logger.debug(
            "=== POSITION ANALYSIS ===",
            service: serviceName
        )

        logger.debug(
            "Using tolerances: minor=\(minorTolerance)px, significant=\(significantTolerance)px",
            service: serviceName
        )

        for (index, savedFingerprint) in saved.displayFingerprints.enumerated() {
            guard index < current.displayFingerprints.count else {
                logger.debug(
                    "Display fingerprint \(savedFingerprint.stableID): Missing in current arrangement",
                    service: serviceName
                )
                continue
            }

            let currentFingerprint = current.displayFingerprints[index]

            let xDiff = abs(
                currentFingerprint.relativePosition.x - savedFingerprint.relativePosition.x)
            let yDiff = abs(
                currentFingerprint.relativePosition.y - savedFingerprint.relativePosition.y)
            let maxDiff = max(xDiff, yDiff)

            maxPositionDiff = max(maxPositionDiff, maxDiff)

            let isSignificant = maxDiff > significantTolerance
            let isMinor = maxDiff > minorTolerance && maxDiff <= significantTolerance

            if isSignificant {
                significantChanges += 1
            } else if isMinor {
                minorChanges += 1
            }

            logger.debug(
                "Display \(savedFingerprint.stableID): saved=\(savedFingerprint.relativePosition), current=\(currentFingerprint.relativePosition)",
                service: serviceName
            )

            logger.debug(
                "Display \(savedFingerprint.stableID): xDiff=\(String(format: "%.3f", xDiff)), yDiff=\(String(format: "%.3f", yDiff)), maxDiff=\(String(format: "%.3f", maxDiff)), significant=\(isSignificant), minor=\(isMinor)",
                service: serviceName
            )
        }

        let hasSignificantChanges = significantChanges > 0
        let hasMinorChanges = minorChanges > 0
        let confidence: Float =
            hasSignificantChanges
            ? min(1.0, Float(maxPositionDiff) / 500.0) : (hasMinorChanges ? 0.6 : 1.0)

        logger.debug(
            "Position analysis result: significantChanges=\(significantChanges), minorChanges=\(minorChanges), maxDiff=\(String(format: "%.1f", maxPositionDiff)), confidence=\(String(format: "%.2f", confidence))",
            service: serviceName
        )

        return PositionAnalysis(
            hasSignificantChanges: hasSignificantChanges,
            hasMinorChanges: hasMinorChanges,
            maxPositionDiff: maxPositionDiff,
            significantChangeCount: significantChanges,
            minorChangeCount: minorChanges,
            confidence: confidence
        )
    }

    /// Calculate stability factor based on recent arrangement history
    private func calculateStabilityFactor() -> Float {
        guard recentArrangements.count >= 2 else { return 1.0 }

        // Check if recent arrangements are consistent
        let recent = recentArrangements.suffix(3)
        var consistencyScore: Float = 1.0

        for i in 1..<recent.count {
            let prev = Array(recent)[i - 1]
            let curr = Array(recent)[i]

            // Compare arrangements
            if !areArrangementsConsistent(prev.arrangement, curr.arrangement) {
                consistencyScore *= 0.7  // Reduce confidence for inconsistency
            }
        }

        return consistencyScore
    }

    /// Check if two arrangements are consistent (allowing for minor differences)
    private func areArrangementsConsistent(
        _ arr1: DisplayArrangementInfo,
        _ arr2: DisplayArrangementInfo
    ) -> Bool {
        // Same display count and main display fingerprint
        return arr1.displayFingerprints.count == arr2.displayFingerprints.count
            && arr1.mainDisplayFingerprint?.stableID == arr2.mainDisplayFingerprint?.stableID
    }
}

// MARK: - Supporting Types

struct DisplayArrangementSnapshot {
    let arrangement: DisplayArrangementInfo
    let timestamp: Date
}

struct DisplayChangeAnalysis {
    let changeType: DisplayChangeType
    let confidence: Float  // 0.0 to 1.0
    let reasons: [String]
    let isInGracePeriod: Bool
}

enum DisplayChangeType: String {
    case noChange = "no_change"
    case displayAdded = "display_added"
    case displayRemoved = "display_removed"
    case displayReplaced = "display_replaced"
    case mainDisplayChanged = "main_display_changed"
    case positionChanged = "position_changed"
    case potentialRestart = "potential_restart"
    case identicalMonitorSwap = "identical_monitor_swap"
}

struct PositionAnalysis {
    let hasSignificantChanges: Bool
    let hasMinorChanges: Bool
    let maxPositionDiff: CGFloat
    let significantChangeCount: Int
    let minorChangeCount: Int
    let confidence: Float
}
