import Foundation

/// Comprehensive error handling for the Snapback License Management API
/// Implements all error codes from the API specification with proper HTTP status mapping
enum LicenseAPIError: Error, LocalizedError, Equatable {
    // MARK: - Client Errors (4xx)
    case validationError(String)
    case unauthorized
    case invalidToken
    case paymentRequired
    case paymentFailed(String)
    case paymentIncomplete
    case notFound(String)
    case licenseNotFound
    case deviceNotRegistered
    case sessionNotFound
    case paymentIntentNotFound
    case maxDevicesReached
    case licenseExpired
    case checkoutSessionExpired
    case trialAlreadyUsedEmail
    case trialAlreadyUsedDevice
    case trialAlreadyUsed
    case rateLimitExceeded(retryAfter: Int)

    // MARK: - Server Errors (5xx)
    case internalError
    case serverError(String)

    // MARK: - Network/Client Errors
    case networkError(String)
    case invalidResponse
    case invalidEmail
    case decodingError(String)
    case encodingError(String)
    case timeout
    case noInternetConnection

    // MARK: - Error Code Mapping

    /// Initialize from API error response
    init(from errorResponse: ErrorResponse) {
        guard let code = errorResponse.code else {
            self = .serverError(errorResponse.error)
            return
        }

        switch code {
        case "VALIDATION_ERROR":
            self = .validationError(errorResponse.details ?? errorResponse.error)
        case "UNAUTHORIZED":
            self = .unauthorized
        case "INVALID_TOKEN":
            self = .invalidToken
        case "NOT_FOUND":
            self = .notFound(errorResponse.details ?? errorResponse.error)
        case "LICENSE_NOT_FOUND":
            self = .licenseNotFound
        case "DEVICE_NOT_REGISTERED":
            self = .deviceNotRegistered
        case "SESSION_NOT_FOUND":
            self = .sessionNotFound
        case "PAYMENT_INTENT_NOT_FOUND":
            self = .paymentIntentNotFound
        case "LICENSE_EXPIRED":
            self = .licenseExpired
        case "MAX_DEVICES_REACHED":
            self = .maxDevicesReached
        case "TRIAL_ALREADY_USED_EMAIL":
            self = .trialAlreadyUsedEmail
        case "TRIAL_ALREADY_USED_DEVICE":
            self = .trialAlreadyUsedDevice
        case "TRIAL_ALREADY_USED":
            self = .trialAlreadyUsed
        case "PAYMENT_REQUIRED":
            self = .paymentRequired
        case "PAYMENT_FAILED":
            self = .paymentFailed(errorResponse.details ?? errorResponse.error)
        case "PAYMENT_INCOMPLETE":
            self = .paymentIncomplete
        case "CHECKOUT_SESSION_EXPIRED":
            self = .checkoutSessionExpired
        case "RATE_LIMIT_EXCEEDED":
            let retryAfter = errorResponse.retryAfter ?? 60
            self = .rateLimitExceeded(retryAfter: retryAfter)
        case "INTERNAL_ERROR":
            self = .internalError
        default:
            self = .serverError(errorResponse.error)
        }
    }

    /// Initialize from HTTP status code and response data
    init(httpStatusCode: Int, data: Data?) {
        // Try to decode error response first
        if let data = data,
            let errorResponse = try? JSONDecoder().decode(ErrorResponse.self, from: data)
        {
            self.init(from: errorResponse)
            return
        }

        // Fallback to status code mapping
        switch httpStatusCode {
        case 400:
            self = .validationError("Bad request")
        case 401:
            self = .unauthorized
        case 402:
            self = .paymentRequired
        case 404:
            self = .notFound("Resource not found")
        case 409:
            self = .maxDevicesReached
        case 410:
            self = .licenseExpired
        case 429:
            self = .rateLimitExceeded(retryAfter: 60)
        case 500...599:
            self = .internalError
        default:
            self = .networkError("HTTP \(httpStatusCode)")
        }
    }

    // MARK: - Error Properties

    /// HTTP status code associated with the error
    var httpStatusCode: Int {
        switch self {
        case .validationError, .invalidEmail, .encodingError:
            return 400
        case .unauthorized, .invalidToken:
            return 401
        case .paymentRequired, .paymentFailed, .paymentIncomplete:
            return 402
        case .notFound, .licenseNotFound, .deviceNotRegistered, .sessionNotFound,
            .paymentIntentNotFound:
            return 404
        case .maxDevicesReached, .trialAlreadyUsedEmail, .trialAlreadyUsedDevice, .trialAlreadyUsed:
            return 409
        case .licenseExpired, .checkoutSessionExpired:
            return 410
        case .rateLimitExceeded:
            return 429
        case .internalError, .serverError:
            return 500
        default:
            return 0  // Client-side errors
        }
    }

    /// Whether this error should trigger a retry
    var isRetryable: Bool {
        switch self {
        case .networkError, .timeout, .noInternetConnection, .internalError, .serverError:
            return true
        case .rateLimitExceeded:
            return true
        default:
            return false
        }
    }

    /// Retry delay for rate limiting
    var retryAfter: TimeInterval? {
        switch self {
        case .rateLimitExceeded(let retryAfter):
            return TimeInterval(retryAfter)
        default:
            return nil
        }
    }

    /// Whether this is a client-side error (user action required)
    var isClientError: Bool {
        return httpStatusCode >= 400 && httpStatusCode < 500
    }

    /// Whether this is a server-side error
    var isServerError: Bool {
        return httpStatusCode >= 500
    }

    // MARK: - LocalizedError Implementation

    var errorDescription: String? {
        switch self {
        case .validationError(let message):
            return message
        case .unauthorized:
            return "Authentication failed. Please re-validate your license."
        case .invalidToken:
            return "Device token is invalid or expired. Please re-validate your license."
        case .paymentRequired:
            return "Payment is required to create this license."
        case .paymentFailed(let message):
            return message
        case .paymentIncomplete:
            return "Payment is still being processed. Please wait."
        case .notFound(let resource):
            return "\(resource) not found"
        case .licenseNotFound:
            return "License key not found"
        case .deviceNotRegistered:
            return "Device not registered with license"
        case .sessionNotFound:
            return "Payment session not found. Please try again."
        case .paymentIntentNotFound:
            return "Payment intent not found"
        case .maxDevicesReached:
            return "Maximum number of devices reached for this license."
        case .licenseExpired:
            return "License has expired. Please renew your license."
        case .checkoutSessionExpired:
            return "Payment session has expired. Please start a new payment."
        case .trialAlreadyUsedEmail:
            return "A trial license has already been used with this email address."
        case .trialAlreadyUsedDevice:
            return "A trial license has already been used on this device."
        case .trialAlreadyUsed:
            return "A trial license has already been used with this email and device."
        case .rateLimitExceeded(let retryAfter):
            return "Too many requests. Please wait \(retryAfter) seconds."
        case .internalError:
            return "Server internal error. Please try again later."
        case .serverError(let message):
            return message
        case .networkError(let message):
            return message
        case .invalidResponse:
            return "Invalid response from server"
        case .invalidEmail:
            return "Invalid email address"
        case .decodingError(let message):
            return message
        case .encodingError(let message):
            return message
        case .timeout:
            return "Request timed out. Please check your internet connection."
        case .noInternetConnection:
            return "No internet connection. Please check your network settings."
        }
    }

    var failureReason: String? {
        switch self {
        case .unauthorized, .invalidToken:
            return "Authentication credentials are invalid or expired"
        case .paymentRequired, .paymentFailed, .paymentIncomplete:
            return "Payment processing issue"
        case .licenseExpired:
            return "License has expired"
        case .maxDevicesReached:
            return "Device limit reached"
        case .rateLimitExceeded:
            return "Rate limit exceeded"
        case .networkError, .timeout, .noInternetConnection:
            return "Network connectivity issue"
        default:
            return nil
        }
    }

    var recoverySuggestion: String? {
        switch self {
        case .unauthorized, .invalidToken:
            return "Please re-enter your license key to authenticate"
        case .paymentRequired:
            return "Please complete payment to activate your license"
        case .paymentFailed:
            return "Please try a different payment method"
        case .paymentIncomplete:
            return "Please wait for payment processing to complete"
        case .licenseExpired:
            return "Please purchase a new license or renew your existing one"
        case .maxDevicesReached:
            return "Please remove a device from your license or upgrade to a higher tier"
        case .trialAlreadyUsedEmail, .trialAlreadyUsedDevice, .trialAlreadyUsed:
            return "Please purchase a full license to continue using Snapback"
        case .rateLimitExceeded(let retryAfter):
            return "Please wait \(retryAfter) seconds before trying again"
        case .networkError, .timeout, .noInternetConnection:
            return "Please check your internet connection and try again"
        default:
            return "Please try again later"
        }
    }
}

// MARK: - Supporting Types

/// Error response model from the API
struct ErrorResponse: Codable {
    let error: String
    let code: String?
    let details: String?
    let retryAfter: Int?
}

// MARK: - Retry Logic

/// Retry configuration for API requests
struct RetryConfiguration {
    let maxAttempts: Int
    let baseDelay: TimeInterval
    let maxDelay: TimeInterval
    let backoffMultiplier: Double

    static let `default` = RetryConfiguration(
        maxAttempts: 3,
        baseDelay: 1.0,
        maxDelay: 30.0,
        backoffMultiplier: 2.0
    )
}

/// Retry helper for API requests
class APIRetryHelper {
    static func shouldRetry(error: LicenseAPIError, attempt: Int, configuration: RetryConfiguration)
        -> Bool
    {
        guard attempt < configuration.maxAttempts else { return false }
        return error.isRetryable
    }

    static func calculateDelay(
        for attempt: Int, configuration: RetryConfiguration, error: LicenseAPIError? = nil
    ) -> TimeInterval {
        // Use specific retry delay for rate limiting
        if let error = error, let retryAfter = error.retryAfter {
            return retryAfter
        }

        // Exponential backoff
        let delay = configuration.baseDelay * pow(configuration.backoffMultiplier, Double(attempt))
        return min(delay, configuration.maxDelay)
    }
}
