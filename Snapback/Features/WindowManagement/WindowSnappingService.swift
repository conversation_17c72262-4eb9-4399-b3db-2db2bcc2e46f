import AppKit
import Carbon

// Adapted from Rectangle (https://github.com/rxhanson/Rectangle) under GPL-3.0
class WindowSnappingService {
    // MARK: - Constants
    private let serviceName = "WindowSnappingService"
    private let logger = LoggingService.shared

    // MARK: - Dependencies
    private let windowMover: WindowMover
    private let calculationService: WindowCalculationService
    private let screenDetection: ScreenDetectionService
    private let accessibilityElement: AccessibilityElement

    // MARK: - Initialization
    init(
        windowMover: WindowMover = WindowMover(),
        calculationService: WindowCalculationService = WindowCalculationService(),
        screenDetection: ScreenDetectionService = ScreenDetectionService(),
        accessibilityElement: AccessibilityElement = AccessibilityElement()
    ) {
        self.windowMover = windowMover
        self.calculationService = calculationService
        self.screenDetection = screenDetection
        self.accessibilityElement = accessibilityElement
        logger.info("Initialized with dependencies", service: serviceName)
    }

    // MARK: - Public Methods
    func snapFrontmostWindow(to position: SnapPosition) {
        // FREEMIUM MODEL: Window snapping is available to all users
        // Only workspace creation is limited for free users
        logger.debug(
            "Snapping frontmost window to \(position) (available to all users)",
            service: serviceName
        )

        Task {
            do {
                let window = try await getFrontmostWindow()
                try await snapWindow(window, to: position)
            } catch WindowSnappingError.noFrontmostWindow {
                logger.warning("No frontmost window found", service: serviceName)
            } catch WindowSnappingError.noTargetScreen {
                logger.warning("Failed to determine target screen", service: serviceName)
            } catch {
                logger.error("Error snapping window: \(error)", service: serviceName)
            }
        }
    }

    // MARK: - Private Methods
    private func snapWindow(_ window: AXUIElement?, to position: SnapPosition) async throws {
        guard let window = window else {
            logger.warning("Window is nil", service: serviceName)
            throw WindowSnappingError.noFrontmostWindow
        }

        // Log screen information for debugging
        ScreenDebugger.shared.logScreenInfo()

        // Use Rectangle's screen detection approach
        guard let usableScreens = screenDetection.detectScreens(using: window) else {
            logger.warning(
                "Failed to detect screens using Rectangle's approach", service: serviceName)
            throw WindowSnappingError.noTargetScreen
        }

        let currentScreen = usableScreens.currentScreen
        let direction = convertSnapPositionToDirection(position)

        // Log comprehensive screen and window information
        logger.info(
            "Window Management Operation - Position: \(position), Direction: \(direction), "
                + "Current Screen: \(screenIdentifier(currentScreen)), Total Screens: \(usableScreens.numScreens)",
            service: serviceName
        )

        // Get current window info for logging
        let windowInfo = try await accessibilityElement.windowInfo(for: window)
        logger.info(
            "Window Info - Frame: \(windowInfo.frame), Expected Screen: \(screenIdentifier(currentScreen))",
            service: serviceName
        )

        // Log screen arrangement for debugging vertical display issues
        logScreenArrangement(usableScreens.screens)

        // Log main display information for debugging coordinate flipping issues
        logMainDisplayInfo()

        // For basic snap operations (like topHalf, bottomHalf), the window should stay on the current screen
        // This is the key difference from the old approach which was incorrectly moving windows between screens
        let targetScreen = currentScreen

        logger.info(
            "Target Screen Decision - Expected: \(screenIdentifier(currentScreen)), "
                + "Actual Target: \(screenIdentifier(targetScreen)) (should be same for basic snaps)",
            service: serviceName
        )

        // Move window using Rectangle's approach
        try await windowMover.moveWindow(window, to: direction, on: targetScreen)

        // Verify the operation
        let newWindowInfo = try await accessibilityElement.windowInfo(for: window)
        let actualScreen = screenDetection.getScreenContaining(newWindowInfo.frame)

        logger.info(
            "Window Snap Result - Position: \(position), "
                + "Expected Screen: \(screenIdentifier(targetScreen)), "
                + "Actual Screen: \(actualScreen.map(screenIdentifier) ?? "Unknown"), "
                + "Window Frame Before: \(windowInfo.frame), "
                + "Window Frame After: \(newWindowInfo.frame)",
            service: serviceName
        )

        if let actualScreen = actualScreen, actualScreen != targetScreen {
            logger.warning(
                "ISSUE DETECTED: Window ended up on wrong screen! "
                    + "Expected: \(screenIdentifier(targetScreen)), "
                    + "Actual: \(screenIdentifier(actualScreen))",
                service: serviceName
            )
        }

        logger.info("Successfully snapped window to \(position)", service: serviceName)
    }

    private func getFrontmostWindow() async throws -> AXUIElement? {
        guard let frontmostApp = NSWorkspace.shared.frontmostApplication else {
            logger.warning("No frontmost application found", service: serviceName)
            return nil
        }

        let appElement = AXUIElementCreateApplication(frontmostApp.processIdentifier)
        var value: AnyObject?

        let error = AXUIElementCopyAttributeValue(
            appElement,
            kAXFocusedWindowAttribute as CFString,
            &value
        )

        guard error == .success else {
            logger.error("Failed to get focused window: \(error)", service: serviceName)
            throw AccessibilityError.failedToGetAttribute(error)
        }

        return (value as! AXUIElement)
    }

    // MARK: - Internal Methods
    internal func convertSnapPositionToDirection(_ position: SnapPosition) -> WindowDirection {
        logger.debug("Converting position \(position) to direction", service: serviceName)
        switch position {
        case .leftHalf: return .leftHalf
        case .rightHalf: return .rightHalf
        case .topHalf: return .topHalf
        case .bottomHalf: return .bottomHalf
        case .fullscreen: return .maximize
        case .leftThird: return .leftThird
        case .centerThird: return .centerThird
        case .rightThird: return .rightThird
        case .topLeftQuarter: return .topLeftQuarter
        case .topRightQuarter: return .topRightQuarter
        case .bottomLeftQuarter: return .bottomLeftQuarter
        case .bottomRightQuarter: return .bottomRightQuarter
        case .leftTwoThirds: return .leftTwoThirds
        case .centerTwoThirds: return .centerTwoThirds
        case .rightTwoThirds: return .rightTwoThirds
        case .custom(let rect): return .custom(rect)
        }
    }

    // MARK: - Helper Methods
    private func screenIdentifier(_ screen: NSScreen) -> String {
        if let screenNumber = screen.deviceDescription[NSDeviceDescriptionKey("NSScreenNumber")]
            as? NSNumber
        {
            return "Screen#\(screenNumber)"
        }
        return "Unknown"
    }

    private func logScreenArrangement(_ screens: [NSScreen]) {
        logger.info("Screen arrangement for window management:", service: serviceName)
        for (index, screen) in screens.enumerated() {
            let identifier = screenIdentifier(screen)
            let isMain = screen == NSScreen.main ? " (MAIN)" : ""
            logger.info(
                "  \(index): \(identifier) - frame: \(screen.frame)\(isMain)",
                service: serviceName
            )
        }
    }

    private func logMainDisplayInfo() {
        // Log information about main display detection for debugging coordinate issues
        let nsScreenMain = NSScreen.main
        let nsScreenFirst = NSScreen.screens.first

        // Find the actual main display (coordinates 0,0)
        let actualMainDisplay = NSScreen.screens.first { screen in
            screen.frame.minX == 0 && screen.frame.minY == 0
        }

        logger.info("Main Display Analysis:", service: serviceName)
        logger.info(
            "  NSScreen.main: \(nsScreenMain.map(screenIdentifier) ?? "nil") - frame: \(nsScreenMain?.frame ?? .zero)",
            service: serviceName)
        logger.info(
            "  NSScreen.screens[0]: \(nsScreenFirst.map(screenIdentifier) ?? "nil") - frame: \(nsScreenFirst?.frame ?? .zero)",
            service: serviceName)
        logger.info(
            "  Actual main (0,0): \(actualMainDisplay.map(screenIdentifier) ?? "nil") - frame: \(actualMainDisplay?.frame ?? .zero)",
            service: serviceName)

        if let nsMain = nsScreenMain, let actualMain = actualMainDisplay, nsMain != actualMain {
            logger.warning(
                "COORDINATE ISSUE: NSScreen.main (\(screenIdentifier(nsMain))) differs from actual main display (\(screenIdentifier(actualMain)))",
                service: serviceName
            )
        }
    }
}

// MARK: - Error Types
enum WindowSnappingError: Error, CustomStringConvertible {
    case noFrontmostWindow
    case noTargetScreen

    var description: String {
        switch self {
        case .noFrontmostWindow:
            return "No frontmost window found"
        case .noTargetScreen:
            return "No target screen found"
        }
    }
}
