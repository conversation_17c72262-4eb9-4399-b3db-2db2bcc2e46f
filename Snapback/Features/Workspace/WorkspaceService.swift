import ApplicationServices  // For AX APIs
import Cocoa
import Combine  // Needed for DispatchQueue.main.async
import Foundation
import KeyboardShortcuts

// Notification for when workspaces change
extension Notification.Name {
    static let workspacesDidChange = Notification.Name("com.snapback.workspacesDidChange")
}

protocol WindowRestorationProtocol {
    var isRestoring: Bool { get set }
    func restoreWorkspaceInternal(workspace: Workspace)
    func updateStatus(_ message: String)
    func cancel()
}

class WorkspaceService: ObservableObject {
    // Logger
    private let logger = LoggingService.shared
    private let serviceName = "WorkspaceService"

    // Published property to hold workspaces, allowing views to react
    @Published var workspaces: [Workspace] = []

    // --- NEW: Published properties for restoration status ---
    @Published var isRestoring: Bool = false
    @Published var restorationStatusMessage: String = ""
    // --- END NEW ---

    // Dependency on WindowSnappingService for applying custom layouts
    private let snappingService: WindowSnappingService

    // Reference to own bundle ID to avoid manipulating Snapback itself during restore
    private let ownBundleID = Bundle.main.bundleIdentifier

    // UserDefaults instance and key
    private let userDefaults: UserDefaults
    private let userDefaultsKey = "savedWorkspaces"

    private var restorationHandler: WindowRestorationProtocol?

    init(
        snappingService: WindowSnappingService,
        userDefaults: UserDefaults = .standard,
        restorationHandler: WindowRestorationProtocol? = nil
    ) {
        self.snappingService = snappingService
        self.userDefaults = userDefaults
        self.restorationHandler = restorationHandler
        self.workspaces = loadWorkspacesInternal()
    }

    // MARK: - KeyboardShortcuts Synchronization

    // TODO: Re-enable keyboard shortcuts synchronization when ready
    func synchronizeKeyboardShortcuts() {
        // logger.debug("Synchronizing KeyboardShortcuts with workspace model", service: serviceName)

        // Temporarily disabled
        /*
        for workspace in workspaces {
            if let id = workspace.id,
               let keyCode = workspace.shortcutKeyCode,
               let modifiers = workspace.shortcutModifiers {
        
                let shortcutName = KeyboardShortcuts.Name.workspaceShortcut(for: id)
        
                if KeyboardShortcuts.getShortcut(for: shortcutName) == nil {
                    let key = KeyboardShortcuts.Key(rawValue: Int(keyCode))
                    let modifierFlags = NSEvent.ModifierFlags(rawValue: modifiers)
                    let shortcut = KeyboardShortcuts.Shortcut(key, modifiers: modifierFlags)
        
                    KeyboardShortcuts.setShortcut(shortcut, for: shortcutName)
        
                    logger.debug(
                        "Set missing shortcut for workspace '\(workspace.name)' in KeyboardShortcuts",
                        service: serviceName)
                }
            }
        }
        */
    }

    // MARK: - Public API (CRUD Operations)

    func reloadWorkspaces() {
        self.workspaces = loadWorkspacesInternal()
        logger.info("Workspaces reloaded. Count: \(self.workspaces.count)", service: serviceName)
    }

    /// Check if a workspace name is unique across all existing workspaces
    /// - Parameters:
    ///   - name: The proposed workspace name (will be trimmed and compared case-insensitively)
    ///   - excludingId: Optional workspace ID to exclude from the check (for editing existing workspaces)
    /// - Returns: True if the name is unique, false if it conflicts with an existing workspace
    func isWorkspaceNameUnique(_ name: String, excludingId: UUID? = nil) -> Bool {
        let trimmedName = name.trimmingCharacters(in: .whitespacesAndNewlines)

        // Empty names are not considered unique
        guard !trimmedName.isEmpty else {
            return false
        }

        // Check against all existing workspaces
        for workspace in workspaces {
            // Skip the workspace being edited (if any)
            if let excludingId = excludingId, workspace.id == excludingId {
                continue
            }

            // Case-insensitive comparison
            if workspace.name.trimmingCharacters(in: .whitespacesAndNewlines).lowercased()
                == trimmedName.lowercased()
            {
                logger.debug(
                    "Workspace name '\(trimmedName)' conflicts with existing workspace '\(workspace.name)'",
                    service: serviceName
                )
                return false
            }
        }

        logger.debug("Workspace name '\(trimmedName)' is unique", service: serviceName)
        return true
    }

    func addWorkspace(_ workspace: Workspace) {
        let licenseManager = LicenseManager.shared

        // FREEMIUM MODEL: Check workspace count limits instead of full license access
        if licenseManager.isLicenseSystemEnabled && !licenseManager.hasUnlimitedWorkspaces {
            // Free users are limited to 3 workspaces
            if workspaces.count >= LicenseManager.maxFreeWorkspaces {
                logger.warning(
                    "Workspace add blocked - free user has reached limit of \(LicenseManager.maxFreeWorkspaces) workspaces",
                    service: serviceName
                )
                // Show upgrade prompt to user
                DispatchQueue.main.async {
                    ToastManager.shared.showError(
                        title: "Workspace Limit Reached",
                        message:
                            "Free users can save up to \(LicenseManager.maxFreeWorkspaces) workspaces. Upgrade to save unlimited workspaces.",
                        duration: 6.0
                    )
                }
                return
            }
        }

        workspaces.append(workspace)
        saveWorkspacesInternal()  // Call the actual save func
        logger.info(
            "Added workspace: \(workspace.name) (total: \(workspaces.count))", service: serviceName)
        // Trigger notification or use @Published to update UI if needed elsewhere
    }

    func updateWorkspace(_ updatedWorkspace: Workspace) {
        // FREEMIUM MODEL: Allow updating existing workspaces for all users
        // Only workspace creation is limited for free users

        guard let index = workspaces.firstIndex(where: { $0.id == updatedWorkspace.id }) else {
            logger.warning(
                "Failed to update workspace: ID \(updatedWorkspace.id?.uuidString ?? "nil") not found.",
                service: serviceName
            )
            return
        }
        workspaces[index] = updatedWorkspace
        saveWorkspacesInternal()  // Call the actual save func
        logger.info("Updated workspace: \(updatedWorkspace.name)", service: serviceName)
        // Trigger notification or use @Published to update UI if needed elsewhere
    }

    // Update just the shortcut data for a workspace
    func updateWorkspaceShortcut(id: UUID, keyCode: UInt16, modifiers: UInt) {
        logger.debug(
            "updateWorkspaceShortcut called for workspace ID \(id.uuidString)", service: serviceName
        )
        logger.debug(
            "keyCode=\(keyCode) (0x\(String(keyCode, radix: 16))), modifiers=\(modifiers) (0x\(String(modifiers, radix: 16)))",
            service: serviceName)

        guard let index = workspaces.firstIndex(where: { $0.id == id }) else {
            logger.warning(
                "Failed to update workspace shortcut: ID \(id.uuidString) not found.",
                service: serviceName
            )
            return
        }

        logger.debug(
            "Found workspace at index \(index): '\(workspaces[index].name)'", service: serviceName)

        // Check if there's any change needed
        let existingKeyCode = workspaces[index].shortcutKeyCode
        let existingModifiers = workspaces[index].shortcutModifiers

        if existingKeyCode == keyCode && existingModifiers == modifiers {
            logger.debug(
                "No change needed, shortcut is already set to these values", service: serviceName)
        } else {
            logger.debug(
                "Updating shortcut from keyCode=\(existingKeyCode.map { String($0) } ?? "nil"), modifiers=\(existingModifiers.map { String($0) } ?? "nil")",
                service: serviceName
            )
            logger.debug("to keyCode=\(keyCode), modifiers=\(modifiers)", service: serviceName)
        }

        // Create a new workspace with updated shortcut data
        var updatedWorkspace = workspaces[index]

        // Handle the case when a shortcut is cleared (keyCode = 0)
        if keyCode == 0 && modifiers == 0 {
            logger.debug(
                "Clearing shortcut for workspace '\(updatedWorkspace.name)'", service: serviceName)
            updatedWorkspace.shortcutKeyCode = nil
            updatedWorkspace.shortcutModifiers = nil
        } else {
            updatedWorkspace.shortcutKeyCode = keyCode
            updatedWorkspace.shortcutModifiers = modifiers
        }

        // Update the workspace in the array
        workspaces[index] = updatedWorkspace
        logger.debug("Updated workspace in memory", service: serviceName)

        // Save to persistent storage
        logger.debug("Saving to persistent storage", service: serviceName)
        saveWorkspacesInternal()

        // Force synchronize to ensure changes are written immediately
        userDefaults.synchronize()
        logger.debug("Forced UserDefaults synchronization", service: serviceName)

        // Check if the shortcut display string can be generated
        if let displayString = formatShortcut(keyCode: keyCode, modifiers: modifiers) {
            logger.debug("Shortcut display string: '\(displayString)'", service: serviceName)
        } else {
            logger.warning("Could not generate shortcut display string", service: serviceName)
        }

        // Log the NSEvent.ModifierFlags for debugging
        let modifierFlags = NSEvent.ModifierFlags(rawValue: modifiers)
        logger.debug("Modifier flags breakdown:", service: serviceName)
        logger.debug("  Control: \(modifierFlags.contains(.control))", service: serviceName)
        logger.debug("  Option: \(modifierFlags.contains(.option))", service: serviceName)
        logger.debug("  Shift: \(modifierFlags.contains(.shift))", service: serviceName)
        logger.debug("  Command: \(modifierFlags.contains(.command))", service: serviceName)
        logger.debug(
            "  Raw value: \(modifiers) (0x\(String(modifiers, radix: 16)))", service: serviceName)

        // Update the KeyboardShortcuts library with the new shortcut
        if let id = updatedWorkspace.id {
            let shortcutName = KeyboardShortcuts.Name.workspaceShortcut(for: id)

            if keyCode == 0 && modifiers == 0 {
                // If the shortcut is being cleared, set it to nil in KeyboardShortcuts
                logger.debug(
                    "Clearing shortcut in KeyboardShortcuts library", service: serviceName)

                // Set the shortcut to nil to clear it without resetting to default
                KeyboardShortcuts.setShortcut(nil, for: shortcutName)
                logger.debug(
                    "Set shortcut to nil in KeyboardShortcuts library", service: serviceName)

                // Verify the shortcut was cleared
                if KeyboardShortcuts.getShortcut(for: shortcutName) == nil {
                    logger.debug(
                        "Verified shortcut was cleared in KeyboardShortcuts", service: serviceName)
                } else {
                    logger.warning(
                        "Failed to clear shortcut in KeyboardShortcuts", service: serviceName)
                }
            } else {
                // Otherwise, set the new shortcut
                let key = KeyboardShortcuts.Key(rawValue: Int(keyCode))
                let shortcut = KeyboardShortcuts.Shortcut(key, modifiers: modifierFlags)

                logger.debug("Updating KeyboardShortcuts library:", service: serviceName)
                logger.debug("  Shortcut name: \(shortcutName)", service: serviceName)
                logger.debug("  Key: \(key)", service: serviceName)
                logger.debug("  Modifiers: \(modifierFlags)", service: serviceName)

                // Set the shortcut in KeyboardShortcuts
                KeyboardShortcuts.setShortcut(shortcut, for: shortcutName)
                logger.debug("Updated shortcut in KeyboardShortcuts library", service: serviceName)

                // Verify the shortcut was set
                if let setShortcut = KeyboardShortcuts.getShortcut(for: shortcutName) {
                    logger.debug(
                        "Verified shortcut was set in KeyboardShortcuts: \(setShortcut)",
                        service: serviceName)
                } else {
                    logger.warning(
                        "Failed to set shortcut in KeyboardShortcuts", service: serviceName)
                }
            }

            // Force synchronize to ensure changes are written immediately
            UserDefaults.standard.synchronize()
            logger.debug(
                "Forced UserDefaults synchronization after KeyboardShortcuts update",
                service: serviceName)
        }

        logger.info(
            "Updated shortcut for workspace '\(updatedWorkspace.name)': keyCode=\(keyCode), modifiers=\(modifiers)",
            service: serviceName
        )

        // Post notification to refresh menu
        logger.debug("Posting notification to refresh menu", service: serviceName)
        NotificationCenter.default.post(name: .refreshStatusMenu, object: nil)
        logger.debug("Notification posted", service: serviceName)

        logger.debug(
            "updateWorkspaceShortcut completed for workspace '\(updatedWorkspace.name)'",
            service: serviceName
        )
    }

    func deleteWorkspace(id: UUID?) {
        // FREEMIUM MODEL: Allow deleting workspaces for all users
        // This helps free users manage their limited workspace slots

        guard let id = id else {
            logger.warning("Attempted to delete workspace with nil ID.", service: serviceName)
            return
        }
        workspaces.removeAll { $0.id == id }
        saveWorkspacesInternal()  // Call the actual save func
        logger.info("Deleted workspace with ID: \(id.uuidString)", service: serviceName)
    }

    func deleteWorkspaces(at offsets: IndexSet) {
        workspaces.remove(atOffsets: offsets)
        saveWorkspacesInternal()  // Call the actual save func
        logger.info("Deleted workspaces at offsets: \(offsets)", service: serviceName)
    }

    // MARK: - Public API (Actions)

    // Updated to manage restoration state
    func triggerRestoreWorkspace(workspace: Workspace) {
        // FREEMIUM MODEL: Allow restoring workspaces for all users
        // Only workspace creation is limited for free users

        // Handle empty workspace case
        if workspace.windowInfos.isEmpty {
            logger.info(
                "Workspace '\(workspace.name)' has no windows to restore - preserving original data",
                service: serviceName,
                category: .workspaces
            )

            DispatchQueue.main.async {
                self.restorationStatusMessage = "No windows to restore."
                self.isRestoring = false

                // Show toast notification
                ToastManager.shared.showInfo(
                    title: "Nothing to Restore",
                    message:
                        "This workspace has no windows to restore. Original workspace data preserved.",
                    duration: 3.0
                )
            }
            return
        }

        // Set initial state
        DispatchQueue.main.async {
            self.isRestoring = true
            self.restorationStatusMessage = "Loading workspace: \(workspace.name)..."

            // Show loading toast
            ToastManager.shared.showLoading(
                title: "Restoring Workspace",
                message: "Loading \(workspace.name)..."
            )
        }

        // Use restoration handler if available
        if let handler = restorationHandler {
            // Let the handler manage its own isRestoring state in restoreWorkspaceInternal
            handler.restoreWorkspaceInternal(workspace: workspace)
        } else {
            // Run restoration in background
            DispatchQueue.global(qos: .userInitiated).async { [weak self] in
                guard let self = self else { return }
                self.restoreWorkspaceInternal(workspace: workspace)
            }
        }
    }

    func triggerApplyCustomLayout(workspace: Workspace) {
        guard let layout = workspace.customLayout else {
            logger.warning(
                "Apply custom layout failed: Could not get layout for workspace \(workspace.name).",
                service: serviceName
            )
            return
        }
        logger.info("Applying custom layout: \(layout.name)", service: serviceName)

        // TODO: Refine this logic as per improvement plan.
        // Current plan: Apply first frame to frontmost window.
        if let firstFrame = layout.layout.first {
            // Use the injected snapping service and explicitly qualify the enum case
            snappingService.snapFrontmostWindow(to: SnapPosition.custom(firstFrame))
            logger.info(
                "Applied first frame of custom layout to frontmost window.", service: serviceName)
        } else {
            logger.warning(
                "Custom layout '\(layout.name)' has no frames defined.", service: serviceName)
        }
        // TODO: Implement handling multiple frames from the layout and matching them to windows.
    }

    // MARK: - Performance Optimization Types

    enum AppTimingType: String {
        case native = "Native macOS"
        case electron = "Electron App"
        case browser = "Web Browser"
        case java = "Java App"
        case unknown = "Unknown"
    }

    struct AppTimingProfile {
        let appType: AppTimingType
        let baseDelay: TimeInterval
        let useVerification: Bool
        let maxRetries: Int
    }

    // MARK: - Performance Optimization Methods

    /// Calculate optimal timing profile based on app bundle identifier
    private func calculateOptimalTiming(for bundleID: String?) -> AppTimingProfile {
        guard let bundleID = bundleID else {
            return AppTimingProfile(
                appType: .unknown,
                baseDelay: 0.020,  // 20ms default
                useVerification: true,
                maxRetries: 2
            )
        }

        // Electron apps (need more time for rendering)
        let electronApps = [
            "com.microsoft.VSCode",
            "com.tinyspeck.slackmacgap",
            "com.hnc.Discord",
            "com.spotify.client",
            "com.figma.Desktop",
            "com.notion.id",
            "com.electron",
        ]

        // Web browsers (moderate timing needs)
        let browserApps = [
            "com.google.Chrome",
            "org.mozilla.firefox",
            "com.apple.Safari",
            "com.microsoft.edgemac",
            "com.operasoftware.Opera",
        ]

        // Java apps (can be slow to respond)
        let javaApps = [
            "com.jetbrains.intellij",
            "org.eclipse.platform.ide",
            "com.install4j.8059-1618-3637-4154.84",
        ]

        // Native macOS apps (fastest response)
        let nativeApps = [
            "com.apple.finder",
            "com.apple.TextEdit",
            "com.apple.Preview",
            "com.apple.Terminal",
            "com.apple.mail",
            "com.apple.Notes",
            "com.apple.Calculator",
        ]

        if electronApps.contains(where: { bundleID.contains($0) }) {
            return AppTimingProfile(
                appType: .electron,
                baseDelay: 0.030,  // 30ms for Electron apps
                useVerification: true,
                maxRetries: 3
            )
        } else if browserApps.contains(where: { bundleID.contains($0) }) {
            return AppTimingProfile(
                appType: .browser,
                baseDelay: 0.025,  // 25ms for browsers
                useVerification: true,
                maxRetries: 2
            )
        } else if javaApps.contains(where: { bundleID.contains($0) }) {
            return AppTimingProfile(
                appType: .java,
                baseDelay: 0.035,  // 35ms for Java apps
                useVerification: true,
                maxRetries: 3
            )
        } else if nativeApps.contains(where: { bundleID.contains($0) }) {
            return AppTimingProfile(
                appType: .native,
                baseDelay: 0.015,  // 15ms for native apps
                useVerification: false,  // Native apps are usually reliable
                maxRetries: 1
            )
        } else {
            return AppTimingProfile(
                appType: .unknown,
                baseDelay: 0.020,  // 20ms default
                useVerification: true,
                maxRetries: 2
            )
        }
    }

    /// Verify if window size was applied correctly (for performance optimization)
    private func verifyWindowSize(
        windowElement: AXUIElement,
        expectedSize: CGSize,
        tolerance: CGFloat = 2.0
    ) -> Bool {
        var currentSize: AnyObject?
        let sizeErr = AXUIElementCopyAttributeValue(
            windowElement, kAXSizeAttribute as CFString, &currentSize)

        guard sizeErr == AXError.success,
            let sizeValue = currentSize
        else {
            return false
        }

        var actualSize = CGSize.zero
        let success = AXValueGetValue(sizeValue as! AXValue, .cgSize, &actualSize)
        guard success else { return false }

        let widthDiff = abs(actualSize.width - expectedSize.width)
        let heightDiff = abs(actualSize.height - expectedSize.height)

        return widthDiff <= tolerance && heightDiff <= tolerance
    }

    /// Smart delay that can exit early if verification passes
    private func smartDelay(
        duration: TimeInterval,
        verification: (() -> Bool)? = nil,
        checkInterval: TimeInterval = 0.005  // 5ms check interval
    ) {
        guard let verification = verification else {
            Thread.sleep(forTimeInterval: duration)
            return
        }

        let startTime = Date()
        let endTime = startTime.addingTimeInterval(duration)

        while Date() < endTime {
            if verification() {
                // Verification passed, exit early
                return
            }
            Thread.sleep(forTimeInterval: checkInterval)
        }

        // Full duration elapsed without verification passing
    }

    // MARK: - Persistence (Moved from AppDelegate)

    // Renamed from saveWorkspaces in AppDelegate
    private func saveWorkspacesInternal() {
        logger.info("Attempting to save \(workspaces.count) workspaces...", service: serviceName)
        // Log details of each workspace being saved
        for workspace in workspaces {
            logger.debug("  Saving workspace: \(workspace.name)", service: serviceName)
            if let keyCode = workspace.shortcutKeyCode, let modifiers = workspace.shortcutModifiers
            {
                logger.debug(
                    "    Shortcut: keyCode=\(keyCode), modifiers=\(modifiers)", service: serviceName
                )
                if let displayString = formatShortcut(keyCode: keyCode, modifiers: modifiers) {
                    logger.debug("    Display string: \(displayString)", service: serviceName)
                }
            } else {
                logger.debug("    No shortcut assigned", service: serviceName)
            }
        }

        do {
            let encoder = JSONEncoder()
            encoder.outputFormatting = .prettyPrinted
            let encoded = try encoder.encode(workspaces)  // Encode the service's property

            // Log the size of the encoded data
            logger.debug(
                "Encoded workspaces data size: \(encoded.count) bytes", service: serviceName)

            // Log details of each workspace being saved
            for workspace in workspaces {
                if let id = workspace.id {
                    logger.debug("  Workspace ID: \(id.uuidString)", service: serviceName)

                    // Check if the shortcut is registered in KeyboardShortcuts
                    let shortcutName = KeyboardShortcuts.Name.workspaceShortcut(for: id)
                    if let shortcut = KeyboardShortcuts.getShortcut(for: shortcutName) {
                        logger.debug(
                            "  Shortcut is registered in KeyboardShortcuts: \(shortcut)",
                            service: serviceName)
                    } else {
                        logger.debug(
                            "  Shortcut is NOT registered in KeyboardShortcuts",
                            service: serviceName)
                    }
                }
            }

            userDefaults.set(encoded, forKey: userDefaultsKey)

            // Force synchronize to ensure changes are written immediately
            userDefaults.synchronize()

            logger.info(
                "Successfully encoded and saved workspaces to UserDefaults.", service: serviceName)

            // Post notification that workspaces changed
            NotificationCenter.default.post(name: .workspacesDidChange, object: nil)
        } catch {
            logger.error(
                "Error encoding workspaces: \(error.localizedDescription)", service: serviceName)
            // TODO: Implement user-facing error reporting
        }
    }

    // Renamed from loadWorkspaces in AppDelegate
    private func loadWorkspacesInternal() -> [Workspace] {
        logger.info("Attempting to load workspaces from UserDefaults...", service: serviceName)
        guard let data = userDefaults.data(forKey: userDefaultsKey) else {
            logger.info(
                "No data found in UserDefaults for key '\(userDefaultsKey)'.", service: serviceName)
            return []
        }
        logger.info("Successfully loaded data (\(data.count) bytes).", service: serviceName)

        do {
            let decoded = try JSONDecoder().decode([Workspace].self, from: data)
            logger.info("Successfully decoded \(decoded.count) workspaces.", service: serviceName)

            // Log details of each workspace being loaded
            for workspace in decoded {
                logger.debug("  Loaded workspace: \(workspace.name)", service: serviceName)
                if let id = workspace.id {
                    logger.debug("    ID: \(id.uuidString)", service: serviceName)
                }
                if let keyCode = workspace.shortcutKeyCode,
                    let modifiers = workspace.shortcutModifiers
                {
                    logger.debug(
                        "    Shortcut: keyCode=\(keyCode), modifiers=\(modifiers)",
                        service: serviceName)
                    if let displayString = formatShortcut(keyCode: keyCode, modifiers: modifiers) {
                        logger.debug("    Display string: \(displayString)", service: serviceName)
                    }
                } else {
                    logger.debug("    No shortcut assigned", service: serviceName)
                }
            }

            // Register all workspace shortcuts with KeyboardShortcuts
            logger.debug(
                "Registering workspace shortcuts with KeyboardShortcuts", service: serviceName)
            for workspace in decoded {
                if let id = workspace.id,
                    let keyCode = workspace.shortcutKeyCode,
                    let modifiers = workspace.shortcutModifiers
                {
                    // Create the shortcut name
                    let shortcutName = KeyboardShortcuts.Name.workspaceShortcut(for: id)

                    // Create the shortcut
                    let key = KeyboardShortcuts.Key(rawValue: Int(keyCode))
                    let modifierFlags = NSEvent.ModifierFlags(rawValue: modifiers)
                    let shortcut = KeyboardShortcuts.Shortcut(key, modifiers: modifierFlags)

                    // Set the shortcut in KeyboardShortcuts
                    KeyboardShortcuts.setShortcut(shortcut, for: shortcutName)

                    // Verify the shortcut was set
                    if let setShortcut = KeyboardShortcuts.getShortcut(for: shortcutName) {
                        logger.debug(
                            "Verified shortcut was set in KeyboardShortcuts: \(setShortcut)",
                            service: serviceName
                        )
                    } else {
                        logger.warning(
                            "Failed to set shortcut in KeyboardShortcuts for workspace '\(workspace.name)'",
                            service: serviceName
                        )

                        // Try to set it again
                        logger.debug(
                            "Trying to set shortcut again for workspace '\(workspace.name)'",
                            service: serviceName
                        )

                        KeyboardShortcuts.setShortcut(shortcut, for: shortcutName)
                    }

                    logger.debug(
                        "Registered workspace shortcut for '\(workspace.name)' in KeyboardShortcuts",
                        service: serviceName
                    )
                }
            }

            // Force synchronize to ensure changes are written immediately
            UserDefaults.standard.synchronize()
            logger.debug(
                "Forced UserDefaults synchronization after registering workspace shortcuts",
                service: serviceName
            )

            return decoded
        } catch {
            logger.error(
                "Error decoding workspaces: \(error.localizedDescription)", service: serviceName)
            // TODO: Implement user-facing error reporting
            // Consider removing the invalid data: UserDefaults.standard.removeObject(forKey: userDefaultsKey)
            return []
        }
    }

    // MARK: - Workspace Restoration Logic (Two-Phase Approach - No Activation in Phase 2)

    private func restoreWorkspaceInternal(workspace: Workspace) {
        // Message update can happen on background thread initially
        updateStatus("Phase 1: Launching apps...")

        // Start building the comprehensive log
        var restorationLog = """
            WORKSPACE RESTORATION:

            WORKSPACE INFO:
            - Name: \(workspace.name)
            - Windows: \(workspace.windowInfos.count)
            - Has Shortcut: \(workspace.shortcutKeyCode != nil)
            """

        // Check if display arrangement has changed
        let currentArrangement = WindowLayoutManager.captureCurrentDisplayArrangement()
        let workspaceToRestore = workspace

        if let savedArrangement = workspace.displayArrangement {
            // Use stability service to determine if this is a genuine change
            let stabilityAnalysis = DisplayArrangementStabilityService.shared
                .isGenuineDisplayChange(
                    savedArrangement: savedArrangement,
                    currentArrangement: currentArrangement
                )

            // Check if this is a critical display arrangement change
            let isCriticalChange = DisplayChangeMonitorService.shared.hasCriticalChanges(
                since: savedArrangement)

            restorationLog += """

                DISPLAY ARRANGEMENT:
                - Saved displays: \(savedArrangement.displayFingerprints.count)
                - Current displays: \(currentArrangement.displayFingerprints.count)
                - Main display changed: \(savedArrangement.mainDisplayFingerprint?.stableID != currentArrangement.mainDisplayFingerprint?.stableID)
                - Change type: \(stabilityAnalysis.changeType.rawValue)
                - Confidence: \(String(format: "%.1f", stabilityAnalysis.confidence * 100))%
                - Critical change detected: \(isCriticalChange)
                """

            if isCriticalChange {
                // Critical display arrangement change detected - prevent restoration
                restorationLog +=
                    "\n    - Restoration blocked: YES (critical display arrangement change)"

                let changeDescription =
                    DisplayChangeMonitorService.shared.getCriticalChangeDescription(for: workspace)
                    ?? "Critical display configuration change"

                logger.warning(
                    "Workspace restoration blocked due to critical display arrangement change: \(changeDescription)",
                    service: serviceName,
                    category: .workspaces
                )

                // Show error message and stop restoration
                DispatchQueue.main.async { [weak self] in
                    guard let self = self else { return }

                    self.isRestoring = false
                    self.restorationStatusMessage =
                        "Restoration blocked - display arrangement changed."

                    ToastManager.shared.showError(
                        title: "Restoration Blocked",
                        message:
                            "Cannot restore '\(workspace.name)' due to \(changeDescription.lowercased()). This prevents potential window positioning issues.",
                        duration: 6.0
                    )
                }

                // Log the comprehensive restoration information and return early
                logger.info(restorationLog, service: serviceName, category: .workspaces)
                return
            }
        } else {
            restorationLog += """

                DISPLAY ARRANGEMENT:
                - No saved display arrangement information
                - Current displays: \(currentArrangement.displayFingerprints.count)
                """
        }

        // Check if we should close non-workspace windows (per-workspace setting)
        let shouldCloseNonWorkspaceWindows = workspaceToRestore.closeNonWorkspaceWindows ?? false

        // If enabled, close non-workspace windows
        if shouldCloseNonWorkspaceWindows {
            updateStatus("Closing non-workspace windows...")
            closeNonWorkspaceWindows(workspace: workspaceToRestore)
            restorationLog += "\n- Closed non-workspace windows: Yes (per-workspace setting)"
        } else {
            restorationLog += "\n- Closed non-workspace windows: No (per-workspace setting)"
        }

        let launchGroup = DispatchGroup()

        // --- Phase 1: Launch All Necessary Apps ---
        var appsToLaunch = Set<String>()  // Use Set to avoid duplicate launches
        var launchResults = [String: Bool]()  // Track launch results

        // Identify apps to launch
        for windowInfo in workspaceToRestore.windowInfos {
            guard let targetBundleID = windowInfo.appBundleIdentifier,
                targetBundleID != self.ownBundleID
            else {
                continue  // Skip Snapback or windows without bundle ID
            }
            if !isAppRunning(bundleIdentifier: targetBundleID) {
                appsToLaunch.insert(targetBundleID)
            }
        }

        restorationLog += "\n\nPHASE 1: APP LAUNCHING"
        restorationLog += "\n- Apps to launch: \(appsToLaunch.count)"

        // Launch apps asynchronously
        if !appsToLaunch.isEmpty {
            restorationLog += "\n- Apps to launch: \(appsToLaunch.joined(separator: ", "))"

            for bundleID in appsToLaunch {
                launchGroup.enter()
                launchApp(bundleIdentifier: bundleID) { success, _ in
                    launchResults[bundleID] = success
                    launchGroup.leave()
                }
            }

            launchGroup.wait()  // Wait for all launch attempts

            // Add a single delay after all launches are done
            Thread.sleep(forTimeInterval: 2.0)  // Allow generous time for apps to open windows

            // Log launch results
            restorationLog += "\n- Launch results:"
            for (bundleID, success) in launchResults {
                restorationLog += "\n  • \(bundleID): \(success ? "Success" : "Failed")"
            }
        } else {
            restorationLog += "\n- No applications needed launching"
        }

        restorationLog += "\n- Phase 1 Complete"

        // --- Phase 2: Position Windows (No Activation) ---
        updateStatus("Phase 2: Positioning windows...")
        restorationLog += "\n\nPHASE 2: WINDOW POSITIONING"

        // Get a fresh list of running apps *after* the launch phase
        let runningApps = NSWorkspace.shared.runningApplications

        // Sort windows by z-order (back to front) to ensure proper stacking
        // This ensures windows are restored in the correct order (background windows first)
        let sortedWindowInfos = workspaceToRestore.windowInfos.sorted { $0.zOrder > $1.zOrder }

        restorationLog += "\n- Total windows to position: \(sortedWindowInfos.count)"
        restorationLog += "\n- Window order (back to front):"

        for (index, window) in sortedWindowInfos.enumerated() {
            if let bundleID = window.appBundleIdentifier {
                restorationLog += "\n  • \(index): \(bundleID) (z-order: \(window.zOrder))"
            }
        }

        var windowResults = [String: String]()  // Track window positioning results

        for savedWindowInfo in sortedWindowInfos {
            guard let targetBundleID = savedWindowInfo.appBundleIdentifier,
                targetBundleID != self.ownBundleID
            else {
                // Skip Snapback or windows without bundle ID
                continue
            }

            // Find the running instance (should exist now if launched)
            guard
                let currentAppInstance = runningApps.first(where: {
                    $0.bundleIdentifier == targetBundleID && !$0.isTerminated
                })
            else {
                windowResults[targetBundleID] = "Not running"
                updateStatus("Skipped: \(targetBundleID) (not running)")
                continue
            }

            let appName = currentAppInstance.localizedName ?? targetBundleID
            updateStatus("Positioning: \(appName)...")

            // Find the window element directly
            let targetPID = currentAppInstance.processIdentifier
            var windowElement: AXUIElement?
            var windowFoundMethod = "Not found"

            // Try to find window element
            for i in 0..<2 {
                windowElement = findWindowElement(for: targetPID, bundleID: targetBundleID)
                if windowElement != nil {
                    windowFoundMethod = "Found on attempt \(i + 1)"
                    break
                }
                if i < 1 {
                    Thread.sleep(forTimeInterval: 0.3)  // Keep short retry delay
                }
            }

            // Re-launch attempt if still not found (especially for hidden apps)
            if windowElement == nil {
                let relaunchGroup = DispatchGroup()
                relaunchGroup.enter()

                // Use relaunchApp which *does* activate
                relaunchApp(bundleIdentifier: targetBundleID) { success in
                    if success {
                        Thread.sleep(forTimeInterval: 1.0)  // Wait after re-launch
                    }
                    relaunchGroup.leave()
                }
                relaunchGroup.wait()  // Wait for re-launch attempt

                windowElement = findWindowElement(for: targetPID, bundleID: targetBundleID)  // Final attempt
                if windowElement != nil {
                    windowFoundMethod = "Found after re-launch"
                }
            }

            // Position the window if found
            guard let finalWindowElement = windowElement else {
                windowResults[targetBundleID] = "Window not found"
                updateStatus("Failed: \(appName) (no window)")

                // Show error toast for this specific window
                DispatchQueue.main.async {
                    ToastManager.shared.showError(
                        title: "Window Not Found",
                        message:
                            "Could not find window for \(appName). Try launching the app manually.",
                        duration: 3.0
                    )
                }

                continue
            }

            // Apply the normalized frame
            applyNormalizedFrame(windowElement: finalWindowElement, savedInfo: savedWindowInfo)
            windowResults[targetBundleID] = "Positioned (\(windowFoundMethod))"
        }

        // Log window positioning results
        restorationLog += "\n- Window positioning results:"
        for (bundleID, result) in windowResults {
            restorationLog += "\n  • \(bundleID): \(result)"
        }

        restorationLog += "\n- Phase 2 Complete"

        // Phase 3: Set the z-order of windows (bring windows to front in correct order)
        updateStatus("Phase 3: Setting window z-order...")
        restorationLog += "\n\nPHASE 3: WINDOW Z-ORDER"

        // We need to bring windows to front in reverse order (back to front)
        let backToFrontWindows = workspaceToRestore.windowInfos.sorted { $0.zOrder > $1.zOrder }

        restorationLog += "\n- Windows to order: \(backToFrontWindows.count)"

        var activationResults = [String: Bool]()  // Track activation results

        for windowInfo in backToFrontWindows {
            guard let targetBundleID = windowInfo.appBundleIdentifier,
                targetBundleID != self.ownBundleID
            else {
                continue
            }

            // Find the running app
            guard
                let app = NSWorkspace.shared.runningApplications.first(where: {
                    $0.bundleIdentifier == targetBundleID && !$0.isTerminated
                })
            else {
                activationResults[targetBundleID] = false
                continue
            }

            // Use NSRunningApplication's activate method to bring the window to front
            let options: NSApplication.ActivationOptions = {
                if #available(macOS 14.0, *) {
                    return []  // In macOS 14+, no options needed as the API behavior changed
                } else {
                    return [.activateIgnoringOtherApps]  // For older macOS versions
                }
            }()
            let activated = app.activate(options: options)
            activationResults[targetBundleID] = activated

            // Small delay to allow the window manager to process the z-order change
            Thread.sleep(forTimeInterval: 0.1)
        }

        // Log activation results
        restorationLog += "\n- Activation results:"
        for (bundleID, success) in activationResults {
            restorationLog += "\n  • \(bundleID): \(success ? "Success" : "Failed")"
        }

        restorationLog += "\n- Phase 3 Complete"

        // Log the comprehensive restoration information
        logger.info(restorationLog, service: serviceName, category: .workspaces)

        // Ensure we update final state on main thread
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.restorationStatusMessage = "Restoration complete."
            self.isRestoring = false

            // Standard success toast
            ToastManager.shared.completeLoading(
                title: "Workspace Restored",
                message: "\(workspaceToRestore.name) has been successfully restored.",
                duration: 3.0
            )

            // Note: Migration cache is NOT cleared here to allow user-controlled updates
            // It will be cleared when the user makes a decision or starts a new restoration
        }
    }

    // MARK: - Restoration Helpers (Moved from AppDelegate)

    // Helper to update status message on main thread
    private func updateStatus(_ message: String) {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.restorationStatusMessage = message
            self.logger.info("Status: \(message)", service: self.serviceName)
        }
    }

    // Helper to close all windows that aren't part of the workspace
    private func closeNonWorkspaceWindows(workspace: Workspace) {
        logger.info("Closing non-workspace windows...", service: serviceName)

        // Get all running apps
        let runningApps = NSWorkspace.shared.runningApplications

        // Create a set of bundle IDs that are part of the workspace
        var workspaceBundleIDs = Set<String>()
        for windowInfo in workspace.windowInfos {
            if let bundleID = windowInfo.appBundleIdentifier {
                workspaceBundleIDs.insert(bundleID)
            }
        }

        // Add Snapback to the list of apps to keep
        if let ownBundleID = self.ownBundleID {
            workspaceBundleIDs.insert(ownBundleID)
        }

        // Add system apps that should never be closed
        let systemAppsToKeep = [
            "com.apple.finder",
            "com.apple.systempreferences",
            "com.apple.systemuiserver",
            "com.apple.dock",
            "com.apple.notificationcenterui",
            "com.apple.controlcenter",
        ]

        for systemApp in systemAppsToKeep {
            workspaceBundleIDs.insert(systemApp)
        }

        logger.debug("Apps to keep: \(workspaceBundleIDs)", service: serviceName)

        // Close windows for apps that aren't part of the workspace
        for app in runningApps {
            if let bundleID = app.bundleIdentifier,
                !workspaceBundleIDs.contains(bundleID),
                app.activationPolicy == .regular
            {  // Only close regular apps, not background or UI element apps

                logger.info("Closing app: \(bundleID)", service: serviceName)

                // Try to terminate the app gracefully
                app.terminate()
            }
        }

        // Give apps a moment to close
        Thread.sleep(forTimeInterval: 1.0)
    }

    private func isAppRunning(bundleIdentifier: String) -> Bool {
        return NSWorkspace.shared.runningApplications.contains {
            $0.bundleIdentifier == bundleIdentifier && !$0.isTerminated  // Check if not terminated
        }
    }

    // Modified launchApp to return the NSRunningApplication instance
    private func launchApp(
        bundleIdentifier: String, completion: @escaping (Bool, NSRunningApplication?) -> Void
    ) {
        guard
            let appURL = NSWorkspace.shared.urlForApplication(
                withBundleIdentifier: bundleIdentifier)
        else {
            print("[WorkspaceService - launchApp] Could not find URL for \(bundleIdentifier).")
            completion(false, nil)  // Return nil for app instance
            return
        }

        let configuration = NSWorkspace.OpenConfiguration()
        configuration.activates = false  // Launch in background initially

        NSWorkspace.shared.openApplication(
            at: appURL,
            configuration: configuration
        ) { runningApp, error in
            // No need for DispatchQueue.main.async here as completion is called from background anyway
            if let error = error {
                print(
                    "[WorkspaceService - launchApp] Error launching \(bundleIdentifier): \(error)")
                completion(false, nil)  // Return nil
            } else if let app = runningApp {  // Use the returned runningApp
                // Don't print PID here, just confirm launch signal sent
                completion(true, app)  // Return the app instance
            } else {
                print(
                    "[WorkspaceService - launchApp] Launched \(bundleIdentifier) but runningApp instance is nil."
                )
                completion(false, nil)  // Return nil
            }
        }
    }

    // Helper to re-launch/re-open an app (useful if it's hidden without windows)
    private func relaunchApp(bundleIdentifier: String, completion: @escaping (Bool) -> Void) {
        guard
            let appURL = NSWorkspace.shared.urlForApplication(
                withBundleIdentifier: bundleIdentifier)
        else {
            print("[WorkspaceService - relaunchApp] Could not find URL for \(bundleIdentifier).")
            completion(false)
            return
        }

        let configuration = NSWorkspace.OpenConfiguration()
        configuration.activates = true  // Activate when re-opening

        NSWorkspace.shared.openApplication(
            at: appURL,
            configuration: configuration
        ) { _, error in
            if let error = error {
                print(
                    "[WorkspaceService - relaunchApp] Error re-opening \(bundleIdentifier): \(error)"
                )
                completion(false)
            } else {
                // Don't print success here, just confirm completion
                completion(true)
            }
        }
    }

    // Refined findWindowElement with better logging and handling
    private func findWindowElement(for pid: pid_t, bundleID: String) -> AXUIElement? {
        // Reduced logging noise inside this frequently called function
        let appElement = AXUIElementCreateApplication(pid)
        var axErr: AXError
        var windowElement: AnyObject?

        // 1. Try Main Window
        axErr = AXUIElementCopyAttributeValue(
            appElement, kAXMainWindowAttribute as CFString, &windowElement)
        if axErr == AXError.success, let element = windowElement {
            return (element as! AXUIElement)
        }

        // 2. Try Focused Window
        windowElement = nil  // Reset before next try
        axErr = AXUIElementCopyAttributeValue(
            appElement, kAXFocusedWindowAttribute as CFString, &windowElement)
        if axErr == AXError.success, let element = windowElement {
            return (element as! AXUIElement)
        }

        // 3. Try Window List
        windowElement = nil  // Reset before next try
        var windowsList: AnyObject?
        axErr = AXUIElementCopyAttributeValue(
            appElement, kAXWindowsAttribute as CFString, &windowsList)

        guard axErr == AXError.success, let windows = windowsList as? [AXUIElement],
            !windows.isEmpty
        else {
            // Only log significant errors or empty list once at the end
            if axErr != AXError.success && axErr != AXError.noValue
                && axErr != AXError.attributeUnsupported
            {
                print(
                    "[WorkspaceService - findWindow] Error getting window list for \(bundleID) (PID: \(pid)): \(axErr.rawValue)"
                )
            }
            return nil  // Return nil if list is empty or error occurred
        }

        // Try to find the first 'standard' window (including minimized ones)
        if let standardWindow = windows.first(where: { window in
            var subrole: AnyObject?
            let subroleErr = AXUIElementCopyAttributeValue(
                window, kAXSubroleAttribute as CFString, &subrole)
            return subroleErr == AXError.success
                && (subrole as? String) == (kAXStandardWindowSubrole as String)
        }) {
            return standardWindow
        }

        // Check for minimized windows if no standard window found
        if let minimizedWindow = windows.first(where: { window in
            var minimizedValue: AnyObject?
            let minimizedErr = AXUIElementCopyAttributeValue(
                window, kAXMinimizedAttribute as CFString, &minimizedValue)
            if minimizedErr == AXError.success, let isMinimized = minimizedValue as? Bool,
                isMinimized
            {
                logger.debug(
                    "[WorkspaceService - findWindow] Found minimized window for \(bundleID)",
                    service: serviceName, category: .workspaces
                )
                return true
            }
            return false
        }) {
            return minimizedWindow
        }

        // Fallback to just the first window if no standard or minimized one found
        return windows.first
    }

    private func applyNormalizedFrame(windowElement: AXUIElement, savedInfo: WindowInfo) {
        // The saved frame is normalized relative to the display's frame
        let normalizedFrame = savedInfo.frame

        // Determine optimal timing based on app type for performance optimization
        let appTiming = calculateOptimalTiming(for: savedInfo.appBundleIdentifier)

        // Start building the log for this window
        var logBuilder = """
            WINDOW POSITIONING:

            NORMALIZED FRAME:
            - X: \(normalizedFrame.minX), Y: \(normalizedFrame.minY)
            - Width: \(normalizedFrame.width), Height: \(normalizedFrame.height)

            PERFORMANCE OPTIMIZATION:
            - App Type: \(appTiming.appType.rawValue)
            - Base Delay: \(Int(appTiming.baseDelay * 1000))ms
            - Verification Enabled: \(appTiming.useVerification)
            """

        // Find the appropriate display for this window using the same approach as the preview
        var targetDisplayInfo: DisplayInfo?
        var screenFoundMethod = "none"

        // Get all displays to ensure we have the latest arrangement
        let allDisplays = WindowLayoutManager.getAllDisplays()
        logBuilder += "\n\nDISPLAY DETECTION:"
        logBuilder += "\n- Available displays: \(allDisplays.count)"

        // First try to find the display by monitor ID using the same method as the preview
        if let savedMonitorID = savedInfo.monitorID {
            logBuilder += "\n- Saved Monitor ID: \(savedMonitorID.uuidString)"

            // Use WindowLayoutManager's findDisplayInfoByUUID which is used by the preview
            if let displayInfo = WindowLayoutManager.findDisplayInfoByUUID(savedMonitorID) {
                targetDisplayInfo = displayInfo
                screenFoundMethod = "uuid_match"
                logBuilder += "\n- Found display with ID: \(displayInfo.id) using UUID match"
            }
        }

        // If we couldn't find the display by UUID, try to find the main display
        if targetDisplayInfo == nil {
            targetDisplayInfo = allDisplays.first(where: { $0.isMain })
            if targetDisplayInfo != nil {
                screenFoundMethod = "main_display_fallback"
                logBuilder += "\n- Fallback to main display (coordinates at 0,0)"
            } else if let firstDisplay = allDisplays.first {
                // Last resort: use the first available display
                targetDisplayInfo = firstDisplay
                screenFoundMethod = "first_display_fallback"
                logBuilder += "\n- Fallback to first available display"
            }
        }

        guard let displayInfo = targetDisplayInfo else {
            // Log comprehensive error and return
            let errorMessage = """
                WINDOW POSITIONING ERROR:

                Could not determine target display for normalized frame \(normalizedFrame).
                Monitor ID: \(savedInfo.monitorID?.uuidString ?? "nil")
                Available displays: \(allDisplays.count)
                """

            logger.error(errorMessage, service: serviceName)
            return
        }

        // Check minimized state and unminimize if needed
        var isCurrentlyMinimized = false
        var minimizedValue: AnyObject?
        let minimizedGetErr = AXUIElementCopyAttributeValue(
            windowElement, kAXMinimizedAttribute as CFString, &minimizedValue)
        if minimizedGetErr == AXError.success {
            isCurrentlyMinimized = (minimizedValue as? Bool) ?? false
        }

        var minimizedRestoreSuccess = true
        if isCurrentlyMinimized {
            logger.info(
                "[WorkspaceService - applyNormalizedFrame] Unminimizing window for \(savedInfo.appBundleIdentifier ?? "unknown")",
                service: serviceName, category: .workspaces
            )
            let unminimizeErr = AXUIElementSetAttributeValue(
                windowElement, kAXMinimizedAttribute as CFString, kCFBooleanFalse)
            if unminimizeErr == AXError.success {
                Thread.sleep(forTimeInterval: 0.5)  // Allow time for unminimize transition
                logger.info(
                    "[WorkspaceService - applyNormalizedFrame] Successfully unminimized window",
                    service: serviceName, category: .workspaces
                )
            } else {
                minimizedRestoreSuccess = false
                logger.error(
                    "[WorkspaceService - applyNormalizedFrame] Failed to unminimize window: \(unminimizeErr.rawValue)",
                    service: serviceName, category: .workspaces
                )
            }
        }

        // Check fullscreen state
        var isCurrentlyFullscreen = false
        var fullscreenValue: AnyObject?
        let getErr = AXUIElementCopyAttributeValue(
            windowElement, "AXFullScreen" as CFString, &fullscreenValue)
        if getErr == AXError.success {
            isCurrentlyFullscreen = (fullscreenValue as? Bool) ?? false
        }

        // Exit fullscreen if needed
        var fullscreenExitSuccess = true
        if isCurrentlyFullscreen {
            let setErr = AXUIElementSetAttributeValue(
                windowElement, "AXFullScreen" as CFString, kCFBooleanFalse)
            if setErr == AXError.success {
                Thread.sleep(forTimeInterval: 0.3)  // Allow time for transition
            } else {
                fullscreenExitSuccess = false
            }
        }

        // Denormalize the frame to get global coordinates using WindowLayoutManager
        // This handles the Y-axis flipping and height difference offsets correctly
        let targetFrame = WindowLayoutManager.denormalizeFrame(normalizedFrame, in: displayInfo)

        logBuilder += "\n\nDENORMALIZED FRAME:"
        logBuilder += "\n- X: \(targetFrame.minX), Y: \(targetFrame.minY)"
        logBuilder += "\n- Width: \(targetFrame.width), Height: \(targetFrame.height)"

        // Use optimized timing approach: Size → Position → Size with smart delays
        // Maintains Rectangle's proven sequence but with performance optimization
        var newSize = targetFrame.size
        var axPosition = targetFrame.origin
        var sizeSetSuccess = true
        var positionSetSuccess = true

        let startTime = Date()

        // Step 1: Set Size first
        if let newAxSize = AXValueCreate(.cgSize, &newSize) {
            let sizeErr = AXUIElementSetAttributeValue(
                windowElement, kAXSizeAttribute as CFString, newAxSize)
            if sizeErr != AXError.success {
                sizeSetSuccess = false
                logger.warning(
                    "Failed to set window size (first attempt): \(sizeErr.rawValue)",
                    service: serviceName
                )
            } else {
                logger.debug(
                    "Successfully set window size (first attempt): \(newSize)",
                    service: serviceName
                )
            }
        }

        // Smart delay with verification for size setting
        if appTiming.useVerification {
            smartDelay(duration: appTiming.baseDelay) {
                self.verifyWindowSize(windowElement: windowElement, expectedSize: newSize)
            }
        } else {
            Thread.sleep(forTimeInterval: appTiming.baseDelay)
        }

        // Step 2: Set Position
        if let newAxPosition = AXValueCreate(.cgPoint, &axPosition) {
            let posErr = AXUIElementSetAttributeValue(
                windowElement, kAXPositionAttribute as CFString, newAxPosition)
            if posErr != AXError.success {
                positionSetSuccess = false
                logger.warning(
                    "Failed to set window position: \(posErr.rawValue)",
                    service: serviceName
                )
            } else {
                logger.debug(
                    "Successfully set window position: \(axPosition)",
                    service: serviceName
                )
            }
        }

        // Standard delay for position (no verification needed)
        Thread.sleep(forTimeInterval: appTiming.baseDelay)

        // Step 3: Set Size again to ensure it's applied correctly (critical for width restoration)
        if let newAxSize = AXValueCreate(.cgSize, &newSize) {
            let finalSizeErr = AXUIElementSetAttributeValue(
                windowElement, kAXSizeAttribute as CFString, newAxSize)
            if finalSizeErr != AXError.success {
                logger.warning(
                    "Failed to set window size (final attempt): \(finalSizeErr.rawValue)",
                    service: serviceName
                )
            } else {
                logger.debug(
                    "Successfully set window size (final attempt): \(newSize)",
                    service: serviceName
                )
            }
        }

        // Final minimal delay (reduced for performance)
        Thread.sleep(forTimeInterval: appTiming.baseDelay * 0.5)

        let totalTime = Date().timeIntervalSince(startTime)
        logger.debug(
            "Window positioning completed in \(Int(totalTime * 1000))ms (vs 150ms baseline)",
            service: serviceName
        )

        // Handle desired fullscreen state AFTER setting bounds
        var fullscreenEnterSuccess = true
        if savedInfo.isFullscreen && !isCurrentlyFullscreen {
            let setErr = AXUIElementSetAttributeValue(
                windowElement, "AXFullScreen" as CFString, kCFBooleanTrue)
            if setErr != AXError.success {
                fullscreenEnterSuccess = false
            }
        }

        // Complete the log with additional information
        logBuilder += "\n\nAPP INFO:"
        logBuilder += "\n- Bundle ID: \(savedInfo.appBundleIdentifier ?? "unknown")"
        logBuilder += "\n- Fullscreen: \(savedInfo.isFullscreen)"
        logBuilder += "\n- Z-Order: \(savedInfo.zOrder)"

        logBuilder += "\n\nMONITOR INFO:"
        logBuilder += "\n- Monitor ID: \(savedInfo.monitorID?.uuidString ?? "nil")"
        logBuilder += "\n- Screen Found Method: \(screenFoundMethod)"
        logBuilder += "\n- Screen ID: \(displayInfo.id)"
        logBuilder += "\n- Is Main Screen: \(displayInfo.isMain)"
        logBuilder += "\n- Scale Factor: \(displayInfo.scaleFactor)"
        logBuilder += "\n- Screen Frame: \(displayInfo.frame)"
        logBuilder += "\n- Visible Frame: \(displayInfo.visibleFrame)"

        logBuilder += "\n\nOPERATIONS:"
        logBuilder +=
            "\n- Minimized Restore: \(isCurrentlyMinimized ? (minimizedRestoreSuccess ? "Success" : "Failed") : "Not Needed")"
        logBuilder +=
            "\n- Fullscreen Exit: \(isCurrentlyFullscreen ? (fullscreenExitSuccess ? "Success" : "Failed") : "Not Needed")"
        logBuilder += "\n- Size Set (First): \(sizeSetSuccess ? "Success" : "Failed")"
        logBuilder += "\n- Position Set: \(positionSetSuccess ? "Success" : "Failed")"
        logBuilder += "\n- Size Set (Final): Applied with optimized timing"
        logBuilder += "\n- App Type: \(appTiming.appType.rawValue)"
        logBuilder += "\n- Base Delay: \(Int(appTiming.baseDelay * 1000))ms"
        logBuilder += "\n- Verification: \(appTiming.useVerification ? "Enabled" : "Disabled")"
        logBuilder +=
            "\n- Fullscreen Enter: \(savedInfo.isFullscreen ? (fullscreenEnterSuccess ? "Success" : "Failed") : "Not Needed")"

        // Use the built log as the final log message
        let logMessage = logBuilder

        logger.info(logMessage, service: serviceName, category: .workspaces)
    }

    // Cancel ongoing restoration
    func cancelRestoration() {
        if let handler = restorationHandler {
            handler.cancel()
        }
        DispatchQueue.main.async {
            self.isRestoring = false
            self.restorationStatusMessage = "Restoration cancelled."

            // Log cancellation with data preservation confirmation
            self.logger.info(
                "Workspace restoration cancelled by user - original workspace data preserved",
                service: self.serviceName,
                category: .workspaces
            )
        }
    }

    // MARK: - Enhanced Error Handling

    /// Handle restoration failures while preserving workspace data integrity
    private func handleRestorationFailure(
        workspace: Workspace,
        error: String,
        preserveCache: Bool = false
    ) {
        logger.error(
            "Workspace restoration failed for '\(workspace.name)': \(error). Original workspace data preserved.",
            service: serviceName,
            category: .workspaces
        )

        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            self.isRestoring = false
            self.restorationStatusMessage = "Restoration failed - original data preserved."

            // Show user-friendly error message
            ToastManager.shared.showError(
                title: "Restoration Failed",
                message:
                    "Could not restore '\(workspace.name)'. Your original workspace data is safe and unchanged.",
                duration: 5.0
            )
        }
    }

    /// Log detailed restoration attempt information for debugging
    private func logRestorationAttempt(
        workspace: Workspace,
        phase: String,
        details: String
    ) {
        let logMessage = """
            RESTORATION ATTEMPT - \(phase.uppercased()):
            Workspace: '\(workspace.name)' (ID: \(workspace.id?.uuidString ?? "nil"))
            Windows: \(workspace.windowInfos.count)
            Details: \(details)
            Data Integrity: Original workspace data preserved throughout process
            """

        logger.info(logMessage, service: serviceName, category: .workspaces)
    }

}
