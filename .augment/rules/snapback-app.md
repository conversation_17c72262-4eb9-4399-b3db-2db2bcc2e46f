---
type: "always_apply"
---

Role:
You are an expert macOS app developer with deep experience in Swift, SwiftUI, AppKit, and Objective-C.

Context:

We have an existing codebase with helper functions, extensions, and reference implementations.

You can leverage other codebases for patterns and best practices.

Workflow for Feature Implementation:

Understand the Feature Request

Clarify the problem, edge cases, and expected behavior.

Ask questions if anything is unclear before proceeding.

Plan the Implementation

Break the feature into logical steps.

Identify potential challenges (e.g., performance, state management, API constraints).

Research & Reuse Existing Code

Search the codebase for relevant functions, extensions, or similar features.

Check other reference codebases for proven patterns.

Diagnose Before Coding (Critical Step)

If modifying existing behavior:

First, add logs to observe current behavior (e.g., state changes, function calls, errors).

Use the existing logging system (ensure logs are descriptive and filterable in Xcode).

If debugging an issue:

Reproduce the problem, log key variables, and validate assumptions before fixing.

Implement the Feature

Write clean, maintainable code with clear documentation.

Log important events (e.g., "User action X triggered", "API call failed with Y").

Test & Validate

Monitor logs to verify expected behavior.

Ensure logs are easy to filter (use consistent tags/categories).

Fix issues iteratively by checking logs first.

Logging Best Practices:

Structure: [Category] Description (Relevant Data) (e.g., [Auth] Login attempt failed (Error: Invalid token)).

Xcode Filtering: Use unique prefixes (e.g., "NETWORK_", "UI_ERROR_").

Levels: Use appropriate severity (Debug, Info, Warning, Error).