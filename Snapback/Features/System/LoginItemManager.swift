import Foundation
import ServiceManagement

class LoginItemManager {
    static func setStartAtLogin(_ enable: Bool) {
        if #available(macOS 13.0, *) {
            // Use the new SMAppService API for macOS 13+
            if enable {
                try? SMAppService.mainApp.register()
            } else {
                try? SMAppService.mainApp.unregister()
            }
        } else {
            // Use the legacy LSSharedFileList API for macOS 12.4
            setStartAtLoginLegacy(enable)
        }
    }

    static func isStartingAtLogin() -> Bool {
        if #available(macOS 13.0, *) {
            return SMAppService.mainApp.status == .enabled
        } else {
            return isStartingAtLoginLegacy()
        }
    }

    // MARK: - Legacy Implementation for macOS 12.4

    private static func setStartAtLoginLegacy(_ enable: Bool) {
        // For macOS 12.4, we'll use a simpler approach that doesn't rely on deprecated APIs
        // This is a basic implementation that uses UserDefaults to track the preference
        // In a production app, you might want to use more sophisticated methods

        UserDefaults.standard.set(enable, forKey: "StartAtLogin")

        // Note: This is a simplified implementation for macOS 12.4 compatibility
        // The actual login item registration would require more complex implementation
        // using deprecated LSSharedFileList APIs or other methods
        print("Login at startup preference set to: \(enable) (macOS 12.4 compatibility mode)")
    }

    private static func isStartingAtLoginLegacy() -> Bool {
        // For macOS 12.4 compatibility, read from UserDefaults
        return UserDefaults.standard.bool(forKey: "StartAtLogin")
    }
}
