import Foundation
import SwiftUI

/// Manages license validation and storage for the Snapback app
class LicenseManager: ObservableObject {
    static let shared = LicenseManager()

    // MARK: - Access Control Properties

    /// Computed property that determines if the user has full access
    /// When license system is disabled, always returns true
    /// When license system is enabled, checks actual license status (pro license only)
    var hasFullAccess: Bool {
        if FeatureFlags.isLicenseSystemDisabled {
            return true
        }
        return licenseStatus == .valid
    }

    /// Computed property that indicates if the license system is active
    /// Used by UI components to determine whether to show license-related elements
    var isLicenseSystemEnabled: Bool {
        return !FeatureFlags.isLicenseSystemDisabled
    }

    // MARK: - Freemium Model Properties

    /// Maximum number of workspaces allowed for free users
    static let maxFreeWorkspaces = 3

    /// Computed property that determines if the user can save unlimited workspaces
    /// Free users are limited to 3 workspaces, pro license users have unlimited access
    var hasUnlimitedWorkspaces: Bool {
        if FeatureFlags.isLicenseSystemDisabled {
            return true
        }
        return licenseStatus == .valid
    }

    /// Published properties for UI binding
    @Published var licenseStatus: LicenseStatus = .unlicensed
    @Published var licenseKey: String = ""
    @Published var userEmail: String = ""
    @Published var licenseInfo: LicenseInfo?
    @Published var isValidating: Bool = false
    @Published var lastError: String?

    // Trial-related properties removed - simplified license system with pro licenses only

    /// Logger for license-related events
    private let logger = LoggingService.shared
    private let serviceName = "LicenseManager"

    /// UserDefaults keys for persistence
    private enum PersistenceKeys {
        static let licenseKey = "SnapbackLicenseKey"
        static let userEmail = "SnapbackUserEmail"
        static let licenseStatus = "SnapbackLicenseStatus"
        static let licenseInfo = "SnapbackLicenseInfo"
        static let lastValidation = "SnapbackLastLicenseValidation"

        // Trial-related keys removed - simplified license system
    }

    // Trial configuration and computed properties removed - simplified license system

    private init() {
        logger.info("Initializing LicenseManager", service: serviceName)

        // Check if license system is disabled
        if FeatureFlags.isLicenseSystemDisabled {
            logger.info(
                "🚫 LICENSE SYSTEM DISABLED: All features available without restrictions",
                service: serviceName)
            // Set status to valid to ensure hasFullAccess returns true
            licenseStatus = .valid
            // Create a placeholder license info for UI display
            licenseInfo = LicenseInfo(
                licenseType: "Full Access",
                registeredUser: "Licensed User",
                email: "",
                expirationDate: nil,
                features: [
                    "All Workspace Features", "Unlimited Workspaces",
                    "Window Management", "Keyboard Shortcuts", "CloudKit Sync",
                ]
            )
            return  // Skip all license-related initialization
        }

        loadPersistedLicense()

        // Validate license on startup if we have one using offline-capable validation
        if !licenseKey.isEmpty {
            Task {
                await validateLicenseWithCaching(silent: true)
            }
        }
    }

    // MARK: - Public Methods

    /// Validate the current license key
    @MainActor
    func validateLicense(silent: Bool = false, registerDevice: Bool = false) async {
        // If license system is disabled, do nothing
        if FeatureFlags.isLicenseSystemDisabled {
            logger.info(
                "🚫 LICENSE SYSTEM DISABLED: Skipping license validation", service: serviceName)
            return
        }

        guard !licenseKey.isEmpty else {
            if !silent {
                lastError = "Please enter a license key"
            }
            return
        }

        isValidating = true
        lastError = nil

        logger.info("Validating license key: \(licenseKey.prefix(8))...", service: serviceName)

        do {
            // Clean the license key for API validation (remove dashes)
            let cleanedKey = licenseKey.replacingOccurrences(of: "-", with: "")

            // Use device metadata only when user manually enters license (registerDevice = true)
            // For regular validation (app startup), don't send device metadata to reduce data transmission
            let response = try await LicenseAPIService.shared.validateLicenseAndRegisterDevice(
                licenseKey: cleanedKey, includeDeviceMetadata: registerDevice)

            if response.valid {
                // Determine license status based on license type and expiration
                let licenseType = response.effectiveLicenseType ?? "License"
                let expirationDate = response.effectiveExpiresAt
                let email = response.effectiveEmail ?? userEmail

                // Set license status - simplified for pro-only model
                licenseStatus = .valid

                // Create license info from new API response with backward compatibility
                licenseInfo = LicenseInfo(
                    licenseType: licenseType,
                    registeredUser: email.isEmpty ? "Licensed User" : email,
                    email: email.isEmpty ? "Licensed User" : email,
                    expirationDate: expirationDate,
                    features: [
                        "All Workspace Features", "Unlimited Workspaces",
                        "Window Management", "Keyboard Shortcuts", "CloudKit Sync",
                    ]
                )

                // Store additional license metadata if available from new API structure
                if let licenseId = response.effectiveLicenseId {
                    UserDefaults.standard.set(licenseId, forKey: "SnapbackLicenseId")
                    logger.info("License ID stored: \(licenseId)", service: serviceName)
                }

                if let createdAt = response.effectiveCreatedAt {
                    UserDefaults.standard.set(createdAt, forKey: "SnapbackLicenseCreatedAt")
                }

                if let updatedAt = response.effectiveUpdatedAt {
                    UserDefaults.standard.set(updatedAt, forKey: "SnapbackLicenseUpdatedAt")
                }

                // Store device token if provided (for future API calls)
                if let deviceToken = response.deviceToken {
                    UserDefaults.standard.set(deviceToken, forKey: "SnapbackDeviceToken")
                    logger.info("Device token stored for future API calls", service: serviceName)
                }

                // Update email if provided in response
                if !email.isEmpty && email != userEmail {
                    userEmail = email
                }

                logger.info(
                    "License validation successful - type: \(licenseType), devices: \(response.effectiveDevicesUsed ?? 0)/\(response.effectiveMaxDevices ?? 0)",
                    service: serviceName)

                // Trial info logging removed - simplified license system
            } else {
                licenseStatus = .invalid
                licenseInfo = nil
                lastError = response.message ?? "Invalid license key"
                logger.warning(
                    "License validation failed: \(response.message ?? "invalid key")",
                    service: serviceName)
            }

            // Persist the results
            persistLicense()

            // Notify about license status change
            postLicenseStatusChangeNotification()

        } catch {
            licenseStatus = .invalid
            licenseInfo = nil
            lastError = error.localizedDescription
            logger.error("License validation error: \(error)", service: serviceName)
        }

        isValidating = false
    }

    /// Validate license using the new offline-capable caching system
    @MainActor
    func validateLicenseWithCaching(silent: Bool = false, forceServerCheck: Bool = false) async {
        // If license system is disabled, do nothing
        if FeatureFlags.isLicenseSystemDisabled {
            logger.info(
                "🚫 LICENSE SYSTEM DISABLED: Skipping license validation", service: serviceName)
            return
        }

        guard !licenseKey.isEmpty else {
            if !silent {
                lastError = "Please enter a license key"
            }
            return
        }

        isValidating = true
        lastError = nil

        logger.info(
            "🔍 Validating license with caching: \(licenseKey.prefix(8))...", service: serviceName)

        // Use the new caching system with graceful degradation
        let result = await LicenseCacheManager.shared.validateLicense(
            licenseKey: licenseKey,
            strategy: .gracefulDegradation,
            forceServerCheck: forceServerCheck
        )

        // Update license manager state based on result
        licenseStatus = result.licenseStatus
        licenseInfo = result.licenseInfo

        // Update userEmail property if we got email from the result
        if let licenseInfo = result.licenseInfo, !licenseInfo.email.isEmpty {
            userEmail = licenseInfo.email
        }

        if result.licenseStatus == .valid {
            lastError = nil
            logger.info(
                "✅ License validation successful (source: \(result.source))",
                service: serviceName
            )

            if result.isGracePeriod {
                logger.info(
                    "⏰ Using cached license in grace period (\(result.remainingGraceDays ?? 0) days remaining)",
                    service: serviceName
                )
            }
        } else {
            lastError = result.errorMessage ?? "License validation failed"
            logger.warning(
                "❌ License validation failed: \(result.errorMessage ?? "unknown error")",
                service: serviceName
            )
        }

        // Persist the results
        persistLicense()

        // Notify about license status change
        postLicenseStatusChangeNotification()

        isValidating = false
    }

    /// Get the registered user email for UI display (with backup fallback)
    /// This ensures consistent email display even when license cache is corrupted
    func getRegisteredUserEmail() -> String {
        // First try the current license info
        if let licenseInfo = licenseInfo, !licenseInfo.email.isEmpty {
            return licenseInfo.email
        }

        // Fall back to the stored userEmail property
        if !userEmail.isEmpty {
            return userEmail
        }

        // Finally, try the backup email from cache manager
        if let backupEmail = LicenseCacheManager.shared.getBackupEmail(), !backupEmail.isEmpty {
            logger.debug(
                "📧 Using backup email for UI display: \(backupEmail)", service: serviceName)
            return backupEmail
        }

        return "Licensed User"
    }

    /// Get the registered user name for UI display (with backup fallback)
    func getRegisteredUserName() -> String {
        // First try the current license info
        if let licenseInfo = licenseInfo, !licenseInfo.registeredUser.isEmpty {
            return licenseInfo.registeredUser
        }

        // Fall back to backup registered user from cache manager
        if let backupUser = LicenseCacheManager.shared.getBackupRegisteredUser(),
            !backupUser.isEmpty
        {
            logger.debug(
                "👤 Using backup registered user for UI display: \(backupUser)", service: serviceName
            )
            return backupUser
        }

        // Finally, try using the email as the name
        let email = getRegisteredUserEmail()
        return email != "Licensed User" ? email : "Licensed User"
    }

    /// Set a new license key and validate it
    @MainActor
    func setLicenseKey(_ key: String) async {
        // If license system is disabled, do nothing
        if FeatureFlags.isLicenseSystemDisabled {
            logger.info(
                "🚫 LICENSE SYSTEM DISABLED: Ignoring license key input", service: serviceName)
            return
        }

        let cleanKey = key.trimmingCharacters(in: .whitespacesAndNewlines)
        licenseKey = formatLicenseKeyForDisplay(cleanKey)

        if !cleanKey.isEmpty {
            await validateLicenseWithCaching(forceServerCheck: true)  // Force server check when user manually enters license
        } else {
            licenseStatus = .unlicensed
            licenseInfo = nil
            lastError = nil
            persistLicense()
        }
    }

    /// Set and validate a license key with email
    @MainActor
    func setLicenseKeyAndEmail(_ key: String, email: String) async {
        // If license system is disabled, do nothing
        if FeatureFlags.isLicenseSystemDisabled {
            logger.info(
                "🚫 LICENSE SYSTEM DISABLED: Ignoring license key and email input",
                service: serviceName)
            return
        }

        let trimmedKey = key.trimmingCharacters(in: .whitespacesAndNewlines)
        let trimmedEmail = email.trimmingCharacters(in: .whitespacesAndNewlines).lowercased()

        logger.info(
            "Setting license key and email: \(trimmedKey.prefix(8))... for \(trimmedEmail)",
            service: serviceName)

        // Validate email format
        guard isValidEmail(trimmedEmail) else {
            lastError = "Please enter a valid email address"
            return
        }

        // Clear any previous error
        lastError = nil

        // Update the key and email (format the key for display)
        licenseKey = formatLicenseKeyForDisplay(trimmedKey)
        userEmail = trimmedEmail

        if !trimmedKey.isEmpty {
            await validateLicenseWithCaching(forceServerCheck: true)  // Force server check when user manually enters license
        } else {
            licenseStatus = .unlicensed
            licenseInfo = nil
            lastError = nil
            persistLicense()
        }
    }

    /// Clear the current license
    func clearLicense() {
        logger.info("Clearing license", service: serviceName)

        // Clear license-related properties
        licenseKey = ""
        userEmail = ""
        licenseStatus = .unlicensed
        licenseInfo = nil
        lastError = nil

        // Clear from UserDefaults - license data only (trial functionality removed)
        UserDefaults.standard.removeObject(forKey: PersistenceKeys.licenseKey)
        UserDefaults.standard.removeObject(forKey: PersistenceKeys.userEmail)
        UserDefaults.standard.removeObject(forKey: PersistenceKeys.licenseStatus)
        UserDefaults.standard.removeObject(forKey: PersistenceKeys.licenseInfo)
        UserDefaults.standard.removeObject(forKey: PersistenceKeys.lastValidation)

        // Clear device token
        UserDefaults.standard.removeObject(forKey: "SnapbackDeviceToken")

        UserDefaults.standard.synchronize()

        logger.info(
            "🔍 LICENSE CLEAR DEBUG: All license data cleared", service: serviceName)

        // Notify about license status change
        postLicenseStatusChangeNotification()
    }

    // MARK: - Trial Management Removed
    // Trial functionality has been completely removed from the simplified license system

    /// Validate email format
    private func isValidEmail(_ email: String) -> Bool {
        let emailRegex = "^[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$"
        let emailPredicate = NSPredicate(format: "SELF MATCHES %@", emailRegex)
        return emailPredicate.evaluate(with: email)
    }

    /// Post notification about license status change
    private func postLicenseStatusChangeNotification() {
        NotificationCenter.default.post(
            name: NSNotification.Name("LicenseStatusChanged"),
            object: licenseStatus
        )
    }

    // MARK: - Private Methods

    /// Format license key for display as XXXX-XXXX-XXXX-XXXX
    private func formatLicenseKeyForDisplay(_ key: String) -> String {
        // Remove existing dashes and convert to uppercase
        let cleaned = key.replacingOccurrences(of: "-", with: "").uppercased()

        // Format as XXXX-XXXX-XXXX-XXXX
        var formatted = ""
        for (index, character) in cleaned.enumerated() {
            if index > 0 && index % 4 == 0 {
                formatted += "-"
            }
            formatted += String(character)
        }

        return formatted
    }

    /// Load persisted license information
    private func loadPersistedLicense() {
        licenseKey = UserDefaults.standard.string(forKey: PersistenceKeys.licenseKey) ?? ""
        userEmail = UserDefaults.standard.string(forKey: PersistenceKeys.userEmail) ?? ""

        if let statusRaw = UserDefaults.standard.string(forKey: PersistenceKeys.licenseStatus),
            let status = LicenseStatus(rawValue: statusRaw)
        {
            licenseStatus = status
        }

        if let infoData = UserDefaults.standard.data(forKey: PersistenceKeys.licenseInfo) {
            do {
                let decoder = JSONDecoder()
                // Note: For persisted data, we use standard decoding since it was encoded locally
                // The flexible date decoding is primarily for API responses
                let info = try decoder.decode(LicenseInfo.self, from: infoData)
                licenseInfo = info
            } catch {
                logger.warning(
                    "Failed to decode persisted license info: \(error)", service: serviceName)
                // Clear corrupted data
                UserDefaults.standard.removeObject(forKey: PersistenceKeys.licenseInfo)
            }
        }

        // DIAGNOSTIC: Log detailed license loading info
        logger.info(
            "🔍 LICENSE LOAD DEBUG: Loaded persisted license - Status: \(licenseStatus)",
            service: serviceName)
        logger.info(
            "🔍 LICENSE LOAD DEBUG: License key: '\(licenseKey.isEmpty ? "EMPTY" : "SET (\(licenseKey.count) chars)")'",
            service: serviceName)
        logger.info(
            "🔍 LICENSE LOAD DEBUG: User email: '\(userEmail.isEmpty ? "EMPTY" : userEmail)'",
            service: serviceName)
    }

    /// Persist license information to UserDefaults
    private func persistLicense() {
        UserDefaults.standard.set(licenseKey, forKey: PersistenceKeys.licenseKey)
        UserDefaults.standard.set(userEmail, forKey: PersistenceKeys.userEmail)
        UserDefaults.standard.set(licenseStatus.rawValue, forKey: PersistenceKeys.licenseStatus)
        UserDefaults.standard.set(
            Date().timeIntervalSince1970, forKey: PersistenceKeys.lastValidation)

        if let info = licenseInfo,
            let infoData = try? JSONEncoder().encode(info)
        {
            UserDefaults.standard.set(infoData, forKey: PersistenceKeys.licenseInfo)
        } else {
            UserDefaults.standard.removeObject(forKey: PersistenceKeys.licenseInfo)
        }

        UserDefaults.standard.synchronize()

        // DIAGNOSTIC: Log license persistence
        logger.info("🔍 LICENSE PERSIST DEBUG: License information persisted", service: serviceName)
        logger.info("🔍 LICENSE PERSIST DEBUG: Status: \(licenseStatus)", service: serviceName)
        logger.info(
            "🔍 LICENSE PERSIST DEBUG: Key: '\(licenseKey.isEmpty ? "EMPTY" : "SET")'",
            service: serviceName)
        logger.info(
            "🔍 LICENSE PERSIST DEBUG: Email: '\(userEmail.isEmpty ? "EMPTY" : userEmail)'",
            service: serviceName)
    }

}

// MARK: - Supporting Types

/// License status enumeration - simplified for pro-only model
enum LicenseStatus: String, CaseIterable, Codable {
    case unlicensed = "unlicensed"
    case valid = "valid"
    case invalid = "invalid"
    case expired = "expired"

    var displayName: String {
        switch self {
        case .unlicensed: return "No License"
        case .valid: return "Licensed"
        case .invalid: return "Invalid"
        case .expired: return "Expired"
        }
    }

    var color: Color {
        switch self {
        case .unlicensed: return .secondary
        case .valid: return .green
        case .invalid: return .red
        case .expired: return .orange
        }
    }

    var isActive: Bool {
        return self == .valid
    }
}

/// License information structure
struct LicenseInfo: Codable {
    let licenseType: String
    let registeredUser: String
    let email: String
    let expirationDate: Date?
    let features: [String]

    var isExpired: Bool {
        guard let expirationDate = expirationDate else { return false }
        return expirationDate < Date()
    }

    var expirationDisplayText: String {
        guard let expirationDate = expirationDate else { return "Never expires" }

        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none

        return formatter.string(from: expirationDate)
    }

    /// Get stored device token for API authentication
    func getDeviceToken() -> String? {
        return UserDefaults.standard.string(forKey: "SnapbackDeviceToken")
    }

    /// Get stored license ID from new API structure
    func getLicenseId() -> String? {
        return UserDefaults.standard.string(forKey: "SnapbackLicenseId")
    }

    /// Get stored license creation date from new API structure
    func getCreatedAt() -> Date? {
        return UserDefaults.standard.object(forKey: "SnapbackLicenseCreatedAt") as? Date
    }

    /// Get stored license update date from new API structure
    func getUpdatedAt() -> Date? {
        return UserDefaults.standard.object(forKey: "SnapbackLicenseUpdatedAt") as? Date
    }
}

/// License validation result
struct LicenseValidationResult {
    let status: LicenseStatus
    let info: LicenseInfo?
    let message: String?
}
