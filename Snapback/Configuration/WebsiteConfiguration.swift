import AppKit
import Foundation

/// Centralized configuration for all website URLs used throughout the app
/// Handles environment-aware URL management for development and production
struct WebsiteConfiguration {

    // MARK: - Singleton

    static let shared = WebsiteConfiguration()

    private init() {}

    // MARK: - Base URL Configuration

    /// Base URL for the marketing website
    /// - Development: http://localhost:3001
    /// - Production: https://snapbackapp.com
    private var baseURL: String {
        #if DEBUG
            return "http://localhost:3001"
        #else
            return "https://snapbackapp.com"
        #endif
    }

    // MARK: - Website URLs

    /// Main website URL
    var websiteURL: String {
        return baseURL
    }

    /// Purchase/Pricing page URL
    var purchaseURL: String {
        return "\(baseURL)/#pricing"
    }

    /// Privacy policy page URL
    var privacyPolicyURL: String {
        return "\(baseURL)/#privacy"
    }

    /// Help/Support page URL
    var helpURL: String {
        return "\(baseURL)/#help"
    }

    /// Terms of service page URL
    var termsOfServiceURL: String {
        return "\(baseURL)/#terms"
    }

    /// Contact/Support page URL
    var contactURL: String {
        return "\(baseURL)/#contact"
    }

    /// Documentation page URL
    var documentationURL: String {
        return "\(baseURL)/#docs"
    }

    /// FAQ page URL
    var faqURL: String {
        return "\(baseURL)/#faq"
    }

    // MARK: - Convenience Methods

    /// Get URL for a specific page fragment
    /// - Parameter fragment: The page fragment (without #)
    /// - Returns: Complete URL with fragment
    func url(for fragment: String) -> String {
        let cleanFragment = fragment.hasPrefix("#") ? String(fragment.dropFirst()) : fragment
        return "\(baseURL)/#\(cleanFragment)"
    }

    /// Get URL object for a specific page fragment
    /// - Parameter fragment: The page fragment (without #)
    /// - Returns: URL object or nil if invalid
    func urlObject(for fragment: String) -> URL? {
        return URL(string: url(for: fragment))
    }

    /// Get the main website URL as a URL object
    var websiteURLObject: URL? {
        return URL(string: websiteURL)
    }

    /// Get the purchase URL as a URL object
    var purchaseURLObject: URL? {
        return URL(string: purchaseURL)
    }

    /// Get the privacy policy URL as a URL object
    var privacyPolicyURLObject: URL? {
        return URL(string: privacyPolicyURL)
    }

    /// Get the help URL as a URL object
    var helpURLObject: URL? {
        return URL(string: helpURL)
    }

    // MARK: - Environment Information

    /// Current environment description
    var environmentDescription: String {
        #if DEBUG
            return "Development"
        #else
            return "Production"
        #endif
    }

    /// Check if running in development environment
    var isDevelopment: Bool {
        #if DEBUG
            return true
        #else
            return false
        #endif
    }

    /// Check if running in production environment
    var isProduction: Bool {
        return !isDevelopment
    }
}

// MARK: - Convenience Extensions

extension WebsiteConfiguration {

    /// Open a website URL in the default browser
    /// - Parameter fragment: The page fragment to open (optional)
    /// - Returns: True if successfully opened, false otherwise
    @discardableResult
    func openInBrowser(fragment: String? = nil) -> Bool {
        let urlString = fragment != nil ? url(for: fragment!) : websiteURL

        guard let url = URL(string: urlString) else {
            print("❌ WebsiteConfiguration: Invalid URL - \(urlString)")
            return false
        }

        let success = NSWorkspace.shared.open(url)

        if success {
            print("🌐 WebsiteConfiguration: Opened URL - \(urlString)")
        } else {
            print("❌ WebsiteConfiguration: Failed to open URL - \(urlString)")
        }

        return success
    }

    /// Open the purchase page in the default browser
    @discardableResult
    func openPurchasePage() -> Bool {
        return openInBrowser(fragment: "pricing")
    }

    /// Open the privacy policy page in the default browser
    @discardableResult
    func openPrivacyPolicy() -> Bool {
        return openInBrowser(fragment: "privacy")
    }

    /// Open the help page in the default browser
    @discardableResult
    func openHelpPage() -> Bool {
        return openInBrowser(fragment: "help")
    }
}

// MARK: - Debug Information

#if DEBUG
    extension WebsiteConfiguration {

        /// Print all configured URLs for debugging
        func printAllURLs() {
            print("🔧 WebsiteConfiguration Debug Information")
            print("Environment: \(environmentDescription)")
            print("Base URL: \(baseURL)")
            print("Website URL: \(websiteURL)")
            print("Purchase URL: \(purchaseURL)")
            print("Privacy Policy URL: \(privacyPolicyURL)")
            print("Help URL: \(helpURL)")
            print("Terms of Service URL: \(termsOfServiceURL)")
            print("Contact URL: \(contactURL)")
            print("Documentation URL: \(documentationURL)")
            print("FAQ URL: \(faqURL)")
        }

        /// Test the URL configuration system
        func testConfiguration() {
            print("🧪 Testing WebsiteConfiguration...")
            printAllURLs()

            // Test URL object creation
            print("\n🔗 Testing URL Object Creation:")
            print("Purchase URL Object: \(purchaseURLObject?.absoluteString ?? "nil")")
            print("Privacy Policy URL Object: \(privacyPolicyURLObject?.absoluteString ?? "nil")")
            print("Help URL Object: \(helpURLObject?.absoluteString ?? "nil")")

            // Test custom fragment URL
            print("\n🎯 Testing Custom Fragment URLs:")
            print("Custom 'features' URL: \(url(for: "features"))")
            print("Custom 'download' URL: \(url(for: "download"))")

            print("✅ WebsiteConfiguration test completed!")
        }
    }
#endif
