<?xml version="1.0" encoding="UTF-8"?>
<!--Generator: Apple Native CoreSVG 341-->
<!DOCTYPE svg
PUBLIC "-//W3C//DTD SVG 1.1//EN"
       "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 3300 2200">
 <!--glyph: "1008CB", point size: 100.0, font version: "20.0d10e1", template writer version: "138.0.0"-->
 <style>.SFSymbolsPreviewWireframe {fill:none;opacity:1.0;stroke:black;stroke-width:0.5}
</style>
 <g id="Notes">
  <rect height="2200" id="artboard" style="fill:white;opacity:1" width="3300" x="0" y="0"/>
  <line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="292" y2="292"/>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 322)">Weight/Scale Variations</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 559.711 322)">Ultralight</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 856.422 322)">Thin</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1153.13 322)">Light</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1449.84 322)">Regular</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1746.56 322)">Medium</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2043.27 322)">Semibold</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2339.98 322)">Bold</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2636.69 322)">Heavy</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2933.4 322)">Black</text>
  <line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1903" y2="1903"/>
  <g transform="matrix(0.2 0 0 0.2 263 1933)">
   <path d="m46.2402 4.15039c21.7773 0 39.4531-17.627 39.4531-39.4043s-17.6758-39.4043-39.4531-39.4043c-21.7285 0-39.4043 17.627-39.4043 39.4043s17.6758 39.4043 39.4043 39.4043Zm0-7.42188c-17.6758 0-31.9336-14.3066-31.9336-31.9824s14.2578-31.9824 31.9336-31.9824 31.9824 14.3066 31.9824 31.9824-14.3066 31.9824-31.9824 31.9824Zm-17.9688-31.9824c0 2.14844 1.51367 3.61328 3.75977 3.61328h10.498v10.5957c0 2.19727 1.46484 3.71094 3.61328 3.71094 2.24609 0 3.71094-1.51367 3.71094-3.71094v-10.5957h10.5957c2.19727 0 3.71094-1.46484 3.71094-3.61328 0-2.19727-1.51367-3.71094-3.71094-3.71094h-10.5957v-10.5469c0-2.24609-1.46484-3.75977-3.71094-3.75977-2.14844 0-3.61328 1.51367-3.61328 3.75977v10.5469h-10.498c-2.24609 0-3.75977 1.51367-3.75977 3.71094Z"/>
  </g>
  <g transform="matrix(0.2 0 0 0.2 281.506 1933)">
   <path d="m58.5449 14.5508c27.4902 0 49.8047-22.3145 49.8047-49.8047s-22.3145-49.8047-49.8047-49.8047-49.8047 22.3145-49.8047 49.8047 22.3145 49.8047 49.8047 49.8047Zm0-8.30078c-22.9492 0-41.5039-18.5547-41.5039-41.5039s18.5547-41.5039 41.5039-41.5039 41.5039 18.5547 41.5039 41.5039-18.5547 41.5039-41.5039 41.5039Zm-22.6562-41.5039c0 2.39258 1.66016 4.00391 4.15039 4.00391h14.3555v14.4043c0 2.44141 1.66016 4.15039 4.05273 4.15039 2.44141 0 4.15039-1.66016 4.15039-4.15039v-14.4043h14.4043c2.44141 0 4.15039-1.61133 4.15039-4.00391 0-2.44141-1.70898-4.15039-4.15039-4.15039h-14.4043v-14.3555c0-2.49023-1.70898-4.19922-4.15039-4.19922-2.39258 0-4.05273 1.70898-4.05273 4.19922v14.3555h-14.3555c-2.49023 0-4.15039 1.70898-4.15039 4.15039Z"/>
  </g>
  <g transform="matrix(0.2 0 0 0.2 304.924 1933)">
   <path d="m74.8535 28.3203c35.1074 0 63.623-28.4668 63.623-63.5742s-28.5156-63.623-63.623-63.623-63.5742 28.5156-63.5742 63.623 28.4668 63.5742 63.5742 63.5742Zm0-9.08203c-30.127 0-54.4922-24.3652-54.4922-54.4922s24.3652-54.4922 54.4922-54.4922 54.4922 24.3652 54.4922 54.4922-24.3652 54.4922-54.4922 54.4922Zm-28.8574-54.4922c0 2.58789 1.85547 4.39453 4.58984 4.39453h19.7266v19.7754c0 2.68555 1.85547 4.58984 4.44336 4.58984 2.68555 0 4.54102-1.85547 4.54102-4.58984v-19.7754h19.7754c2.68555 0 4.58984-1.80664 4.58984-4.39453 0-2.73438-1.85547-4.58984-4.58984-4.58984h-19.7754v-19.7266c0-2.73438-1.85547-4.63867-4.54102-4.63867-2.58789 0-4.44336 1.9043-4.44336 4.63867v19.7266h-19.7266c-2.73438 0-4.58984 1.85547-4.58984 4.58984Z"/>
  </g>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 1953)">Design Variations</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1971)">Symbols are supported in up to nine weights and three scales.</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1989)">For optimal layout with text and other symbols, vertically align</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 2007)">symbols with the adjacent text.</text>
  <line style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="776" x2="776" y1="1919" y2="1933"/>
  <g transform="matrix(0.2 0 0 0.2 776 1933)">
   <path d="m16.5527 0.78125c2.58789 0 3.85742-0.976562 4.78516-3.71094l6.29883-17.2363h28.8086l6.29883 17.2363c0.927734 2.73438 2.19727 3.71094 4.73633 3.71094 2.58789 0 4.24805-1.5625 4.24805-4.00391 0-0.830078-0.146484-1.61133-0.537109-2.63672l-22.9004-60.9863c-1.12305-2.97852-3.125-4.49219-6.25-4.49219-3.02734 0-5.07812 1.46484-6.15234 4.44336l-22.9004 61.084c-0.390625 1.02539-0.537109 1.80664-0.537109 2.63672 0 2.44141 1.5625 3.95508 4.10156 3.95508Zm13.4766-28.3691 11.8652-32.8613h0.244141l11.8652 32.8613Z"/>
  </g>
  <line style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="792.836" x2="792.836" y1="1919" y2="1933"/>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 776 1953)">Margins</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1971)">Leading and trailing margins on the left and right side of each symbol</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1989)">can be adjusted by modifying the x-location of the margin guidelines.</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2007)">Modifications are automatically applied proportionally to all</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2025)">scales and weights.</text>
  <g transform="matrix(0.2 0 0 0.2 1289 1933)">
   <path d="m14.209 9.32617 8.49609 8.54492c4.29688 4.3457 9.22852 4.05273 13.8672-1.07422l53.4668-58.9355-4.83398-4.88281-53.0762 58.3984c-1.75781 2.00195-3.41797 2.49023-5.76172 0.146484l-5.85938-5.81055c-2.34375-2.29492-1.80664-4.00391 0.195312-5.81055l57.373-54.0039-4.88281-4.83398-57.959 54.4434c-4.93164 4.58984-5.32227 9.47266-1.02539 13.8184Zm32.0801-90.9668c-2.09961 2.05078-2.24609 4.93164-1.07422 6.88477 1.17188 1.80664 3.4668 2.97852 6.68945 2.14844 7.32422-1.70898 14.9414-2.00195 22.0703 2.68555l-2.92969 7.27539c-1.70898 4.15039-0.830078 7.08008 1.85547 9.81445l11.4746 11.5723c2.44141 2.44141 4.49219 2.53906 7.32422 2.05078l5.32227-0.976562 3.32031 3.36914-0.195312 2.7832c-0.195312 2.49023 0.439453 4.39453 2.88086 6.78711l3.80859 3.71094c2.39258 2.39258 5.46875 2.53906 7.8125 0.195312l14.5508-14.5996c2.34375-2.34375 2.24609-5.32227-0.146484-7.71484l-3.85742-3.80859c-2.39258-2.39258-4.24805-3.17383-6.64062-2.97852l-2.88086 0.244141-3.22266-3.17383 1.2207-5.61523c0.634766-2.83203-0.146484-5.0293-3.07617-7.95898l-10.9863-10.9375c-16.6992-16.6016-38.8672-16.2109-53.3203-1.75781Zm7.4707 1.85547c12.1582-8.88672 28.6133-7.37305 39.7461 3.75977l12.1582 12.0605c1.17188 1.17188 1.36719 2.09961 1.02539 3.80859l-1.61133 7.42188 7.51953 7.42188 4.93164-0.292969c1.26953-0.0488281 1.66016 0.0488281 2.63672 1.02539l2.88086 2.88086-12.207 12.207-2.88086-2.88086c-0.976562-0.976562-1.12305-1.36719-1.07422-2.68555l0.341797-4.88281-7.4707-7.42188-7.61719 1.26953c-1.61133 0.341797-2.34375 0.195312-3.56445-0.976562l-10.0098-10.0098c-1.26953-1.17188-1.41602-2.00195-0.634766-3.85742l4.39453-10.4492c-7.8125-7.27539-17.9688-10.4004-28.125-7.42188-0.78125 0.195312-1.07422-0.439453-0.439453-0.976562Z"/>
  </g>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 1289 1953)">Exporting</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1971)">Symbols should be outlined when exporting to ensure the</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1989)">design is preserved when submitting to Xcode.</text>
  <text id="template-version" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1933)">Template v.6.0</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1951)">Requires Xcode 16 or greater</text>
  <text id="descriptive-name" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1969)">Generated from gearshape</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1987)">Typeset at 100.0 points</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 726)">Small</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1156)">Medium</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1586)">Large</text>
 </g>
 <g id="Guides">
  <g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 696)">
   <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
  </g>
  <line id="Baseline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="696" y2="696"/>
  <line id="Capline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="625.541" y2="625.541"/>
  <g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1126)">
   <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
  </g>
  <line id="Baseline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1126" y2="1126"/>
  <line id="Capline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1055.54" y2="1055.54"/>
  <g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1556)">
   <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
  </g>
  <line id="Baseline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1556" y2="1556"/>
  <line id="Capline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1485.54" y2="1485.54"/>
  <line id="right-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2984.82" x2="2984.82" y1="600.785" y2="720.121"/>
  <line id="left-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2881.98" x2="2881.98" y1="600.785" y2="720.121"/>
  <line id="right-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1498.01" x2="1498.01" y1="600.785" y2="720.121"/>
  <line id="left-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1401.68" x2="1401.68" y1="600.785" y2="720.121"/>
  <line id="right-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="605.678" x2="605.678" y1="600.785" y2="720.121"/>
  <line id="left-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="513.745" x2="513.745" y1="600.785" y2="720.121"/>
 </g>
 <g id="Symbols">
  <g id="Black-S" transform="matrix(1 0 0 1 2881.98 696)">
   <path class="SFSymbolsPreviewWireframe" d="M47.4609 9.32617L55.3711 9.32617C59.6191 9.32617 63.1348 6.54297 64.0625 2.53906L65.1855-2.29492L65.1855-2.29492L69.4336 0.292969C72.998 2.44141 77.3438 1.80664 80.3223-1.17188L85.791-6.5918C88.916-9.66797 89.3066-14.0625 87.1582-17.5293L84.5703-21.6797L84.5703-21.6797L89.3066-22.8516C93.3105-23.8281 95.9961-27.3438 95.9961-31.543L95.9961-38.9648C95.9961-43.1641 93.3105-46.6797 89.3066-47.6562L84.5703-48.8281L84.5703-48.8281L87.1582-52.9785C89.3066-56.4453 88.916-60.8398 85.791-63.916L80.3223-69.3359C77.3438-72.3145 72.998-72.998 69.4336-70.8008L65.1855-68.2129L65.1855-68.2129L64.0625-73.0469C63.1348-77.0508 59.6191-79.834 55.3711-79.834L47.4609-79.834C43.2129-79.834 39.6973-77.002 38.7695-73.0469L37.6465-68.2129L37.6465-68.2129L33.3984-70.8008C29.834-72.9492 25.4883-72.3145 22.5098-69.3359L17.041-63.916C13.916-60.8398 13.5254-56.4453 15.6738-52.9785L18.2617-48.8281L18.2617-48.8281L13.5254-47.6562C9.47266-46.6309 6.83594-43.1641 6.83594-38.9648L6.83594-31.543C6.83594-27.3438 9.52148-23.8281 13.5254-22.8516L18.2617-21.6797L18.2617-21.6797L15.6738-17.5293C13.5254-14.0625 13.916-9.66797 17.041-6.5918L22.5098-1.17188C25.4883 1.80664 29.834 2.44141 33.3984 0.292969L37.6465-2.29492L37.6465-2.29492L38.7695 2.53906C39.6973 6.54297 43.2129 9.32617 47.4609 9.32617ZM50.0977-2.39258C49.4629-2.39258 49.2188-2.73438 49.1211-3.22266L47.0215-12.3047C44.1406-12.7441 41.1133-14.0137 38.1836-15.9668L30.2246-11.084C29.7852-10.791 29.3457-10.791 28.9551-11.1816L27.0508-13.0371C26.6113-13.4766 26.709-13.916 26.9531-14.3066L31.9336-22.3145C30.0293-25.2441 28.8574-28.125 28.418-30.957L19.3359-33.0566C18.8477-33.1543 18.5059-33.3984 18.5059-34.0332L18.5059-36.4746C18.5059-37.1094 18.8477-37.3535 19.3359-37.4512L28.418-39.5508C28.9551-42.334 29.9805-45.3125 31.9336-48.1934L26.9531-56.2012C26.709-56.5918 26.6113-57.0312 27.0508-57.4707L28.9551-59.3262C29.3457-59.7168 29.7852-59.7168 30.2246-59.4238L38.1836-54.541C40.332-56.1523 43.8477-57.6172 46.9727-58.2031L49.1211-67.2852C49.2188-67.7734 49.4629-68.1152 50.0977-68.1152L52.7344-68.1152C53.3691-68.1152 53.6133-67.7734 53.7109-67.2852L55.8594-58.2031C58.8867-57.666 61.9141-56.3965 64.6484-54.541L72.5098-59.3262C72.9492-59.6191 73.3398-59.668 73.7793-59.2285L75.6836-57.373C76.123-56.9336 76.0254-56.4941 75.7812-56.1035L70.8984-48.1934C72.8027-45.3613 73.877-42.334 74.3652-39.5996L83.4961-37.4512C83.9844-37.3535 84.3262-37.1094 84.3262-36.4746L84.3262-34.0332C84.3262-33.3984 83.9844-33.1543 83.4961-33.0566L74.3652-30.9082C73.9258-28.1738 72.7539-25.0977 70.8984-22.3145L75.7812-14.4043C76.0254-14.0137 76.123-13.5742 75.6836-13.1348L73.7793-11.2793C73.3398-10.8398 72.9492-10.8887 72.5098-11.1816L64.6973-15.9668C61.6211-14.0137 59.1797-12.8906 55.8594-12.3047L53.7109-3.22266C53.6133-2.73438 53.3691-2.39258 52.7344-2.39258ZM51.3672-21.0938C59.1797-21.0938 65.5273-27.4414 65.5273-35.2539C65.5273-43.0664 59.1797-49.4141 51.3672-49.4141C43.5547-49.4141 37.207-43.0664 37.207-35.2539C37.207-27.4414 43.5547-21.0938 51.3672-21.0938ZM51.3672-30.7617C48.877-30.7617 46.875-32.7637 46.875-35.2539C46.875-37.7441 48.877-39.7461 51.3672-39.7461C53.8574-39.7461 55.8594-37.7441 55.8594-35.2539C55.8594-32.7637 53.8574-30.7617 51.3672-30.7617Z"/>
  </g>
  <g id="Regular-S" transform="matrix(1 0 0 1 1401.68 696)">
   <path class="SFSymbolsPreviewWireframe" d="M44.2871 6.00586L52.002 6.00586C55.127 6.00586 57.6172 4.10156 58.3008 1.17188L59.9121-5.85938L60.791-6.15234L66.8457-2.39258C69.4824-0.830078 72.5586-1.17188 74.7559-3.41797L80.127-8.74023C82.3242-10.9375 82.7148-14.0625 81.1035-16.6016L77.2949-22.7051L77.5879-23.4375L84.6191-25.0488C87.5488-25.7324 89.502-28.2715 89.502-31.3477L89.502-38.8672C89.502-41.9434 87.5977-44.4336 84.6191-45.166L77.6855-46.875L77.3438-47.6562L81.1523-53.7598C82.7637-56.3477 82.4219-59.4238 80.1758-61.6699L74.8047-66.9922C72.6562-69.1895 69.5801-69.5801 66.9434-68.0176L60.8398-64.3066L59.9121-64.6484L58.3008-71.6797C57.6172-74.6094 55.127-76.5137 52.002-76.5137L44.2871-76.5137C41.1621-76.5137 38.7207-74.6094 38.0371-71.6797L36.4258-64.6484L35.4492-64.3066L29.3457-68.0176C26.7578-69.5801 23.6816-69.1895 21.4844-66.9922L16.1621-61.6699C13.916-59.4238 13.5742-56.3477 15.1855-53.7598L18.9453-47.6562L18.6035-46.875L11.6699-45.166C8.74023-44.4336 6.83594-41.9434 6.83594-38.8672L6.83594-31.3477C6.83594-28.2715 8.78906-25.7324 11.6699-25.0488L18.7012-23.4375L18.9941-22.7051L15.2344-16.6016C13.623-14.0625 14.0137-10.9375 16.2109-8.74023L21.5332-3.41797C23.7305-1.17188 26.8555-0.830078 29.4434-2.39258L35.5469-6.15234L36.4258-5.85938L38.0371 1.17188C38.7207 4.10156 41.1621 6.00586 44.2871 6.00586ZM44.9707-0.78125C44.4824-0.78125 44.2383-0.976562 44.1895-1.41602L41.7969-10.9863C39.3066-11.6211 37.0605-12.5488 35.3516-13.7207L26.9043-8.49609C26.5625-8.30078 26.1719-8.30078 25.8789-8.64258L21.4844-13.0859C21.1426-13.3789 21.1426-13.7207 21.3867-14.1113L26.5137-22.5098C25.5859-24.1699 24.5117-26.416 23.877-28.9062L14.2578-31.25C13.8184-31.2988 13.623-31.543 13.623-32.0312L13.623-38.2812C13.623-38.8184 13.7695-38.9648 14.2578-39.1113L23.8281-41.4062C24.4629-44.0918 25.7324-46.4355 26.416-47.9004L21.3379-56.25C21.0449-56.6895 21.0449-57.0312 21.3867-57.373L25.8301-61.7188C26.1719-62.0117 26.416-62.0605 26.9043-61.8652L35.2539-56.7871C36.9629-57.8125 39.4043-58.8379 41.8457-59.5215L44.1895-69.0918C44.2383-69.5312 44.4824-69.7266 44.9707-69.7266L51.3672-69.7266C51.8555-69.7266 52.0996-69.5312 52.1484-69.0918L54.4922-59.4238C57.0312-58.7891 59.2285-57.7637 60.9863-56.7383L69.4336-61.8652C69.873-62.0605 70.1172-62.0117 70.5078-61.7188L74.9512-57.373C75.2441-57.0312 75.293-56.6895 75-56.25L69.873-47.9004C70.6055-46.4355 71.8262-44.0918 72.4609-41.4062L82.0801-39.1113C82.5684-38.9648 82.666-38.8184 82.666-38.2812L82.666-32.0312C82.666-31.543 82.5195-31.2988 82.0801-31.25L72.4121-28.9062C71.8262-26.416 70.752-24.1699 69.7754-22.5098L74.9512-14.1113C75.1953-13.7207 75.1953-13.3789 74.8535-13.0859L70.459-8.64258C70.1172-8.30078 69.7266-8.30078 69.4336-8.49609L60.9375-13.7207C59.2773-12.5488 56.9824-11.6211 54.4922-10.9863L52.1484-1.41602C52.0996-0.976562 51.8555-0.78125 51.3672-0.78125ZM48.1445-20.7031C56.2012-20.7031 62.6953-27.1973 62.6953-35.2539C62.6953-43.3105 56.2012-49.8047 48.1445-49.8047C40.0879-49.8047 33.5938-43.3105 33.5938-35.2539C33.5938-27.1973 40.0879-20.7031 48.1445-20.7031ZM48.1445-27.4414C43.8477-27.4414 40.3809-30.957 40.3809-35.2539C40.3809-39.5508 43.8477-43.0664 48.1445-43.0664C52.4414-43.0664 55.957-39.5508 55.957-35.2539C55.957-30.957 52.4414-27.4414 48.1445-27.4414Z"/>
  </g>
  <g id="Ultralight-S" transform="matrix(1 0 0 1 513.745 696)">
   <path class="SFSymbolsPreviewWireframe" d="M42.1074 3.91701L49.8223 3.91701C52.0845 3.91701 53.5303 2.69386 54.0777 0.354499L55.8706-7.40331L58.7476-8.46824L65.4834-4.20897C67.4844-2.91893 69.3799-3.1245 70.9868-4.73485L76.4033-10.1479C78.0557-11.7549 78.2647-13.6538 76.9712-15.6934L72.7085-22.3872L73.7735-25.2993L81.5313-27.0923C83.8706-27.6396 85.0972-29.0889 85.0972-31.3477L85.0972-39.0488C85.0972-41.3076 83.8741-42.7534 81.5313-43.3042L73.7803-45.104L72.712-48.0195L76.9746-54.7134C78.2681-56.7563 78.0625-58.6519 76.4068-60.2622L70.9903-65.6753C69.3867-67.2822 67.4912-67.4912 65.4902-66.2012L58.751-61.9453L55.8706-63.0137L54.0777-70.7715C53.5303-73.1109 52.0845-74.334 49.8223-74.334L42.1074-74.334C39.8452-74.334 38.4028-73.1109 37.8555-70.7715L36.0625-63.0137L33.1333-61.9453L26.3941-66.2012C24.3965-67.4912 22.4556-67.2822 20.894-65.6753L15.481-60.2622C13.8252-58.6519 13.6196-56.7563 14.9131-54.7134L19.1724-48.0195L18.104-45.104L10.3984-43.3042C8.05909-42.7534 6.83594-41.3076 6.83594-39.0488L6.83594-31.3477C6.83594-29.0889 8.06251-27.6396 10.3984-27.0923L18.1108-25.2993L19.1758-22.3872L14.9165-15.6934C13.623-13.6538 13.832-11.7549 15.4844-10.1479L20.8975-4.73485C22.459-3.1245 24.4033-2.91893 26.4009-4.20897L33.1402-8.46824L36.0625-7.40331L37.8555 0.354499C38.4028 2.69386 39.8452 3.91701 42.1074 3.91701ZM42.4278 1.71629C41.0313 1.71629 40.2422 1.02147 39.9663-0.235361L37.937-9.26076C36.3096-9.75929 34.563-10.4146 33.1265-11.0415L25.3604-6.13478C24.2466-5.43996 23.1748-5.53078 22.1553-6.50832L17.2159-11.4512C16.2383-12.4707 16.1475-13.4482 16.8911-14.6108L21.7911-22.4189C21.2266-23.8066 20.5157-25.5532 20.0171-27.1807L10.9883-29.2066C9.73148-29.4824 9.03666-30.2715 9.03666-31.668L9.03666-38.7353C9.03666-40.1352 9.72806-40.9175 10.9883-41.2002L20.0137-43.2226C20.5122-44.8638 21.2369-46.6172 21.7842-47.9912L16.8877-55.7959C16.1407-56.9619 16.2315-57.9394 17.209-58.9624L22.1519-63.8984C23.1748-64.8725 24.2363-64.9668 25.3604-64.272L33.1196-59.3755C34.5562-59.9922 36.3164-60.6543 37.9405-61.1562L39.9663-70.1816C40.2422-71.4385 41.0313-72.1333 42.4278-72.1333L49.5054-72.1333C50.9473-72.1333 51.6909-71.5293 51.9668-70.1816L53.9927-61.1494C55.7144-60.6509 57.3667-59.9888 58.7612-59.372L66.5274-64.272C67.648-64.9668 68.7095-64.8725 69.7358-63.8984L74.6787-58.9624C75.6528-57.9394 75.7471-56.9619 75-55.7959L70.1001-47.9912C70.7417-46.6172 71.4175-44.9092 71.916-43.2226L80.8994-41.2002C82.2051-40.9175 82.8931-40.1352 82.8931-38.7353L82.8931-31.668C82.8931-30.2715 82.2017-29.4824 80.8994-29.2066L71.9126-27.1807C71.4175-25.5078 70.752-23.8066 70.0933-22.4189L74.9966-14.6108C75.7402-13.4482 75.6494-12.4707 74.6719-11.4512L69.7324-6.50832C68.7095-5.53078 67.6377-5.43996 66.5274-6.13478L58.7578-11.0415C57.3701-10.4146 55.7109-9.75929 53.9927-9.26076L51.9668-0.235361C51.6909 1.11229 50.9473 1.71629 49.5054 1.71629ZM45.9649-20.6123C54.0669-20.6123 60.6065-27.1519 60.6065-35.2539C60.6065-43.356 54.0669-49.8955 45.9649-49.8955C37.8628-49.8955 31.3233-43.356 31.3233-35.2539C31.3233-27.1519 37.8628-20.6123 45.9649-20.6123ZM45.9649-22.8096C39.0796-22.8096 33.524-28.3687 33.524-35.2539C33.524-42.1391 39.0796-47.6982 45.9649-47.6982C52.8501-47.6982 58.4092-42.1391 58.4092-35.2539C58.4092-28.3687 52.8501-22.8096 45.9649-22.8096Z"/>
  </g>
 </g>
</svg>
