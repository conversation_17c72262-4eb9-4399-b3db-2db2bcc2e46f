//
//  IconCompatibilityTest.swift
//  Snapback
//
//  Created by <PERSON> on 05/01/25.
//

import AppKit
import SwiftUI

#if DEBUG
    /// A test view to verify SF Symbol compatibility across different macOS versions
    struct IconCompatibilityTestView: View {
        @State private var testResults: [String: Bool] = [:]
        @State private var isRunningTests = false

        // Test symbols that we use throughout the app
        private let testSymbols = [
            "inset.filled.leadinghalf.toptrailing.bottomtrailing.rectangle",
            "exclamationmark.triangle.fill",
            "arrow.clockwise",
            "pencil",
            "trash",
            "person",
            "desktopcomputer",
            "questionmark.circle",
            "info.circle",
            "power",
            "key.fill",
            "arrow.down.circle",
            "arrow.up.circle",
            "globe",
            "checkmark.circle.fill",
            "xmark.circle.fill",
            // Test basic fallback symbols
            "rectangle",
            "display",
        ]

        var body: some View {
            VStack(alignment: .leading, spacing: 16) {
                Text("Icon Compatibility Test")
                    .font(.title2)
                    .fontWeight(.semibold)

                Text("Testing SF Symbols compatibility across macOS versions")
                    .font(.body)
                    .foregroundColor(.secondary)

                HStack(spacing: 12) {
                    Button(action: runCompatibilityTests) {
                        HStack {
                            if isRunningTests {
                                ProgressView()
                                    .scaleEffect(0.8)
                            } else {
                                Image.systemCompat("arrow.clockwise")
                            }
                            Text(isRunningTests ? "Running Tests..." : "Run Icon Tests")
                        }
                    }
                    .disabled(isRunningTests)

                    Button(action: testModalDismissal) {
                        HStack {
                            Image.systemCompat("xmark.circle")
                            Text("Test Modal Dismissal")
                        }
                    }
                }

                if !testResults.isEmpty {
                    Divider()

                    Text("Test Results:")
                        .font(.headline)

                    ScrollView {
                        LazyVStack(alignment: .leading, spacing: 8) {
                            ForEach(testSymbols.sorted(), id: \.self) { symbol in
                                HStack {
                                    // Show the actual icon
                                    Image.systemCompat(symbol)
                                        .frame(width: 20, height: 20)

                                    Text(symbol)
                                        .font(
                                            .system(size: 13, weight: .regular, design: .monospaced)
                                        )

                                    Spacer()

                                    // Show test result
                                    if let result = testResults[symbol] {
                                        Image.systemCompat(
                                            result ? "CheckFilled" : "xmark.circle.fill"
                                        )
                                        .foregroundColor(result ? .green : .red)
                                    } else {
                                        Image.systemCompat("Question")
                                            .foregroundColor(.orange)
                                    }
                                }
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(Color.secondary.opacity(0.1))
                                .cornerRadius(4)
                            }
                        }
                    }
                    .frame(maxHeight: 300)

                    // Summary
                    let passedCount = testResults.values.filter { $0 }.count
                    let totalCount = testResults.count

                    HStack {
                        Text("Summary:")
                            .font(.headline)

                        Spacer()

                        Text("\(passedCount)/\(totalCount) symbols working")
                            .foregroundColor(passedCount == totalCount ? .green : .orange)
                            .font(.system(size: 14, weight: .medium))
                    }
                    .padding(.top)
                }
            }
            .padding()
            .frame(maxWidth: 600, maxHeight: 500)
        }

        private func runCompatibilityTests() {
            isRunningTests = true
            testResults.removeAll()

            // Run tests asynchronously to avoid blocking the UI
            DispatchQueue.global(qos: .userInitiated).async {
                var results: [String: Bool] = [:]

                for symbol in testSymbols {
                    // Test if the symbol can be loaded
                    let canLoadSymbol = testSymbolAvailability(symbol)
                    results[symbol] = canLoadSymbol

                    // Small delay to show progress
                    Thread.sleep(forTimeInterval: 0.1)
                }

                DispatchQueue.main.async {
                    self.testResults = results
                    self.isRunningTests = false

                    // Log results to console
                    print("🔍 Icon Compatibility Test Results:")
                    for symbol in testSymbols.sorted() {
                        let status = results[symbol] == true ? "✅" : "❌"
                        print("\(status) \(symbol)")
                    }

                    let passedCount = results.values.filter { $0 }.count
                    print("📊 Summary: \(passedCount)/\(results.count) symbols working")
                    print("🎨 Simplified SF Symbol System: Active")
                    print("💡 Using native SF Symbols with macOS system fallbacks")
                }
            }
        }

        private func testSymbolAvailability(_ symbolName: String) -> Bool {
            // Test both SwiftUI Image and NSImage creation
            let swiftUIWorks = testSwiftUIImage(symbolName)
            let nsImageWorks = testNSImage(symbolName)

            return swiftUIWorks && nsImageWorks
        }

        private func testSwiftUIImage(_ symbolName: String) -> Bool {
            // Try to create the image using our compatibility system
            let _ = Image.systemCompat(symbolName)

            // For SwiftUI, we can't easily test if the image loaded successfully
            // So we assume it works if no exception was thrown
            return true
        }

        private func testNSImage(_ symbolName: String) -> Bool {
            // Test NSImage creation using our compatibility system
            let image = NSImage.systemCompat(symbolName)
            return image != nil
        }

        private func testModalDismissal() {
            // Test the modal dismissal system
            print("🧪 Testing Modal Dismissal System")

            // Create a test modal window
            let testWindow = NSWindow(
                contentRect: NSRect(x: 0, y: 0, width: 300, height: 200),
                styleMask: [.titled, .closable],
                backing: .buffered,
                defer: false
            )

            testWindow.title = "Test Modal"
            testWindow.level = .floating

            let testView = VStack {
                Text("Test Modal Window")
                    .font(.headline)
                    .padding()

                Button("Test Dismissal") {
                    WindowDismissalManager.shared.dismissCurrentModal(windowTitle: "Test Modal")
                }
                .buttonStyle(.bordered)
            }
            .frame(width: 280, height: 180)

            testWindow.contentView = NSHostingView(rootView: testView)
            testWindow.center()
            testWindow.makeKeyAndOrderFront(nil)

            print("✅ Test modal window created and displayed")
            print(
                "💡 Click 'Test Dismissal' button to verify modal dismissal works on macOS Monterey")
        }
    }

    // MARK: - Preview

    struct IconCompatibilityTestView_Previews: PreviewProvider {
        static var previews: some View {
            IconCompatibilityTestView()
        }
    }

    // MARK: - Integration with Settings

    extension SettingsView {
        /// Add this to the debug section of settings to access the icon compatibility test
        var iconCompatibilityTestButton: some View {
            Button("Test Icon Compatibility") {
                let window = NSWindow(
                    contentRect: NSRect(x: 0, y: 0, width: 650, height: 550),
                    styleMask: [.titled, .closable, .resizable],
                    backing: .buffered,
                    defer: false
                )
                window.title = "Icon Compatibility Test"
                window.contentView = NSHostingView(rootView: IconCompatibilityTestView())
                window.center()
                window.makeKeyAndOrderFront(nil)
            }
            .buttonStyle(.bordered)
        }
    }
#endif
