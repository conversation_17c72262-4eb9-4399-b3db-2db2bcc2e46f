import AppKit
import Carbon.HIToolbox
import Foundation
import KeyboardShortcuts
import MA<PERSON>hortcut

/// Example of how to use the ShortcutManager
class ShortcutManagerUsageExample {
    /// Initialize the shortcut manager
    static func initializeShortcutManager() {
        // Register default shortcuts
        registerDefaultShortcuts()

        // Register shortcut handlers
        registerShortcutHandlers()
    }

    /// Register default shortcuts
    private static func registerDefaultShortcuts() {
        let bridge = KeyboardShortcutsBridge.shared

        // Register left half shortcut
        bridge.registerName(
            .leftHalf,
            stringName: "leftHalf",
            defaultShortcut: Shortcut(
                keyCode: Int(kVK_LeftArrow),
                modifiers: [.control, .option]
            )
        )

        // Register right half shortcut
        bridge.registerName(
            .rightHalf,
            stringName: "rightHalf",
            defaultShortcut: Shortcut(
                keyCode: Int(kVK_RightArrow),
                modifiers: [.control, .option]
            )
        )

        // Register top half shortcut
        bridge.registerName(
            .topHalf,
            stringName: "topHalf",
            defaultShortcut: Shortcut(
                keyCode: Int(kVK_UpArrow),
                modifiers: [.control, .option]
            )
        )

        // Register bottom half shortcut
        bridge.registerName(
            .bottomHalf,
            stringName: "bottomHalf",
            defaultShortcut: Shortcut(
                keyCode: Int(kVK_DownArrow),
                modifiers: [.control, .option]
            )
        )

        // Register more shortcuts as needed
    }

    /// Register shortcut handlers
    private static func registerShortcutHandlers() {
        let bridge = KeyboardShortcutsBridge.shared

        // Register left half handler
        bridge.registerShortcutHandler(for: .leftHalf) {
            print("Left half shortcut triggered")
            // Add your action here
        }

        // Register right half handler
        bridge.registerShortcutHandler(for: .rightHalf) {
            print("Right half shortcut triggered")
            // Add your action here
        }

        // Register top half handler
        bridge.registerShortcutHandler(for: .topHalf) {
            print("Top half shortcut triggered")
            // Add your action here
        }

        // Register bottom half handler
        bridge.registerShortcutHandler(for: .bottomHalf) {
            print("Bottom half shortcut triggered")
            // Add your action here
        }

        // Register more handlers as needed
    }

    /// Example of how to set a shortcut
    static func setShortcutExample() {
        let bridge = KeyboardShortcutsBridge.shared

        // Create a shortcut
        let shortcut = Shortcut(
            keyCode: Int(kVK_ANSI_A),
            modifiers: [.command, .shift]
        )

        // Set the shortcut
        bridge.setShortcut(shortcut, for: .leftHalf)

        // Get the shortcut
        if let savedShortcut = bridge.getShortcutForName(.leftHalf) {
            print("Saved shortcut: \(savedShortcut.displayString ?? "Unknown")")
        } else {
            print("No shortcut saved")
        }
    }

    /// Example of how to reset a shortcut
    static func resetShortcutExample() {
        let bridge = KeyboardShortcutsBridge.shared

        // Reset a specific shortcut
        bridge.resetShortcut(for: .leftHalf)

        // Reset all shortcuts
        bridge.resetAllShortcuts()
    }
}
