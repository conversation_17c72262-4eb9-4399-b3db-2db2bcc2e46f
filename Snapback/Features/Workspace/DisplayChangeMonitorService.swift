import AppKit
import CoreGraphics
import Foundation

/// Global service to monitor display configuration changes and provide consistent detection
/// regardless of whether workspace forms are currently visible
class DisplayChangeMonitorService {
    static let shared = DisplayChangeMonitorService()

    private let logger = LoggingService.shared
    private let serviceName = "DisplayChangeMonitorService"

    // Current display arrangement snapshot
    private var currentArrangement: DisplayArrangementInfo?
    private var lastUpdateTime: Date = Date()

    // Notification name for display changes
    static let displayConfigurationChanged = Notification.Name("DisplayConfigurationChanged")

    private init() {
        // Capture initial display arrangement
        updateCurrentArrangement()

        // Set up system notification observer for display changes
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleSystemDisplayChange),
            name: NSApplication.didChangeScreenParametersNotification,
            object: nil
        )

        logger.info("DisplayChangeMonitorService initialized", service: serviceName)
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    /// Get the current display arrangement
    func getCurrentArrangement() -> DisplayArrangementInfo {
        return currentArrangement ?? WindowLayoutManager.captureCurrentDisplayArrangement()
    }

    /// Check if there have been critical display changes since a saved arrangement
    /// This method provides consistent detection regardless of UI state
    func hasCriticalChanges(since savedArrangement: DisplayArrangementInfo) -> Bool {
        let current = getCurrentArrangement()

        // Use the stability service to analyze the change
        let analysis = DisplayArrangementStabilityService.shared.isGenuineDisplayChange(
            savedArrangement: savedArrangement,
            currentArrangement: current
        )

        logger.debug(
            "Global critical change check: \(analysis.changeType.rawValue)",
            service: serviceName
        )

        return isCriticalDisplayChange(analysis.changeType)
    }

    /// Get detailed analysis of changes since a saved arrangement
    func analyzeChanges(since savedArrangement: DisplayArrangementInfo) -> DisplayChangeAnalysis {
        let current = getCurrentArrangement()

        return DisplayArrangementStabilityService.shared.isGenuineDisplayChange(
            savedArrangement: savedArrangement,
            currentArrangement: current
        )
    }

    /// Handle system display configuration changes
    @objc private func handleSystemDisplayChange() {
        logger.info("System display configuration changed", service: serviceName)

        // Update our current arrangement snapshot
        let previousArrangement = currentArrangement
        updateCurrentArrangement()

        // If we had a previous arrangement, analyze the change
        if let previous = previousArrangement {
            let analysis = DisplayArrangementStabilityService.shared.isGenuineDisplayChange(
                savedArrangement: previous,
                currentArrangement: currentArrangement!
            )

            logger.info(
                "Display change detected: \(analysis.changeType.rawValue) (confidence: \(String(format: "%.2f", analysis.confidence)))",
                service: serviceName
            )

            // Post notification about the change
            NotificationCenter.default.post(
                name: Self.displayConfigurationChanged,
                object: analysis
            )
        }
    }

    /// Update the current display arrangement snapshot
    private func updateCurrentArrangement() {
        currentArrangement = WindowLayoutManager.captureCurrentDisplayArrangement()
        lastUpdateTime = Date()

        if let arrangement = currentArrangement {
            logger.debug(
                "Updated display arrangement snapshot: \(arrangement.displayFingerprints.count) displays, main: \(arrangement.mainDisplayFingerprint?.stableID ?? "none")",
                service: serviceName
            )
        }
    }

    /// Determine if a display change type is critical and should trigger migration warnings
    /// Critical scenarios that break saved workspaces:
    /// 1. Display removed (count decreased) - windows on removed displays have nowhere to go
    /// 2. Main display changed (primary display switched) - coordinate system changes
    /// 3. Display replaced with incompatible specifications - windows may not position correctly
    ///
    /// Non-critical scenarios:
    /// - Display additions: existing displays unchanged, windows can restore normally
    /// - Display replaced with compatible specifications: windows can restore to same positions
    /// - Position changes: display rearrangement doesn't break workspaces
    func isCriticalDisplayChange(_ changeType: DisplayChangeType) -> Bool {
        switch changeType {
        case .displayAdded:
            return false  // Non-critical: Existing displays unchanged, windows can restore normally
        case .displayRemoved:
            return true  // Critical: Display count decreased, windows on removed displays lost
        case .mainDisplayChanged:
            return true  // Critical: Primary display switched, coordinate system changed
        case .noChange:
            return false  // No change
        case .positionChanged:
            return false  // Non-critical: Display rearrangement doesn't break workspaces
        case .displayReplaced:
            // Compatibility check is done in workspace-specific method with full context
            return false  // Non-critical by default, specific compatibility checked per workspace
        case .potentialRestart:
            return false  // Non-critical: Likely system restart, not actual change
        case .identicalMonitorSwap:
            return false  // Non-critical: Identical monitors swapped, workspace should adapt
        }
    }

    /// Check if display replacement involves incompatible specifications
    /// This method compares saved and current display arrangements to determine compatibility
    func hasIncompatibleDisplayReplacement(
        savedArrangement: DisplayArrangementInfo,
        currentArrangement: DisplayArrangementInfo
    ) -> Bool {

        logger.debug(
            "Checking display replacement compatibility between saved and current arrangements",
            service: serviceName
        )

        // Both arrangements should have the same number of displays for replacement scenario
        guard
            savedArrangement.displayFingerprints.count
                == currentArrangement.displayFingerprints.count
        else {
            logger.debug(
                "Display count mismatch - not a replacement scenario", service: serviceName)
            return false
        }

        // Check if each saved display has a migration-compatible counterpart in current arrangement
        for savedDisplay in savedArrangement.displayFingerprints {
            let hasCompatibleMatch = currentArrangement.displayFingerprints.contains {
                currentDisplay in
                savedDisplay.isCompatibleForMigration(with: currentDisplay)
            }

            if !hasCompatibleMatch {
                logger.info(
                    "Incompatible display replacement detected: saved display '\(savedDisplay.name ?? "unknown")' has no migration-compatible match",
                    service: serviceName
                )
                return true  // Critical: incompatible replacement
            }
        }

        logger.debug(
            "All replaced displays are migration-compatible - workspace can restore normally",
            service: serviceName
        )

        return false  // Non-critical: all replacements are compatible
    }
}

// MARK: - Convenience Extensions

extension DisplayChangeMonitorService {
    /// Check if a workspace has critical display changes
    func workspaceHasCriticalChanges(_ workspace: Workspace) -> Bool {
        guard let savedArrangement = workspace.displayArrangement else {
            return false  // No saved arrangement to compare against
        }

        let currentArrangement = getCurrentArrangement()
        let analysis = analyzeChanges(since: savedArrangement)

        // Enhanced logging for main display change debugging
        logger.info(
            "🔍 MAIN DISPLAY DEBUG: Saved main: '\(savedArrangement.mainDisplayFingerprint?.stableID ?? "none")', Current main: '\(currentArrangement.mainDisplayFingerprint?.stableID ?? "none")', Change type: \(analysis.changeType.rawValue)",
            service: serviceName
        )

        // For display replacement, check compatibility using existing logic
        if analysis.changeType == .displayReplaced {
            return hasIncompatibleDisplayReplacement(
                savedArrangement: savedArrangement,
                currentArrangement: currentArrangement
            )
        }

        let isCritical = isCriticalDisplayChange(analysis.changeType)

        // Additional logging for main display changes
        if analysis.changeType == .mainDisplayChanged {
            logger.warning(
                "🚨 MAIN DISPLAY CHANGE DETECTED: This should prevent workspace restoration! Critical: \(isCritical)",
                service: serviceName
            )
        }

        return isCritical
    }

    /// Get a user-friendly description of the critical changes for a workspace
    func getCriticalChangeDescription(for workspace: Workspace) -> String? {
        guard let savedArrangement = workspace.displayArrangement else {
            return nil
        }

        let analysis = analyzeChanges(since: savedArrangement)

        // Special handling for display replacement - check compatibility
        if analysis.changeType == .displayReplaced {
            let isIncompatible = hasIncompatibleDisplayReplacement(
                savedArrangement: savedArrangement,
                currentArrangement: getCurrentArrangement()
            )
            if !isIncompatible {
                return nil  // Compatible replacement, not critical
            }
        } else if !isCriticalDisplayChange(analysis.changeType) {
            return nil
        }

        switch analysis.changeType {
        case .displayAdded:
            let savedCount = savedArrangement.displayFingerprints.count
            let currentCount = getCurrentArrangement().displayFingerprints.count
            return "Display added (\(savedCount) → \(currentCount) displays)"

        case .displayRemoved:
            let savedCount = savedArrangement.displayFingerprints.count
            let currentCount = getCurrentArrangement().displayFingerprints.count
            return "Display removed (\(savedCount) → \(currentCount) displays)"

        case .mainDisplayChanged:
            return "Main display changed"

        case .displayReplaced:
            return "Display replaced with incompatible specifications"

        default:
            return "Critical display configuration change"
        }
    }
}
