import SwiftUI

struct CustomLayoutView: View {
    // Inject WorkspaceService
    @EnvironmentObject var workspaceService: WorkspaceService

    // State for the form
    @State private var customLayoutName: String

    // Environment action for dismissing
    @Environment(\.dismiss) var dismiss

    // The workspace being edited
    let workspace: Workspace

    // Initializer takes the workspace and sets up initial state
    init(workspace: Workspace) {
        self.workspace = workspace
        _customLayoutName = State(initialValue: workspace.customLayout?.name ?? "")
    }

    var body: some View {
        VStack(spacing: 15) {
            Text("Edit Custom Layout")
                .font(.title)
                .padding(.bottom)

            // TODO: Add actual layout editing UI here later
            Text("Layout editing UI placeholder.")
                .foregroundColor(.secondary)
                .padding(.vertical)

            TextField("Custom Layout Name", text: $customLayoutName)
                .textFieldStyle(.roundedBorder)
                .padding(.bottom)

            HStack(spacing: 20) {
                But<PERSON>("Cancel") { dismiss() }
                    .buttonStyle(.bordered)
                    .keyboardShortcut(.cancelAction)

                But<PERSON>("Save") {
                    saveCustomLayout()
                    dismiss()
                }
                .buttonStyle(.borderedProminent)
                .keyboardShortcut(.defaultAction)
            }
            .padding(.top)
        }
        .padding()
        .frame(minWidth: 350, idealWidth: 400, minHeight: 250)
    }

    func saveCustomLayout() {
        var updatedWorkspace = workspace

        if customLayoutName.isEmpty {
            updatedWorkspace.customLayout = nil
            print("[CustomLayoutView] Custom layout removed for workspace: \(workspace.name)")
        } else {
            // TODO: Replace placeholder layout creation with actual UI-driven layout data
            let screen = NSScreen.main
            let screenFrame = screen?.visibleFrame ?? .zero
            let firstFrame = CGRect(
                x: screenFrame.minX, y: screenFrame.minY, width: screenFrame.width / 2,
                height: screenFrame.height / 2)
            let layoutFrames = [firstFrame]

            let layoutID = updatedWorkspace.customLayout?.id ?? UUID()
            let updatedLayout = CustomLayout(
                id: layoutID, name: customLayoutName, layout: layoutFrames)
            updatedWorkspace.customLayout = updatedLayout
            print("[CustomLayoutView] Custom layout saved for workspace: \(workspace.name)")
        }
        workspaceService.updateWorkspace(updatedWorkspace)
    }
}

#Preview {
    struct PreviewWrapper: View {
        @StateObject var workspaceService = WorkspaceService(
            snappingService: WindowSnappingService())
        @State private var workspaceToEdit: Workspace?
        @State var showSheet = true

        var body: some View {
            VStack { Text("Preview Host") }
                .onAppear {
                    let sampleLayout = CustomLayout(
                        id: UUID(), name: "Initial Layout",
                        layout: [CGRect(x: 0, y: 0, width: 100, height: 100)])
                    // *** Corrected Workspace initialization for preview ***
                    let initialWorkspace = Workspace(
                        id: UUID(), name: "Layout Test", windowInfos: [], shortcutKeyCode: nil,
                        shortcutModifiers: nil, customLayout: sampleLayout)  // Use new properties
                    if workspaceService.workspaces.isEmpty {
                        workspaceService.workspaces = [initialWorkspace]
                    }
                    workspaceToEdit = initialWorkspace
                }
                .sheet(isPresented: $showSheet) {
                    if let workspace = workspaceToEdit {
                        CustomLayoutView(workspace: workspace)
                            .environmentObject(workspaceService)
                    } else {
                        Text("Loading...")
                    }
                }
        }
    }
    return PreviewWrapper()
}
