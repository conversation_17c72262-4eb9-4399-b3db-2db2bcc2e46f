import SwiftUI

/// Main view for displaying and filtering logs
struct LogViewerView: View {
    @StateObject private var logStore = LogStore.shared
    @State private var searchText = ""
    @State private var showGroupedByFeature = false

    // Logger for this view
    private let logger = LoggingService.shared
    private let serviceName = "LogViewerView"

    var body: some View {
        VStack(spacing: 0) {
            // Toolbar
            HStack {
                // Log level filter
                Picker("Level", selection: $logStore.selectedLogLevel) {
                    ForEach(LogLevelFilter.allCases) { level in
                        Text(level.rawValue).tag(level)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
                .frame(width: 300)

                Spacer()

                // Feature category filter
                Menu {
                    Button("All Categories") {
                        logStore.selectedCategory = .all
                    }

                    Divider()

                    // Feature groups
                    ForEach(Array(LogCategory.featureGroups.keys.sorted()), id: \.self) { group in
                        Menu(group) {
                            Button("All \(group)") {
                                logStore.selectedCategory = .feature(group)
                            }

                            Divider()

                            // Individual categories in this group
                            if let categories = LogCategory.featureGroups[group] {
                                ForEach(categories, id: \.rawValue) { category in
                                    Button(category.displayName) {
                                        logStore.selectedCategory = .specific(category)
                                    }
                                }
                            }
                        }
                    }
                } label: {
                    HStack {
                        Text(logStore.selectedCategory.displayName)
                        Image(systemName: "chevron.down")
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.secondary.opacity(0.2))
                    .cornerRadius(6)
                }

                Spacer()

                // Group by feature toggle
                Toggle("Group by Feature", isOn: $showGroupedByFeature)
                    .toggleStyle(.switch)

                // Clear logs button
                Button(action: {
                    logStore.clearLogs()
                    logger.info("Logs cleared", service: serviceName)
                }) {
                    Image(systemName: "trash")
                }
                .buttonStyle(.borderless)
                .padding(.horizontal)
            }
            .padding()
            .background(SnapbackTheme.Background.info)

            // Search field
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(SnapbackTheme.Text.secondary)

                TextField("Search logs...", text: $searchText)
                    .textFieldStyle(PlainTextFieldStyle())

                if !searchText.isEmpty {
                    Button(action: {
                        searchText = ""
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(SnapbackTheme.Text.secondary)
                    }
                    .buttonStyle(BorderlessButtonStyle())
                }
            }
            .padding(8)
            .background(Color.secondary.opacity(0.1))
            .cornerRadius(8)
            .padding(.horizontal)
            .padding(.bottom, 8)

            // Log content
            if showGroupedByFeature {
                groupedLogsView
            } else {
                flatLogsView
            }
        }
        .frame(minWidth: 800, minHeight: 600)
        .onAppear {
            logger.info("Log viewer opened", service: serviceName)
        }
    }

    // Filtered logs based on search text
    private var filteredBySearchLogs: [LogEntry] {
        if searchText.isEmpty {
            return logStore.filteredLogs
        } else {
            return logStore.filteredLogs.filter { log in
                log.message.localizedCaseInsensitiveContains(searchText)
                    || log.service.localizedCaseInsensitiveContains(searchText)
                    || log.category.rawValue.localizedCaseInsensitiveContains(searchText)
            }
        }
    }

    // View for displaying logs in a flat list
    private var flatLogsView: some View {
        List {
            ForEach(filteredBySearchLogs) { log in
                LogEntryRow(log: log)
            }
        }
        .listStyle(PlainListStyle())
    }

    // View for displaying logs grouped by feature
    private var groupedLogsView: some View {
        ScrollView {
            LazyVStack(alignment: .leading, spacing: 16) {
                let groupedLogs = logStore.getLogsGroupedByFeature()
                let filteredGroups = groupedLogs.filter { group, logs in
                    if searchText.isEmpty {
                        return true
                    } else {
                        return logs.contains { log in
                            log.message.localizedCaseInsensitiveContains(searchText)
                                || log.service.localizedCaseInsensitiveContains(searchText)
                                || log.category.rawValue.localizedCaseInsensitiveContains(
                                    searchText)
                        }
                    }
                }

                ForEach(filteredGroups.keys.sorted(), id: \.self) { group in
                    if let logs = filteredGroups[group]?.filter({ log in
                        if searchText.isEmpty {
                            return true
                        } else {
                            return log.message.localizedCaseInsensitiveContains(searchText)
                                || log.service.localizedCaseInsensitiveContains(searchText)
                                || log.category.rawValue.localizedCaseInsensitiveContains(
                                    searchText)
                        }
                    }), !logs.isEmpty {
                        VStack(alignment: .leading, spacing: 4) {
                            Text(group)
                                .font(.headline)
                                .padding(.horizontal)

                            Divider()

                            ForEach(logs) { log in
                                LogEntryRow(log: log)
                                    .padding(.horizontal)
                            }
                        }
                        .padding(.vertical, 8)
                        .background(Color.secondary.opacity(0.05))
                        .cornerRadius(8)
                        .padding(.horizontal)
                    }
                }
            }
            .padding(.vertical)
        }
    }
}

/// Row view for a single log entry
struct LogEntryRow: View {
    let log: LogEntry

    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack(alignment: .top) {
                // Level indicator
                Text(log.levelPrefix)
                    .font(.system(size: 14))

                // Timestamp
                Text(log.formattedTimestamp)
                    .font(.system(size: 12, weight: .medium, design: .monospaced))
                    .foregroundColor(.secondary)

                // Service and category
                Text("[\(log.service)]")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.secondary)

                Text("[\(log.category.rawValue)]")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.secondary)

                Spacer()
            }

            // Message
            Text(log.message)
                .font(.system(size: 13))
                .lineLimit(nil)
                .padding(.leading, 4)
        }
        .padding(.vertical, 4)
        .contentShape(Rectangle())
    }
}

struct LogViewerView_Previews: PreviewProvider {
    static var previews: some View {
        LogViewerView()
    }
}
