import AppKit
import Carbon.HIToolbox
import Foundation
import KeyboardShortcuts
import MA<PERSON>hortcut
import SwiftUI

struct ShortcutRow: View {
    let name: String
    let action: String
    let onReset: () -> Void

    // Environment objects for conflict detection
    @EnvironmentObject var workspaceService: WorkspaceService

    // Logger
    private let logger = LoggingService.shared
    private let serviceName = "ShortcutRow"

    // Get the corresponding KeyboardShortcuts.Name for this action
    private var shortcutName: KeyboardShortcuts.Name {
        switch action {
        case "leftHalf":
            return .leftHalf
        case "rightHalf":
            return .rightHalf
        case "topHalf":
            return .topHalf
        case "bottomHalf":
            return .bottomHalf
        case "topLeftQuarter":
            return .topLeftQuarter
        case "topRightQuarter":
            return .topRightQuarter
        case "bottomLeftQuarter":
            return .bottomLeftQuarter
        case "bottomRightQuarter":
            return .bottomRightQuarter
        case "leftThird":
            return .leftThird
        case "centerThird":
            return .centerThird
        case "rightThird":
            return .rightThird
        case "leftTwoThirds":
            return .leftTwoThirds
        case "centerTwoThirds":
            return .centerTwoThirds
        case "rightTwoThirds":
            return .rightTwoThirds
        case "fullscreen":
            return .fullscreen
        case "saveWorkspace":
            return .saveWorkspace
        default:
            // This should never happen, but we need a fallback
            return .leftHalf
        }
    }

    // Get the shortcut from our wrapper
    private var currentShortcut: Shortcut? {
        return KeyboardShortcutsBridge.shared.getShortcutForName(shortcutName)
    }

    // State to force refresh
    @State private var refreshTrigger = UUID()

    var body: some View {
        VStack(spacing: 0) {
            HStack {
                Text(name)
                    .frame(minWidth: 100, alignment: .leading)

                Spacer()

                // Use KeyboardShortcuts.Recorder instead of custom UI
                KeyboardShortcuts.Recorder(
                    for: shortcutName,
                    onChange: { newShortcut in
                        // This is the direct onChange callback from the KeyboardShortcuts.Recorder
                        print("🔄 DIRECT CALLBACK: Shortcut changed for '\(name)' (\(action))")
                        print("🔄 DIRECT CALLBACK: New value: \(String(describing: newShortcut))")

                        // Log the change - no need to post notification as KeyboardShortcuts library
                        // already posts the notification "KeyboardShortcuts_shortcutByNameDidChange"
                        logger.debug(
                            "Shortcut changed for '\(name)'",
                            service: serviceName,
                            category: .shortcuts
                        )

                        if newShortcut == nil {
                            // Log the removal
                            logger.debug(
                                "Shortcut removed for '\(name)'",
                                service: serviceName,
                                category: .shortcuts
                            )
                        }

                        // If this is the save workspace shortcut, explicitly refresh the menu
                        if action == "saveWorkspace" {
                            logger.debug(
                                "Save workspace shortcut changed, posting menu refresh notification",
                                service: serviceName,
                                category: .shortcuts
                            )
                            NotificationCenter.default.post(
                                name: .refreshStatusMenu,
                                object: nil
                            )
                        }
                    }
                )
                .padding(.trailing, -SnapbackTheme.Padding.large)  // Negative padding to remove extra space on the right
                .frame(width: 140)  // Fixed width

                // No individual reset buttons - only using the Reset All button at the bottom
            }
            .snapbackRowStyle()

        }

        .onAppear {
            // Log the shortcut name being used
            logger.debug(
                "ShortcutRow appeared for '\(name)' (\(action)) using shortcutName: \(shortcutName.rawValue)",
                service: serviceName,
                category: .shortcuts
            )

            // Check for conflicts when the view appears, but don't show alerts
            if let shortcut = KeyboardShortcuts.getShortcut(for: shortcutName) {
                logger.debug(
                    "Found existing shortcut for '\(name)': \(String(describing: shortcut))",
                    service: serviceName,
                    category: .shortcuts
                )

            } else {
                logger.debug(
                    "No existing shortcut found for '\(name)' (\(action))",
                    service: serviceName,
                    category: .shortcuts
                )
            }

            // Register onKeyDown handler to detect key events
            print("[ShortcutRow] 🔍 Registering onKeyDown handler for '\(name)' (\(action))")

            // Use the standard onKeyDown handler
            KeyboardShortcuts.onKeyDown(for: shortcutName) { [self] in
                print("[ShortcutRow] 🔍 onKeyDown triggered for '\(name)' (\(action))")

                // Log the current shortcut
                if let shortcut = KeyboardShortcuts.getShortcut(for: shortcutName) {
                    print(
                        "[ShortcutRow] 🔍 Current shortcut: \(String(describing: shortcut))")

                }
            }

            // Observe shortcut changes
            NotificationCenter.default.addObserver(
                forName: Notification.Name("KeyboardShortcuts_shortcutByNameDidChange"),
                object: nil,
                queue: .main
            ) { notification in
                guard let nameObj = notification.userInfo?["name"] as? KeyboardShortcuts.Name,
                    nameObj == shortcutName
                else {
                    return
                }

                // Force a refresh when this shortcut changes
                refreshTrigger = UUID()

                // Note: No conflict checking - following professional app approach
            }

        }
    }
}
