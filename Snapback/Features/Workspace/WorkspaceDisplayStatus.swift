import Foundation
import SwiftUI

/// Simple status representation for workspace restoration capability
/// Integrates with existing DisplayChangeMonitorService instead of duplicating functionality
enum WorkspaceDisplayStatus {
    case available
    case criticalDisplayChange(description: String)
    
    /// Whether the workspace can be restored
    var canRestore: Bool {
        switch self {
        case .available:
            return true
        case .criticalDisplayChange:
            return false
        }
    }
    
    /// User-friendly status description for tooltips
    var statusDescription: String {
        switch self {
        case .available:
            return "Ready to restore"
        case .criticalDisplayChange(let description):
            return description
        }
    }
    
    /// Short status badge text
    var badgeText: String? {
        switch self {
        case .available:
            return nil
        case .criticalDisplayChange:
            return "Display Changed"
        }
    }
    
    /// Badge color for status indicator
    var badgeColor: Color {
        switch self {
        case .available:
            return .clear
        case .criticalDisplayChange:
            return .orange
        }
    }
    
    /// Whether the workspace should appear dimmed/disabled
    var shouldDim: Bool {
        switch self {
        case .available:
            return false
        case .criticalDisplayChange:
            return true
        }
    }
}

// MARK: - Workspace Extensions

extension Workspace {
    /// Get the current display status for this workspace using existing DisplayChangeMonitorService
    var displayStatus: WorkspaceDisplayStatus {
        let hasCriticalChanges = DisplayChangeMonitorService.shared.workspaceHasCriticalChanges(self)
        
        if hasCriticalChanges {
            let description = DisplayChangeMonitorService.shared.getCriticalChangeDescription(for: self) 
                ?? "Critical display configuration change"
            return .criticalDisplayChange(description: description)
        }
        
        return .available
    }
    
    /// Whether this workspace can currently be restored (integrates with existing logic)
    var canRestore: Bool {
        return displayStatus.canRestore
    }
}

// MARK: - UI Helper Functions

/// Get visual styling properties for a workspace based on display status
struct WorkspaceVisualStyle {
    let opacity: Double
    let saturation: Double
    let badgeText: String?
    let badgeColor: Color
    let statusDescription: String
    let canRestore: Bool
    
    init(for workspace: Workspace) {
        let status = workspace.displayStatus
        
        self.opacity = status.shouldDim ? 0.6 : 1.0
        self.saturation = status.shouldDim ? 0.5 : 1.0
        self.badgeText = status.badgeText
        self.badgeColor = status.badgeColor
        self.statusDescription = status.statusDescription
        self.canRestore = status.canRestore
    }
}
