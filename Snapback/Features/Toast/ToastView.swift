import SwiftUI

struct ToastContentView: View {
    @ObservedObject private var toastManager = ToastManager.shared
    @State private var isRotating = false

    var body: some View {
        if let toast = toastManager.currentToast {
            VStack {
                HStack(spacing: 12) {
                    // Icon
                    Group {
                        if toast.type == .loading {
                            Image.systemCompat(toast.type.icon)
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                                .frame(width: 18, height: 18)
                                .rotationEffect(.degrees(isRotating ? 360 : 0))
                                .onAppear {
                                    withAnimation(
                                        Animation.linear(duration: 1.0).repeatForever(
                                            autoreverses: false)
                                    ) {
                                        isRotating = true
                                    }
                                }
                                .onDisappear {
                                    isRotating = false
                                }
                        } else {
                            Image.systemCompat(toast.type.icon)
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                                .frame(width: 18, height: 18)
                        }
                    }
                    .foregroundColor(toast.type.color)

                    // Content
                    VStack(alignment: .leading, spacing: SnapbackTheme.Padding.extraSmall) {
                        Text(toast.title)
                            .font(
                                .system(
                                    size: SnapbackTheme.FontSize.body,
                                    weight: SnapbackTheme.FontWeight.heading)
                            )
                            .foregroundColor(SnapbackTheme.Text.primary)

                        if let message = toast.message {
                            Text(message)
                                .font(.system(size: SnapbackTheme.FontSize.caption))
                                .foregroundColor(SnapbackTheme.Text.secondary)
                        }
                    }

                    // Close button
                    Button(action: {
                        toastManager.dismiss()
                    }) {
                        Image(systemName: "xmark")
                            .font(
                                .system(
                                    size: SnapbackTheme.FontSize.caption,
                                    weight: SnapbackTheme.FontWeight.heading)
                            )
                            .foregroundColor(SnapbackTheme.Text.secondary)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
                .padding(.horizontal, SnapbackTheme.Padding.large)
                .padding(.vertical, SnapbackTheme.Padding.standard)
                .background(SnapbackTheme.Background.card)
                .clipShape(RoundedRectangle(cornerRadius: SnapbackTheme.CornerRadius.card))
                .overlay(
                    RoundedRectangle(cornerRadius: SnapbackTheme.CornerRadius.card)
                        .strokeBorder(
                            SnapbackTheme.Border.card, lineWidth: SnapbackTheme.BorderWidth.normal)
                )
                .snapbackToastShadow()
            }
            .padding(.top, SnapbackTheme.Padding.extraLarge)
            .transition(.move(edge: .top).combined(with: .opacity))
            .animation(SnapbackTheme.Animation.slow, value: toastManager.isPresented)
        }
    }
}

// MARK: - Toast Container Window

class ToastWindowController: NSWindowController {
    static let shared = ToastWindowController()

    private override init(window: NSWindow?) {
        let window = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 400, height: 100),
            styleMask: [.borderless],
            backing: .buffered,
            defer: false
        )

        window.backgroundColor = .clear
        window.isOpaque = false
        window.hasShadow = false
        window.level = .floating
        window.ignoresMouseEvents = true

        super.init(window: window)

        // Set the content view
        let hostingView = NSHostingView(rootView: ToastContainerView())
        window.contentView = hostingView

        // Position the window at the top center of the main screen
        positionWindow()

        // Observe screen changes to reposition the window
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(screenParametersDidChange),
            name: NSApplication.didChangeScreenParametersNotification,
            object: nil
        )
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    func showToast() {
        if window?.isVisible == false {
            window?.orderFront(nil)
        }
    }

    @objc private func screenParametersDidChange() {
        positionWindow()
    }

    private func positionWindow() {
        guard let window = window, let screen = NSScreen.main else { return }

        // Calculate the window position (top center of the main screen)
        let windowWidth: CGFloat = 400
        let windowHeight: CGFloat = 100
        let screenFrame = screen.visibleFrame

        let x = screenFrame.midX - windowWidth / 2
        let y = screenFrame.maxY - windowHeight - 20  // 20px from the top

        window.setFrame(NSRect(x: x, y: y, width: windowWidth, height: windowHeight), display: true)
    }
}

// MARK: - Toast Container View

struct ToastContainerView: View {
    @ObservedObject private var toastManager = ToastManager.shared

    var body: some View {
        VStack {
            if toastManager.isPresented {
                ToastContentView()
            }
            Spacer()
        }
        .frame(maxWidth: .infinity)
        .onChangeCompat(of: toastManager.isPresented) { newValue in
            if newValue {
                ToastWindowController.shared.showToast()
            }
        }
    }
}
