import AppKit
import Carbon
import CoreGraphics
import Foundation
import KeyboardShortcuts

// MARK: - Main Structs
public struct WindowInfo: Codable {
    public let frame: CGRect
    public let monitorID: UUID?
    public let appBundleIdentifier: String?
    public let isFullscreen: Bool
    public let zOrder: Int  // Lower values are closer to the front (0 is frontmost)

    public init(
        frame: CGRect, monitorID: UUID?, appBundleIdentifier: String?, isFullscreen: Bool,
        zOrder: Int = 0
    ) {
        self.frame = frame
        self.monitorID = monitorID
        self.appBundleIdentifier = appBundleIdentifier
        self.isFullscreen = isFullscreen
        self.zOrder = zOrder
    }
}

struct CustomLayout: Codable, Identifiable, Hashable, Equatable {
    var id = UUID()
    var name: String
    var layout: [CGRect]

    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }

    static func == (lhs: CustomLayout, rhs: CustomLayout) -> Bool {
        return lhs.id == rhs.id
    }
}

// Enhanced display fingerprint for stable identification
struct DisplayFingerprint: Codable, Equatable, Hashable {
    let resolution: CGSize  // Native resolution (stable)
    let physicalSize: CGSize?  // Physical dimensions in mm (for compatibility checking)
    let scaleFactor: CGFloat  // Backing scale factor
    let colorSpace: String?  // Color space identifier
    let isBuiltIn: Bool  // Internal vs external display
    let relativePosition: CGPoint  // Position relative to arrangement center
    let name: String?  // Display name (if available)

    // Simplified 4-tier hardware identification system
    let persistentUUID: String?  // Tier 1: CGDisplayCreateUUIDFromDisplayID (most persistent)
    let hardwareSerial: String?  // Tier 2: Combined CG + EDID serial extraction
    let edidFingerprint: String?  // Tier 3: EDID data hash for hardware uniqueness
    // Tier 4: Intelligent position logic (handled in stableID generation)

    // Computed stable identifier with hardware-level uniqueness
    var stableID: String {
        var components = [
            String(format: "%.0fx%.0f", resolution.width, resolution.height),
            String(format: "%.1f", scaleFactor),
            isBuiltIn ? "builtin" : "external",
            name?.lowercased().replacingOccurrences(of: " ", with: "") ?? "unknown",
        ]

        // Simplified 4-tier identification hierarchy

        // Tier 1: Persistent UUID (gold standard - most stable across reboots)
        if let uuid = persistentUUID, !uuid.isEmpty {
            components.append("uuid_\(uuid.prefix(8))")
        }

        // Tier 2: Hardware Serial (combined CG + EDID extraction)
        else if let serial = hardwareSerial, !serial.isEmpty {
            components.append("serial_\(serial)")
        }

        // Tier 3: EDID Fingerprint (hardware-level hash when no serial)
        else if let edid = edidFingerprint, !edid.isEmpty {
            components.append("edid_\(edid.prefix(8))")
        }

        // Tier 4: Intelligent Position (for truly identical hardware)
        else {
            // Use smart position logic that works with identical monitor swapping
            let intelligentPositionID = createIntelligentPositionID()
            components.append("smart_\(intelligentPositionID)")
        }

        return components.joined(separator: "_")
    }

    /// Create intelligent position ID that works with identical monitor swapping
    private func createIntelligentPositionID() -> String {
        // Create a position-based ID that's more stable for identical monitor scenarios
        // Use a combination of position and display characteristics
        let positionHash = String(format: "%.0f_%.0f", relativePosition.x, relativePosition.y)
        let resolutionHash = String(format: "%.0fx%.0f", resolution.width, resolution.height)
        return "\(resolutionHash)_\(positionHash)"
    }

    // Check if this display has hardware-level unique identification (simplified)
    var hasHardwareUniqueID: Bool {
        return (persistentUUID != nil && !persistentUUID!.isEmpty)
            || (hardwareSerial != nil && !hardwareSerial!.isEmpty)
            || (edidFingerprint != nil && !edidFingerprint!.isEmpty)
    }

    // Check if this display is identical hardware to another (same model, no unique ID)
    func isIdenticalHardware(to other: DisplayFingerprint) -> Bool {
        return resolution == other.resolution && scaleFactor == other.scaleFactor
            && isBuiltIn == other.isBuiltIn && name == other.name && !hasHardwareUniqueID
            && !other.hasHardwareUniqueID
    }

    // Check if this display is compatible with another for workspace migration
    func isCompatibleForMigration(with other: DisplayFingerprint) -> Bool {
        // Essential requirements for workspace compatibility
        return resolution == other.resolution  // Same pixel space required
            && scaleFactor == other.scaleFactor  // Same UI scaling required
            && isBuiltIn == other.isBuiltIn  // Same coordinate system required
    }

    // Check if displays have matching physical dimensions (for preference, not requirement)
    func hasMatchingPhysicalDimensions(with other: DisplayFingerprint, tolerance: CGFloat = 5.0)
        -> Bool
    {
        // If both have physical dimensions, check if they match within tolerance
        if let thisPhysical = physicalSize, let otherPhysical = other.physicalSize {
            let widthDiff = abs(thisPhysical.width - otherPhysical.width)
            let heightDiff = abs(thisPhysical.height - otherPhysical.height)
            return widthDiff <= tolerance && heightDiff <= tolerance
        }

        // If physical dimensions unavailable, can't verify match
        return false
    }

    // Get physical compatibility score (0.0 to 1.0) for migration decisions
    func physicalCompatibilityScore(with other: DisplayFingerprint) -> Float {
        // Perfect match for identical displays
        if self == other { return 1.0 }

        var score: Float = 0.0

        // Resolution match (essential)
        if resolution == other.resolution {
            score += 0.4
        } else {
            return 0.0  // No compatibility without matching resolution
        }

        // Scale factor match (essential)
        if scaleFactor == other.scaleFactor {
            score += 0.2
        } else {
            return 0.0  // No compatibility without matching scale
        }

        // Display type match (essential)
        if isBuiltIn == other.isBuiltIn {
            score += 0.1
        } else {
            return 0.0  // No compatibility between built-in and external
        }

        // Physical dimensions compatibility (if available)
        if let thisPhysical = physicalSize, let otherPhysical = other.physicalSize {
            let widthDiff = abs(thisPhysical.width - otherPhysical.width)
            let heightDiff = abs(thisPhysical.height - otherPhysical.height)
            let maxDiff = max(widthDiff, heightDiff)

            if maxDiff <= 5.0 {
                score += 0.2  // Perfect physical match
            } else if maxDiff <= 20.0 {
                score += 0.1  // Close physical match
            }
            // No points for poor physical match
        } else {
            // No physical dimensions available, use name matching
            if name == other.name {
                score += 0.15  // Name match as fallback
            }
        }

        // Color space compatibility
        if colorSpace == other.colorSpace {
            score += 0.1
        }

        return min(score, 1.0)
    }

    // Calculate similarity score with another fingerprint (0.0 to 1.0)
    func similarity(to other: DisplayFingerprint) -> Float {
        // Simplified hardware-level exact match (4-tier system)
        if hasHardwareUniqueID && other.hasHardwareUniqueID {
            // Tier 1: Persistent UUID (highest confidence)
            if persistentUUID == other.persistentUUID && persistentUUID != nil {
                return 1.0  // Perfect match via persistent UUID
            }

            // Tier 2: Hardware serial (combined CG + EDID)
            if hardwareSerial == other.hardwareSerial && hardwareSerial != nil {
                return 1.0  // Perfect match via hardware serial
            }

            // Tier 3: EDID fingerprint
            if edidFingerprint == other.edidFingerprint && edidFingerprint != nil {
                return 1.0  // Perfect match via EDID fingerprint
            }

            // Different hardware with unique IDs = no match
            return 0.0
        }

        // For identical hardware (same model, no unique IDs), use traditional scoring
        var score: Float = 0.0
        var factors = 0

        // Resolution match (most important)
        if resolution == other.resolution {
            score += 0.4
        } else {
            let resolutionDiff =
                abs(resolution.width - other.resolution.width)
                + abs(resolution.height - other.resolution.height)
            let maxDimension = max(
                resolution.width + resolution.height,
                other.resolution.width + other.resolution.height)
            score += 0.4 * max(0, 1 - Float(resolutionDiff / maxDimension))
        }
        factors += 1

        // Scale factor match
        if abs(scaleFactor - other.scaleFactor) < 0.1 {
            score += 0.2
        }
        factors += 1

        // Built-in status match
        if isBuiltIn == other.isBuiltIn {
            score += 0.2
        }
        factors += 1

        // Name match (if available)
        if let name1 = name?.lowercased(), let name2 = other.name?.lowercased() {
            if name1 == name2 {
                score += 0.2
            } else if name1.contains(name2) || name2.contains(name1) {
                score += 0.1
            }
        }
        factors += 1

        return score
    }
}

// Structure to store display arrangement metadata using fingerprinting
struct DisplayArrangementInfo: Codable, Equatable {
    let createdAt: Date  // When this arrangement was captured
    let displayFingerprints: [DisplayFingerprint]  // Stable display characteristics
    let mainDisplayFingerprint: DisplayFingerprint?  // Main display fingerprint
    let arrangementFingerprint: String  // Overall arrangement signature

    static func == (lhs: DisplayArrangementInfo, rhs: DisplayArrangementInfo) -> Bool {
        // Use fingerprint-based comparison for stable matching
        return lhs.arrangementFingerprint == rhs.arrangementFingerprint
    }

    // Calculate similarity score with another arrangement (0.0 to 1.0)
    func similarity(to other: DisplayArrangementInfo) -> Float {
        guard displayFingerprints.count == other.displayFingerprints.count else {
            return 0.0
        }

        // Find best matching pairs using Hungarian algorithm approximation
        var totalSimilarity: Float = 0.0
        var usedIndices = Set<Int>()

        for fingerprint in displayFingerprints {
            var bestMatch: Float = 0.0
            var bestIndex = -1

            for (index, otherFingerprint) in other.displayFingerprints.enumerated() {
                if !usedIndices.contains(index) {
                    let similarity = fingerprint.similarity(to: otherFingerprint)
                    if similarity > bestMatch {
                        bestMatch = similarity
                        bestIndex = index
                    }
                }
            }

            if bestIndex >= 0 {
                usedIndices.insert(bestIndex)
                totalSimilarity += bestMatch
            }
        }

        return totalSimilarity / Float(displayFingerprints.count)
    }

    // Helper to determine if this arrangement is compatible with the current one
    func isCompatibleWith(currentArrangement: DisplayArrangementInfo) -> Bool {
        // Check if arrangements have same number of displays and similar fingerprints
        return displayFingerprints.count == currentArrangement.displayFingerprints.count
            && similarity(to: currentArrangement) > 0.8
    }
}

struct Workspace: Codable, Identifiable, Hashable, Equatable {
    let id: UUID?  // Keep optional for now
    var name: String
    var windowInfos: [WindowInfo]
    var shortcutKeyCode: UInt16?
    var shortcutModifiers: UInt?  // Raw value of NSEvent.ModifierFlags

    // Display arrangement metadata
    var displayArrangement: DisplayArrangementInfo?

    var customLayout: CustomLayout?

    // Per-workspace setting for closing non-workspace windows
    // Optional for backward compatibility - defaults to false if not set
    var closeNonWorkspaceWindows: Bool?

    // Use global helper function for display
    var shortcutDisplayString: String? {
        // Just use the existing method for backward compatibility
        if let keyCode = shortcutKeyCode, let modifiers = shortcutModifiers {
            return formatShortcut(keyCode: keyCode, modifiers: modifiers)
        }
        return nil
    }

    static func == (lhs: Workspace, rhs: Workspace) -> Bool {
        return lhs.id == rhs.id
    }

    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
}
