import CryptoKit
import Foundation

/// Network manager for Snapback License API with security headers and authentication
/// Handles HTTP requests, authentication, retry logic, and request signing
class LicenseAPINetworkManager {
    static let shared = LicenseAPINetworkManager()

    private let configuration = LicenseAPIConfiguration.shared
    private let session: URLSession

    init() {
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = configuration.requestTimeout
        config.timeoutIntervalForResource = configuration.requestTimeout * 2
        self.session = URLSession(configuration: config)
    }

    // MARK: - Public API

    /// Perform a network request with automatic retry and error handling
    /// - Parameters:
    ///   - endpoint: API endpoint path (e.g., "/licenses/validate")
    ///   - method: HTTP method
    ///   - body: Request body object (will be JSON encoded)
    ///   - requiresAuth: Whether the request requires authentication
    /// - Returns: Decoded response object
    func request<RequestBody: Codable, ResponseBody: Codable>(
        endpoint: String,
        method: HTTPMethod,
        body: RequestBody? = nil,
        requiresAuth: Bool = false
    ) async throws -> ResponseBody {
        let retryConfig = RetryConfiguration.default
        var lastError: LicenseAPIError?

        for attempt in 0..<retryConfig.maxAttempts {
            do {
                return try await performSingleRequest(
                    endpoint: endpoint,
                    method: method,
                    body: body,
                    requiresAuth: requiresAuth
                )
            } catch let error as LicenseAPIError {
                lastError = error

                // Check if we should retry
                guard
                    APIRetryHelper.shouldRetry(
                        error: error, attempt: attempt, configuration: retryConfig)
                else {
                    throw error
                }

                // Calculate delay and wait
                let delay = APIRetryHelper.calculateDelay(
                    for: attempt, configuration: retryConfig, error: error)
                try await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))

            } catch {
                // Non-API errors are not retryable
                throw LicenseAPIError.networkError(error.localizedDescription)
            }
        }

        // If we get here, all retries failed
        throw lastError ?? LicenseAPIError.networkError("All retry attempts failed")
    }

    /// Perform a simple GET request without body
    func get<ResponseBody: Codable>(
        endpoint: String,
        requiresAuth: Bool = false
    ) async throws -> ResponseBody {
        return try await request(
            endpoint: endpoint,
            method: .GET,
            body: EmptyBody?.none,
            requiresAuth: requiresAuth
        )
    }

    /// Perform a POST request with body
    func post<RequestBody: Codable, ResponseBody: Codable>(
        endpoint: String,
        body: RequestBody,
        requiresAuth: Bool = false
    ) async throws -> ResponseBody {
        return try await request(
            endpoint: endpoint,
            method: .POST,
            body: body,
            requiresAuth: requiresAuth
        )
    }

    /// Perform a PUT request with body
    func put<RequestBody: Codable, ResponseBody: Codable>(
        endpoint: String,
        body: RequestBody,
        requiresAuth: Bool = false
    ) async throws -> ResponseBody {
        return try await request(
            endpoint: endpoint,
            method: .PUT,
            body: body,
            requiresAuth: requiresAuth
        )
    }

    /// Perform a DELETE request without body
    func delete<ResponseBody: Codable>(
        endpoint: String,
        requiresAuth: Bool = true
    ) async throws -> ResponseBody {
        return try await request(
            endpoint: endpoint,
            method: .DELETE,
            body: EmptyBody?.none,
            requiresAuth: requiresAuth
        )
    }

    // MARK: - Private Implementation

    private func performSingleRequest<RequestBody: Codable, ResponseBody: Codable>(
        endpoint: String,
        method: HTTPMethod,
        body: RequestBody?,
        requiresAuth: Bool
    ) async throws -> ResponseBody {
        // Build URL
        guard let url = buildURL(for: endpoint) else {
            throw LicenseAPIError.networkError("Invalid URL for endpoint: \(endpoint)")
        }

        // Create request
        var request = URLRequest(url: url)
        request.httpMethod = method.rawValue

        // Add headers
        try addHeaders(to: &request, requiresAuth: requiresAuth)

        // Add body if present
        if let body = body {
            do {
                request.httpBody = try JSONEncoder().encode(body)
            } catch {
                throw LicenseAPIError.encodingError(
                    "Failed to encode request body: \(error.localizedDescription)")
            }
        }

        // Sign request if enabled
        if configuration.isRequestSigningEnabled {
            try signRequest(&request)
        }

        // Perform request
        let networkStartTime = Date()
        print(
            "🔍 NETWORK_TIMING: Starting network request to \(endpoint) at \(networkStartTime)")

        do {
            let (data, response) = try await session.data(for: request)

            let networkEndTime = Date()
            let networkDuration = networkEndTime.timeIntervalSince(networkStartTime)
            print(
                "🔍 NETWORK_TIMING: Network request to \(endpoint) completed in \(String(format: "%.3f", networkDuration))s"
            )

            return try handleResponse(data: data, response: response)
        } catch {
            let networkEndTime = Date()
            let networkDuration = networkEndTime.timeIntervalSince(networkStartTime)
            print(
                "🔍 NETWORK_TIMING: Network request to \(endpoint) failed after \(String(format: "%.3f", networkDuration))s - \(error)"
            )
            throw mapNetworkError(error)
        }
    }

    private func buildURL(for endpoint: String) -> URL? {
        let baseURL = configuration.baseURL
        let fullPath = endpoint.hasPrefix("/") ? endpoint : "/\(endpoint)"
        return URL(string: baseURL + fullPath)
    }

    private func addHeaders(to request: inout URLRequest, requiresAuth: Bool) throws {
        // Content-Type for JSON requests
        if request.httpMethod != "GET" {
            request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        }

        // User-Agent
        request.setValue(configuration.userAgent, forHTTPHeaderField: "User-Agent")

        // Accept header
        request.setValue("application/json", forHTTPHeaderField: "Accept")

        // Authorization header if required
        if requiresAuth {
            guard let deviceToken = configuration.getDeviceToken() else {
                throw LicenseAPIError.unauthorized
            }
            request.setValue("Bearer \(deviceToken)", forHTTPHeaderField: "Authorization")
        }

        // API Key if available
        if let apiKey = configuration.getAPIKey() {
            request.setValue(apiKey, forHTTPHeaderField: "X-API-Key")
        }

        // Request ID for tracking
        let requestId = UUID().uuidString
        request.setValue(requestId, forHTTPHeaderField: "X-Request-ID")

        // App version information
        request.setValue(LicenseKeyFormatter.getAppVersion(), forHTTPHeaderField: "X-App-Version")
        request.setValue(LicenseKeyFormatter.getBuildNumber(), forHTTPHeaderField: "X-App-Build")
    }

    private func signRequest(_ request: inout URLRequest) throws {
        // Implementation would depend on the specific signing algorithm required
        // This is a placeholder for future implementation

        // Example: HMAC-SHA256 signing
        // let timestamp = String(Int(Date().timeIntervalSince1970))
        // let payload = "\(request.httpMethod ?? "GET")\n\(request.url?.path ?? "")\n\(timestamp)"
        // let signature = HMAC<SHA256>.authenticationCode(for: Data(payload.utf8), using: signingKey)
        // request.setValue("HMAC-SHA256 \(signature.hexString)", forHTTPHeaderField: "Authorization")
        // request.setValue(timestamp, forHTTPHeaderField: "X-Timestamp")
    }

    private func handleResponse<ResponseBody: Codable>(
        data: Data,
        response: URLResponse
    ) throws -> ResponseBody {
        guard let httpResponse = response as? HTTPURLResponse else {
            throw LicenseAPIError.invalidResponse
        }

        // Check for success status codes
        guard 200...299 ~= httpResponse.statusCode else {
            throw LicenseAPIError(httpStatusCode: httpResponse.statusCode, data: data)
        }

        // Decode response with flexible date decoding strategy
        do {
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = createFlexibleDateDecodingStrategy()
            return try decoder.decode(ResponseBody.self, from: data)
        } catch {
            throw LicenseAPIError.decodingError(
                "Failed to decode response: \(error.localizedDescription)")
        }
    }

    private func mapNetworkError(_ error: Error) -> LicenseAPIError {
        if let urlError = error as? URLError {
            switch urlError.code {
            case .notConnectedToInternet, .networkConnectionLost:
                return .noInternetConnection
            case .timedOut:
                return .timeout
            default:
                return .networkError(urlError.localizedDescription)
            }
        }

        return .networkError(error.localizedDescription)
    }
}

// MARK: - Supporting Types

/// HTTP methods supported by the API
enum HTTPMethod: String {
    case GET = "GET"
    case POST = "POST"
    case PUT = "PUT"
    case PATCH = "PATCH"
    case DELETE = "DELETE"
}

/// Empty body type for requests without body
private struct EmptyBody: Codable {}

// MARK: - Date Decoding Helper

extension LicenseAPINetworkManager {
    /// Custom date decoding strategy that handles both ISO8601 with and without fractional seconds
    internal func createFlexibleDateDecodingStrategy() -> JSONDecoder.DateDecodingStrategy {
        return .custom { decoder in
            let container = try decoder.singleValueContainer()
            let dateString = try container.decode(String.self)

            // Try ISO8601 with fractional seconds first
            let iso8601WithFractionalSeconds = ISO8601DateFormatter()
            iso8601WithFractionalSeconds.formatOptions = [
                .withInternetDateTime, .withFractionalSeconds,
            ]
            if let date = iso8601WithFractionalSeconds.date(from: dateString) {
                return date
            }

            // Fallback to standard ISO8601 without fractional seconds
            let iso8601Standard = ISO8601DateFormatter()
            iso8601Standard.formatOptions = [.withInternetDateTime]
            if let date = iso8601Standard.date(from: dateString) {
                return date
            }

            throw DecodingError.dataCorruptedError(
                in: container, debugDescription: "Invalid date format: \(dateString)")
        }
    }
}

// MARK: - Extensions

extension Data {
    /// Convert data to hex string
    var hexString: String {
        return map { String(format: "%02hhx", $0) }.joined()
    }
}

extension SymmetricKey {
    /// Create symmetric key from string
    init(string: String) {
        let data = Data(string.utf8)
        self.init(data: data)
    }
}
