import SwiftUI

struct LicenseSettingsView: View {
    @StateObject private var licenseManager = LicenseManager.shared
    @EnvironmentObject private var appDelegate: AppDelegate
    @State private var licenseKeyInput: String = ""
    @State private var emailInput: String = ""
    @State private var showingClearConfirmation = false

    // MARK: - Helper Methods

    // MARK: - Computed Properties

    /// Determines when to show device management section
    /// Uses centralized debug UI mode instead of individual toggles
    private var shouldShowDeviceManagement: Bool {
        let result: Bool

        #if DEBUG
            // In debug mode, show for testing purposes (centralized debug UI mode) OR when max devices reached
            result = FeatureFlags.debugUIMode || isMaxDevicesReachedError
        #else
            // In production, only show when there's a "max devices reached" error
            result = isMaxDevicesReachedError
        #endif

        return result
    }

    /// Check if the current error is specifically a "maximum devices reached" error
    private var isMaxDevicesReachedError: Bool {
        guard let errorMessage = licenseManager.lastError else { return false }

        let lowercaseError = errorMessage.lowercased()
        let isMaxDevicesError =
            lowercaseError.contains("maximum") && lowercaseError.contains("device")
            || lowercaseError.contains("device limit")
            || lowercaseError.contains("max devices")

        return isMaxDevicesError
    }

    // MARK: - Main View

    var body: some View {
        VStack(spacing: 0) {
            ScrollView {
                VStack(alignment: .leading, spacing: 16) {
                    // License Status Section
                    Text("License Status")
                        .snapbackSectionTitleStyle()

                    GroupBox {
                        VStack(spacing: 0) {
                            // Current Status Row
                            HStack {
                                Image.systemCompat(statusIcon)
                                    .foregroundColor(licenseManager.licenseStatus.color)
                                    .frame(width: 16)

                                Text("Status")

                                Spacer()

                                Text(licenseManager.licenseStatus.displayName)
                                    .foregroundColor(licenseManager.licenseStatus.color)
                                    .font(
                                        .system(size: SnapbackTheme.FontSize.body, weight: .medium))
                            }
                            .snapbackRowStyle()

                            // License Information (if available)
                            if let info = licenseManager.licenseInfo {
                                Divider()

                                VStack(spacing: 0) {
                                    // Registered User
                                    HStack {
                                        Image.systemCompat("person")
                                            .foregroundColor(.secondary)
                                            .frame(width: 16)

                                        Text("Registered To")

                                        Spacer()

                                        Text(info.registeredUser)
                                            .foregroundColor(.secondary)
                                    }
                                    .snapbackRowStyle()

                                    // Device Registration Status (if available)
                                    if let deviceToken = UserDefaults.standard.string(
                                        forKey: "SnapbackDeviceToken"),
                                        !deviceToken.isEmpty
                                    {
                                        Divider()

                                        HStack {
                                            Image.systemCompat("desktopcomputer")
                                                .foregroundColor(.secondary)
                                                .frame(width: 16)

                                            Text("Device Registration")

                                            Spacer()

                                            Text("Registered")
                                                .foregroundColor(.green)
                                        }
                                        .snapbackRowStyle()
                                    }
                                }
                            }
                        }
                    }

                    licenseInputSection

                    // Device Management Section (conditional display)
                    if shouldShowDeviceManagement {
                        DeviceManagementView(
                            isMaxDevicesReachedContext: isMaxDevicesReachedError
                        )
                    }

                    Spacer(minLength: 20)
                }
                .padding()
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .onAppear {
            setupOnAppear()
        }
        .onChangeCompat(of: licenseManager.licenseKey) { newKey in
            licenseKeyInput = newKey
        }
        .onChangeCompat(of: licenseManager.userEmail) { newEmail in
            emailInput = newEmail
        }
        .onChangeCompat(of: licenseManager.licenseStatus) { newStatus in
            handleLicenseStatusChange(newStatus)
        }
        .alert("Unregister License", isPresented: $showingClearConfirmation) {
            Button("Cancel", role: .cancel) {}
            Button("Unregister", role: .destructive) {
                licenseManager.clearLicense()
                licenseKeyInput = ""
                emailInput = ""
            }
        } message: {
            Text(
                "Are you sure you want to unregister this license? This will remove the license from this device. You can re-register it later using the same license key."
            )
        }
    }

    // MARK: - View Components

    private var licenseInputSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("License Information")
                .snapbackSectionTitleStyle()

            GroupBox {
                VStack(spacing: 0) {
                    // Email Input Row - following General tab pattern
                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Text("Email Address")

                            Spacer()

                            if licenseManager.isValidating {
                                ProgressView()
                                    .scaleEffect(0.8)
                            } else {
                                TextField("<EMAIL>", text: $emailInput)
                                    .textFieldStyle(.roundedBorder)
                                    .disabled(
                                        licenseManager.isValidating
                                            || (licenseManager.licenseStatus == .valid
                                                && !licenseManager.licenseKey.isEmpty)
                                    )
                                    .disableAutocorrection(true)
                                    .modifier(EmailTextContentTypeModifier())
                                    .frame(width: 200)
                            }
                        }

                        if licenseManager.licenseStatus == .valid
                            && !licenseManager.licenseKey.isEmpty
                        {
                            Text("Email and license key are locked when a valid license is active.")
                                .snapbackCaptionStyle()
                        }
                    }
                    .snapbackRowStyle()

                    Divider()

                    // License Key Input Row - following General tab pattern
                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Text("License Key")

                            Spacer()

                            TextField("XXXX-XXXX-XXXX-XXXX-XXXX-XXXX", text: $licenseKeyInput)
                                .textFieldStyle(.roundedBorder)
                                .font(.system(.body, design: .monospaced))
                                .disabled(
                                    licenseManager.isValidating
                                        || (licenseManager.licenseStatus == .valid
                                            && !licenseManager.licenseKey.isEmpty)
                                )
                                .disableAutocorrection(true)
                                .autocorrectionDisabled(true)
                                .textContentType(.none)
                                .frame(width: 200)
                                .onSubmit {
                                    Task {
                                        await licenseManager.setLicenseKeyAndEmail(
                                            licenseKeyInput, email: emailInput)
                                    }
                                }
                                .onChangeCompat(of: licenseKeyInput) { oldValue, newValue in
                                    // Auto-format license key with dashes
                                    let cleaned = newValue.replacingOccurrences(of: "-", with: "")
                                        .uppercased()

                                    // Use the correct license key length from LicenseKeyFormatter
                                    if cleaned.count <= LicenseKeyFormatter.keyLength {
                                        let formatted = formatLicenseKey(cleaned)
                                        if formatted != newValue {
                                            licenseKeyInput = formatted
                                        }
                                    } else {
                                        // Truncate to maximum allowed length instead of reverting
                                        let truncated = String(
                                            cleaned.prefix(LicenseKeyFormatter.keyLength))
                                        licenseKeyInput = formatLicenseKey(truncated)
                                    }
                                }
                        }
                    }
                    .snapbackRowStyle()

                    Divider()

                    // Action Buttons Section - following General tab pattern
                    VStack(alignment: .leading, spacing: 4) {
                        // Error Message
                        if let error = licenseManager.lastError {
                            Text(error)
                                .snapbackCaptionStyle()
                                .foregroundColor(.red)
                                .padding(.bottom, 8)
                        }

                        // Action Buttons - show based on license status
                        if licenseManager.licenseStatus == .valid {
                            // Show unregister button for valid licenses
                            HStack {
                                Button("Unregister") {
                                    showingClearConfirmation = true
                                }
                                .buttonStyle(.bordered)
                                .foregroundColor(.red)

                                Spacer()
                            }
                        } else {
                            // Show validate and unregister buttons for non-licensed states
                            HStack(spacing: SnapbackTheme.Padding.standard) {
                                // Only show Validate button when not already licensed
                                Button("Validate License") {
                                    Task {
                                        await licenseManager.setLicenseKeyAndEmail(
                                            licenseKeyInput, email: emailInput)
                                    }
                                }
                                .buttonStyle(.borderedProminent)
                                .disabled(
                                    licenseKeyInput.isEmpty || emailInput.isEmpty
                                        || licenseManager.isValidating)

                                // Unregister Button (only show if there's a license key)
                                if !licenseManager.licenseKey.isEmpty {
                                    Button("Unregister") {
                                        showingClearConfirmation = true
                                    }
                                    .buttonStyle(.bordered)
                                    .foregroundColor(SnapbackTheme.Text.error)
                                }

                                Spacer()
                            }
                        }
                    }
                    .snapbackRowStyle()
                }
            }
        }
    }

    // MARK: - Helper Methods

    private func setupOnAppear() {
        // Load current license key and email into input fields
        licenseKeyInput = licenseManager.licenseKey
        emailInput = licenseManager.userEmail

        // Debug logging for UI state
        print(
            "🔍 LICENSE UI DEBUG: onAppear - licenseStatus: \(licenseManager.licenseStatus), licenseKey: '\(licenseManager.licenseKey)', isValidating: \(licenseManager.isValidating)"
        )
    }

    private func handleLicenseStatusChange(_ newStatus: LicenseStatus) {
        // Debug logging for license status changes
        print(
            "🔍 LICENSE UI DEBUG: licenseStatus changed to: \(newStatus), licenseKey: '\(licenseManager.licenseKey)'"
        )
    }

    // MARK: - Helper Properties

    private var statusIcon: String {
        switch licenseManager.licenseStatus {
        case .valid:
            return "checkmark.circle.fill"
        case .expired:
            return "exclamationmark.triangle.fill"
        case .invalid:
            return "xmark.circle.fill"
        case .unlicensed:
            return "questionmark.circle.fill"
        }
    }

    /// Format license key with dashes (XXXX-XXXX-XXXX-XXXX)
    private func formatLicenseKey(_ key: String) -> String {
        // Remove all non-alphanumeric characters
        let cleaned = key.replacingOccurrences(
            of: "[^A-Za-z0-9]", with: "", options: .regularExpression)

        // Add dashes every 4 characters
        var formatted = ""
        for (index, character) in cleaned.enumerated() {
            if index > 0 && index % 4 == 0 {
                formatted += "-"
            }
            formatted += String(character)
        }

        return formatted
    }
}

#Preview {
    LicenseSettingsView()
        .frame(width: 600, height: 500)
        .background(SnapbackTheme.Background.window)
}
