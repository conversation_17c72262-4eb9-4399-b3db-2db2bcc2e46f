# Snapback User Interaction Flows Documentation

## Overview
This document traces the complete user interaction flows for Snapback's core functionality, providing detailed step-by-step descriptions of user actions, visual changes, code execution paths, data processing, and completion states.

---

## 1. Workspace Creation Flow

### 1.1 Trigger
**User Action:** User presses `Ctrl+Option+S` (Save Workspace shortcut) or selects "Save Current Workspace" from menu bar

### 1.2 UI Response
**Immediate Visual Feedback:**
- No immediate visual change (shortcut processing happens in background)
- System begins capturing window information

### 1.3 Code Execution Path

#### Phase 1: Shortcut Handler Activation
```
ShortcutService.registerWorkspaceShortcuts()
├── KeyboardShortcutsBridge.registerShortcutHandler(for: .saveWorkspace)
└── Handler Callback: appDelegate.saveCurrentWorkspace()
```

#### Phase 2: Window Capture Initiation
```
AppDelegate.saveCurrentWorkspace()
├── Logger.info("saveCurrentWorkspace called")
├── WindowCaptureService.getCurrentAppsAndWindowPositionsNormalized()
│   ├── WindowCaptureService.getCurrentAppsAndWindowPositions()
│   │   ├── CGWindowListCopyWindowInfo() // Core Graphics API call
│   │   ├── Filter windows (exclude desktop elements, Snapback windows)
│   │   ├── Extract window bounds, app bundle IDs, z-order
│   │   └── Return [WindowInfo] array
│   └── For each WindowInfo:
│       ├── WindowLayoutManager.findBestDisplay(for: windowFrame)
│       ├── createConsistentUUID(from: displayInfo.id)
│       └── WindowLayoutManager.normalizeFrameWithLog()
└── windowManager.showSaveWorkspacePopup(windowInfos: windowInfos)
```

### 1.4 UI Response - Modal Presentation
**Visual Changes:**
- SaveWorkspaceView modal appears with translucent background
- Window positioned intelligently via WindowPositioningService
- Modal contains:
  - Workspace name field (focused)
  - App selection list with toggles
  - Shortcut recorder field
  - Settings toggles
  - Save/Cancel buttons

#### Phase 3: Modal Window Creation
```
WindowManager.showSaveWorkspacePopup(windowInfos:)
├── Check for existing save popup (prevent duplicates)
├── Create SaveWorkspaceView with windowInfos
├── Create NSWindow with hosting view
├── WindowPositioningService.shared.positionWindow()
│   ├── getMainDisplay()
│   ├── centerWindow() with top-snap positioning
│   └── Avoid dock/menu bar overlap
├── Set window properties (floating level, delegate)
└── makeKeyAndOrderFront() + NSApp.activate()
```

### 1.5 Data Processing - App Selection Preparation
```
SaveWorkspaceView.prepareAppItems()
├── Filter windowInfos (exclude Snapback's own windows)
├── Convert to AppSelectionItem objects
│   ├── AppSelectionItem.init(windowInfo:, isSelected: true)
│   ├── Compute appName from bundle identifier
│   ├── Generate previewColor via AppColorUtility
│   └── Load app icon
├── Sort by app name, then by display
└── Update selectedCount binding
```

### 1.6 User Interaction - Form Completion
**User Actions:**
1. **Name Entry:** User types workspace name
   - Real-time validation (duplicate detection)
   - Character limit enforcement
2. **App Selection:** User toggles apps on/off
   - Visual feedback with selection count updates
   - Color-coded app identification
3. **Shortcut Recording:** User records keyboard shortcut
   - Conflict detection with system/app shortcuts
   - Real-time validation feedback
4. **Settings:** User configures per-workspace options
5. **Save Button:** User clicks Save

### 1.7 Code Execution Path - Save Operation
```
SaveWorkspaceView.saveWorkspace()
├── Validation checks (name, selected apps)
├── Create display arrangement fingerprint
│   └── WindowLayoutManager.captureCurrentDisplayArrangement()
├── Process shortcut data (keyCode, modifiers)
├── Filter selected windows only
├── Create Workspace object
│   ├── UUID generation
│   ├── WindowInfo array (selected only)
│   ├── Shortcut data
│   ├── Display arrangement metadata
│   └── Per-workspace settings
├── workspaceService.addWorkspace(newWorkspace)
│   ├── License validation (freemium model)
│   ├── Workspace limit enforcement
│   ├── Add to workspaces array
│   ├── saveWorkspacesInternal() // Persistence
│   └── CloudKit sync (if enabled)
└── Post-save actions
```

### 1.8 Visual Feedback - Success State
**UI Updates:**
- Loading toast appears: "Saving workspace..."
- Modal dismisses with animation
- Success toast: "Workspace Saved - [Name] has been saved with [N] apps"
- Menu bar updates with new workspace item

#### Phase 4: Shortcut Registration
```
SaveWorkspaceView (post-save)
├── appDelegate.shortcutService.registerWorkspaceShortcutImmediately()
├── ShortcutService.registerWorkspaceShortcut()
│   ├── Create KeyboardShortcuts.Name for workspace
│   ├── Set shortcut in KeyboardShortcuts framework
│   └── Register handler: workspaceService.triggerRestoreWorkspace()
└── NotificationCenter.post(.refreshStatusMenu)
```

### 1.9 Completion
**Final State:**
- Workspace saved to UserDefaults and CloudKit
- Keyboard shortcut registered and active
- Menu bar updated with new workspace
- User returns to normal application state
- Toast notification confirms success

---

## 2. Workspace Restoration Flow

### 2.1 Trigger
**User Actions:**
- Press workspace-specific keyboard shortcut (e.g., `Ctrl+Option+1`)
- Select workspace from menu bar dropdown
- Click workspace in Workspace Manager

### 2.2 UI Response
**Immediate Visual Feedback:**
- Loading toast appears: "Restoring Workspace - Loading [Workspace Name]..."
- Status message updates in real-time during restoration

### 2.3 Code Execution Path

#### Phase 1: Restoration Initiation
```
Trigger Sources:
├── Keyboard Shortcut: ShortcutService handler → workspaceService.triggerRestoreWorkspace()
├── Menu Item: AppDelegate.restoreSavedWorkspaceMenuItem() → workspaceService.triggerRestoreWorkspace()
└── UI Button: WorkspaceManagerView → workspaceService.triggerRestoreWorkspace()

WorkspaceService.triggerRestoreWorkspace(workspace:)
├── Set @Published isRestoring = true
├── Update @Published restorationStatusMessage
├── ToastManager.shared.showLoading()
└── Dispatch to background queue: restoreWorkspaceInternal()
```

#### Phase 2: Display Arrangement Analysis
```
WorkspaceService.restoreWorkspaceInternal(workspace:)
├── updateStatus("Phase 1: Launching apps...")
├── WindowLayoutManager.captureCurrentDisplayArrangement()
├── Compare with workspace.displayArrangement
└── If critical change detected:
    ├── DisplayChangeMonitorService.hasCriticalChanges()
    │   ├── Analyze display fingerprints
    │   ├── Determine change type (added/removed/main display changed)
    │   └── Check if change is critical
    ├── Log prevention reason
    ├── Show error toast to user
    └── Return early (prevent restoration)
```

### 2.4 Data Processing - Window Restoration

#### Phase 3: App Launching
```
For each unique app in workspace.windowInfos:
├── Extract bundle identifier
├── Check if app is running
├── If not running:
│   ├── NSWorkspace.shared.launchApplication()
│   ├── Wait for app launch (with timeout)
│   └── updateStatus("Launching [AppName]...")
└── Collect running app references
```

#### Phase 4: Window Positioning
```
updateStatus("Phase 2: Positioning windows...")
For each WindowInfo in workspace:
├── Find running app by bundle identifier
├── Get app's windows via Accessibility API
├── Match window to WindowInfo (by size, position heuristics)
├── Calculate target position:
│   ├── If display arrangement changed: use migrated coordinates
│   ├── Else: use original normalized coordinates
│   └── WindowLayoutManager.denormalizeFrame()
├── WindowMover.moveWindow()
│   ├── WindowCalculationService.calculateWindowRect()
│   ├── DisplayScaleManager coordinate conversion
│   ├── Accessibility API: AXUIElementSetAttributeValue()
│   └── Retry mechanism for positioning accuracy
└── Verify final position
```

### 2.5 Visual Feedback - Progress Updates
**UI Updates During Restoration:**
- Toast message updates: "Phase 1: Launching apps..."
- Toast message updates: "Phase 2: Positioning windows..."
- Real-time status in WorkspaceManagerView (if open)
- Individual window movements visible to user

### 2.6 Completion
```
WorkspaceService.restoreWorkspaceInternal() completion:
├── updateStatus("Restoration complete")
├── Set @Published isRestoring = false
├── ToastManager.shared.completeLoading()
│   ├── Success toast: "Workspace Restored - [Name] loaded successfully"
│   └── Auto-dismiss after 3 seconds
├── Log restoration summary
└── Return to main thread
```

**Final State:**
- All workspace windows positioned correctly
- Apps launched and active
- User can interact with restored workspace
- Success feedback provided via toast

---

## 3. Window Snapping Flow (Keyboard Shortcut)

### 3.1 Trigger
**User Action:** User presses window management shortcut (e.g., `Ctrl+Option+Left` for left half)

### 3.2 UI Response
**Immediate Visual Feedback:**
- No immediate UI change (processing happens in background)
- Window movement occurs within ~100ms

### 3.3 Code Execution Path

#### Phase 1: Shortcut Detection
```
KeyboardShortcuts Framework Detection:
├── System captures key combination
├── KeyboardShortcuts.onKeyDown(for: .leftHalf) triggered
└── ShortcutService registered handler executed

ShortcutService.registerWindowManagementShortcuts():
├── Handler for .leftHalf: snappingService.snapFrontmostWindow(to: .leftHalf)
├── Handler for .rightHalf: snappingService.snapFrontmostWindow(to: .rightHalf)
├── Handler for .topHalf: snappingService.snapFrontmostWindow(to: .topHalf)
└── [Additional handlers for quarters, thirds, fullscreen...]
```

#### Phase 2: Window Snapping Service
```
WindowSnappingService.snapFrontmostWindow(to: position)
├── Logger.debug("Snapping frontmost window to [position]")
├── Task.async:
│   ├── getFrontmostWindow() // Accessibility API
│   │   ├── AXUIElementCreateSystemWide()
│   │   ├── AXUIElementCopyAttributeValue(kAXFocusedApplicationAttribute)
│   │   └── AXUIElementCopyAttributeValue(kAXFocusedWindowAttribute)
│   └── snapWindow(window, to: position)
└── Error handling (no window, no screen, etc.)
```

#### Phase 3: Screen Detection & Calculation
```
WindowSnappingService.snapWindow(window, to: position)
├── ScreenDebugger.shared.logScreenInfo()
├── screenDetection.detectScreens(using: window)
│   ├── Get window's current frame
│   ├── Find screen containing window center
│   └── Return UsableScreens object
├── Convert SnapPosition to WindowDirection
├── Determine target screen (current or adjacent)
└── windowMover.moveWindow(window, to: direction, on: targetScreen)
```

#### Phase 4: Window Movement Execution
```
WindowMover.moveWindow(window, to: direction, on: screen)
├── ScreenshotDebugger.shared.clearAllDebugOverlays()
├── accessibilityElement.windowInfo(for: window) // Get current state
├── calculationService.calculateWindowRect()
│   ├── Create WindowCalculationParameters
│   ├── Route to appropriate calculation class:
│   │   ├── StandardPositionCalculation (halves, quarters)
│   │   ├── LeftRightHalfCalculation (with multi-display support)
│   │   ├── TopBottomHalfCalculation (with multi-display support)
│   │   └── [Other specialized calculations...]
│   └── Return calculated CGRect
├── Coordinate system conversion (screenFlipped)
├── DisplayScaleManager.adjustRectForScreen()
└── moveWindowWithRetry() // Actual positioning
```

### 3.4 Data Processing - Position Calculation

#### Calculation Examples:
```
StandardPositionCalculation.calculateRect() for .leftHalf:
├── visibleFrameOfScreen = screen.visibleFrame
├── rect = visibleFrameOfScreen
├── rect.size.width = floor(visibleFrameOfScreen.width / 2.0)
└── return RectResult(rect)

LeftRightHalfCalculation.calculate() with SubsequentExecutionMode:
├── Check DefaultsManager.shared.subsequentExecutionMode
├── If .resize: cycle through half → two-thirds → third
├── If .acrossMonitor: move to adjacent display
├── If .none: standard half positioning
└── Return WindowCalculationResult
```

### 3.5 Visual Feedback - Window Movement
**UI Updates:**
- Window smoothly animates to new position (macOS handles animation)
- Window resizes to calculated dimensions
- Movement typically completes in 100-200ms

#### Phase 5: Position Application
```
WindowMover.moveWindowWithRetry():
├── Convert to AX coordinate system (screenFlipped)
├── AXUIElementSetAttributeValue(window, kAXPositionAttribute, position)
├── AXUIElementSetAttributeValue(window, kAXSizeAttribute, size)
├── Verify positioning with retry mechanism (up to 3 attempts)
├── Handle edge cases (window constraints, app-specific behavior)
└── Log final position for debugging
```

### 3.6 Completion
**Final State:**
- Window positioned at calculated location
- Window sized according to snap position
- User can immediately interact with repositioned window
- No additional UI feedback (operation is instantaneous)

---

## 4. Drag-to-Snap Flow

### 4.1 Trigger
**User Action:** User begins dragging a window while holding modifier key (default: Option key)

### 4.2 UI Response
**Immediate Visual Feedback:**
- No immediate change until drag threshold is met
- Modifier key detection begins

### 4.3 Code Execution Path

#### Phase 1: Event Monitoring Setup
```
SnappingManager.init():
├── loadConfiguration() // Load snap modifiers from UserDefaults
├── box = FootprintWindow() // Create visual feedback window
├── setupEventMonitoring()
│   ├── EventMonitor(mask: [.leftMouseDown, .leftMouseUp, .leftMouseDragged])
│   ├── startModifierMonitor() // Monitor for required modifier keys
│   └── Register event handlers
└── Start monitoring if enabled
```

#### Phase 2: Drag Initiation Detection
```
SnappingManager.handle(event: .leftMouseDown):
├── Check if required modifiers are pressed
├── If modifiers match snapModifiers:
│   ├── Get frontmost window via Accessibility API
│   ├── Store windowElement and initialWindowRect
│   ├── Set isDragging = true
│   └── Logger.debug("Started drag with snap modifiers")
└── Else: ignore event
```

### 4.4 Data Processing - Drag Tracking

#### Phase 3: Mouse Drag Handling
```
SnappingManager.handle(event: .leftMouseDragged):
├── handleMouseDragged()
├── Task.detached:
│   ├── AccessibilityElement().getFrame(windowElement)
│   ├── Compare currentRect with initialRect
│   ├── If position changed: set windowMoving = true
│   └── MainActor.run: update UI state
└── updateSnapAreaAndFootprint()
```

#### Phase 4: Snap Area Detection
```
SnappingManager.updateSnapAreaAndFootprint():
├── snapAreaContainingCursor()
│   ├── mouseLocation = NSEvent.mouseLocation
│   ├── For each screen in NSScreen.screens:
│   │   ├── directionalLocationOfCursor(location, screen)
│   │   ├── Check screen orientation (landscape/portrait)
│   │   ├── Get SnapAreaModel configuration
│   │   └── Return SnapArea if action configured
│   └── Return matching SnapArea or nil
├── If snapArea != currentSnapArea:
│   ├── currentSnapArea = snapArea
│   └── updateFootprintWindow()
└── Show/hide visual feedback
```

### 4.5 Visual Feedback - Footprint Window
**UI Updates During Drag:**
- Translucent overlay window appears showing target snap area
- Footprint window follows cursor and shows snap zones
- Color-coded feedback (blue for valid snap areas)
- Real-time updates as cursor moves between snap zones

#### Phase 5: Footprint Window Management
```
SnappingManager.updateFootprintWindow():
├── If snapArea exists:
│   ├── Calculate footprint frame for snap area
│   ├── box.setFrame(footprintFrame)
│   ├── box.backgroundColor = snapAreaColor
│   ├── box.orderFront(nil) // Show footprint
│   └── Logger.debug("Showing footprint for [directional]")
├── Else:
│   ├── box.orderOut(nil) // Hide footprint
│   └── Logger.debug("Hiding footprint")
└── Update footprint appearance
```

### 4.6 Snap Execution

#### Phase 6: Mouse Release Handling
```
SnappingManager.handle(event: .leftMouseUp):
├── If windowMoving && currentSnapArea exists:
│   ├── isPerformingSnap = true
│   ├── Logger.info("Snapping window to [directional]")
│   ├── performSnap(element: windowElement, to: snapArea)
│   └── lastSnapTime = Date()
├── Else: resetState()
├── Hide footprint window
└── Clear drag state
```

#### Phase 7: Snap Operation Execution
```
SnappingManager.performSnap(element, to: snapArea):
├── Task.async:
│   ├── Convert snapArea.directional to WindowDirection
│   ├── Logger.debug("Starting snap operation to [directional]")
│   ├── WindowMover().moveWindow(element, to: direction, on: snapArea.screen)
│   │   └── [Same execution path as keyboard shortcut snapping]
│   ├── Logger.info("Successfully snapped window")
│   └── resetState()
└── Error handling and cleanup
```

### 4.7 Visual Feedback - Completion
**UI Updates:**
- Footprint window disappears
- Window smoothly moves to snap position
- Window resizes to calculated dimensions
- Drag operation completes

### 4.8 Completion
**Final State:**
- Window positioned in selected snap area
- Drag state reset and monitoring continues
- User can immediately begin another drag operation
- No persistent UI changes (footprint window hidden)

---

## Key Integration Points

### Service Dependencies
1. **LoggingService**: Comprehensive logging throughout all flows
2. **ToastManager**: User feedback for long-running operations
3. **DefaultsManager**: User preferences and configuration
4. **LicenseManager**: Freemium model enforcement
5. **WindowLayoutManager**: Display detection and coordinate normalization
6. **DisplayScaleManager**: Multi-display coordinate conversion

### State Management
- **@Published properties** in WorkspaceService for UI reactivity
- **UserDefaults** for persistent configuration
- **CloudKit** for cross-device workspace sync
- **In-memory caches** for performance optimization

### Error Handling
- **Graceful degradation** when APIs unavailable
- **Retry mechanisms** for positioning operations
- **User feedback** via toast notifications
- **Comprehensive logging** for debugging

---

## 5. App Startup and Initialization Flow

### 5.1 Trigger
**User Action:** User launches Snapback application (double-click, Spotlight, etc.)

### 5.2 UI Response
**Immediate Visual Feedback:**
- App icon appears in dock briefly
- Status bar icon appears (if permissions granted)
- No main window (accessory app)

### 5.3 Code Execution Path

#### Phase 1: Application Launch
```
AppDelegate.applicationDidFinishLaunching()
├── Logger.info("applicationDidFinishLaunching started")
├── NSApp.setActivationPolicy(.accessory) // Hide from dock
├── NSApp.activate(ignoringOtherApps: true)
├── initializeStatusBar()
│   ├── Create NSStatusItem with square length
│   ├── Set status bar icon (Snapback logo)
│   └── Configure initial menu structure
├── initializeToastSystem()
│   ├── Initialize ToastWindowController.shared
│   └── Set up permission notification observers
└── checkAndRequestPermissions()
```

#### Phase 2: Permission Checking
```
AppDelegate.checkAndRequestPermissions()
├── PermissionManager.shared.forceCheckPermissions()
├── If accessibility not granted:
│   ├── Show permission request dialog
│   ├── Open System Settings (if user agrees)
│   └── Start permission monitoring timer
└── If permissions granted:
    ├── Initialize core services
    ├── Load workspaces
    └── Register shortcuts
```

#### Phase 3: Service Initialization
```
AppDelegate (after permissions granted):
├── workspaceService = WorkspaceService(snappingService)
│   ├── Load workspaces from UserDefaults
│   ├── Validate workspace data
│   └── Set up CloudKit sync (if enabled)
├── shortcutService = ShortcutService()
│   ├── Register window management shortcuts
│   ├── Register workspace shortcuts
│   └── Set up shortcut change observers
├── windowManager = WindowManager()
│   ├── Create settings window (hidden)
│   ├── Create workspace manager window (hidden)
│   └── Set up window positioning service
└── buildStatusBarMenu() // Populate menu with workspaces
```

### 5.4 Data Processing - Workspace Loading
```
WorkspaceService.init():
├── loadWorkspacesInternal()
│   ├── UserDefaults.standard.data(forKey: "workspaces")
│   ├── JSONDecoder().decode([Workspace].self)
│   ├── Validate workspace integrity
│   └── Filter corrupted workspaces
├── Set up CloudKit container (if available)
└── @Published workspaces = loadedWorkspaces
```

### 5.5 Visual Feedback - Status Bar Setup
**UI Updates:**
- Status bar icon appears with Snapback logo
- Menu becomes available (but not yet populated)
- Toast notification if permissions granted: "Snapback is Ready"

### 5.6 Completion
**Final State:**
- App running as accessory (no dock icon)
- Status bar menu populated with workspaces
- All shortcuts registered and active
- Services initialized and ready
- Permission monitoring active

---

## 6. Settings Management Flow

### 6.1 Trigger
**User Actions:**
- Click "Settings..." from status bar menu
- Press settings keyboard shortcut (if configured)
- Right-click status bar icon → Settings

### 6.2 UI Response
**Immediate Visual Feedback:**
- Settings window appears with tabbed interface
- Window positioned intelligently on main display
- General tab selected by default

### 6.3 Code Execution Path

#### Phase 1: Settings Window Presentation
```
AppDelegate.openSettings() / WindowManager.openSettings()
├── Check if settings window already open
├── If not open:
│   ├── WindowPositioningService.shared.positionWindow()
│   ├── settingsWindow.makeKeyAndOrderFront(nil)
│   ├── NSApp.activate(ignoringOtherApps: true)
│   └── Fade-in animation
└── If already open: bring to front
```

#### Phase 2: Tab Management
```
SettingsView.body:
├── Custom toolbar with tab buttons
├── ForEach(tabs) { tab in ToolbarButton() }
├── selectedTab state management
└── Content switching:
    ├── "general": GeneralSettingsView()
    ├── "hotkeys": ShortcutSettingsView()
    ├── "workspaces": WorkspacesSettingsView()
    └── "license": LicenseSettingsView()
```

### 6.4 Data Processing - Settings Persistence

#### General Settings Changes:
```
GeneralSettingsView setting changes:
├── @AppStorage property wrappers for automatic persistence
├── Toggle changes trigger immediate UserDefaults updates
├── Drag-to-snap modifier changes:
│   ├── Update SnappingManager configuration
│   ├── Restart event monitoring with new modifiers
│   └── UserDefaults.standard.set(snapModifiers)
└── Import/Export operations via WorkspaceImportExportService
```

### 6.5 Visual Feedback - Real-time Updates
**UI Updates:**
- Settings changes apply immediately
- Visual feedback for toggle states
- Validation messages for invalid inputs
- Progress indicators for import/export operations

### 6.6 Completion
**Final State:**
- Settings persisted to UserDefaults
- Services updated with new configuration
- Window remains open for additional changes
- Changes reflected immediately in app behavior

---

## 7. Workspace Editing Flow

### 7.1 Trigger
**User Actions:**
- Double-click workspace in Workspace Manager
- Right-click workspace → "Edit Workspace"
- Click edit button in workspace settings row

### 7.2 UI Response
**Immediate Visual Feedback:**
- Edit workspace modal appears
- Form pre-populated with existing workspace data
- "Update Current Positions" button available

### 7.3 Code Execution Path

#### Phase 1: Edit Modal Presentation
```
WindowManager.showEditWorkspacePopup(workspace:)
├── Check for existing edit popup (prevent duplicates)
├── Create EditWorkspaceView with workspace data
├── Pre-populate form fields:
│   ├── workspaceName = workspace.name
│   ├── Load existing windowInfos as AppSelectionItems
│   ├── shortcutDisplayString from keyCode/modifiers
│   └── closeNonWorkspaceWindows setting
├── Create NSWindow with hosting view
├── WindowPositioningService.shared.positionWindow()
└── makeKeyAndOrderFront() + NSApp.activate()
```

#### Phase 2: Form Data Preparation
```
EditWorkspaceView.prepareEditData():
├── Convert workspace.windowInfos to AppSelectionItems
├── appItems = windowInfos.map { AppSelectionItem(windowInfo: $0) }
├── Sort by app name and display
├── selectedCount = appItems.filter { $0.isSelected }.count
└── Load app icons and colors asynchronously
```

### 7.4 Data Processing - Position Updates

#### "Update Current Positions" Feature:
```
EditWorkspaceView.updateCurrentPositions():
├── ToastManager.shared.showLoading("Updating Positions...")
├── WindowCaptureService.getCurrentAppsAndWindowPositionsNormalized()
├── Filter out Snapback's own windows
├── Match existing apps with current windows
├── Update AppSelectionItem.windowInfo with new positions
├── Preserve selection states
└── ToastManager.shared.completeLoading("Positions Updated")
```

### 7.5 Visual Feedback - Form Updates
**UI Updates:**
- Real-time app selection count updates
- Shortcut conflict detection and warnings
- Position update progress via toast notifications
- Form validation feedback

#### Phase 3: Save Changes
```
EditWorkspaceView.saveChanges():
├── ToastManager.shared.showLoading("Saving Workspace...")
├── Validate form data (name, selections)
├── Create updated workspace:
│   ├── Preserve workspace.id
│   ├── Update name, windowInfos, shortcuts
│   └── Maintain display arrangement data
├── workspaceService.updateWorkspace(updatedWorkspace)
├── Update shortcuts if changed
└── ToastManager.shared.completeLoading("Workspace Updated")
```

### 7.6 Completion
**Final State:**
- Workspace updated in WorkspaceService
- Changes persisted to UserDefaults/CloudKit
- Modal dismissed with success feedback
- Menu bar updated if name changed
- Shortcuts updated if modified

---

## 8. Workspace Deletion Flow

### 8.1 Trigger
**User Actions:**
- Click delete button in workspace settings row
- Right-click workspace → "Delete Workspace"
- Swipe to delete in Workspace Manager (macOS)

### 8.2 UI Response
**Immediate Visual Feedback:**
- Confirmation alert appears with workspace name
- Alert offers "Delete" (destructive) and "Cancel" options
- No immediate deletion (safety confirmation required)

### 8.3 Code Execution Path

#### Phase 1: Deletion Request
```
WorkspaceManagerView.deleteWorkspaceWithConfirmation():
├── selectedWorkspaceID = workspace.id
├── showingDeleteAlert = true
└── SwiftUI presents confirmation alert
```

#### Phase 2: Confirmation Alert
```
SwiftUI Alert:
├── Title: "Delete Workspace"
├── Message: "Are you sure you want to delete '[WorkspaceName]'? This action cannot be undone."
├── Buttons:
│   ├── "Delete" (destructive role) → confirmDelete()
│   └── "Cancel" (cancel role) → dismiss alert
└── Alert styling with destructive button emphasis
```

### 8.4 Data Processing - Deletion Execution

#### Phase 3: Actual Deletion
```
WorkspaceManagerView.confirmDelete(id:):
├── workspaceService.deleteWorkspace(id: idToDelete)
│   ├── workspaces.removeAll { $0.id == id }
│   ├── saveWorkspacesInternal() // Persist changes
│   ├── CloudKit sync (remove from cloud)
│   └── Logger.info("Deleted workspace with ID: [uuid]")
├── Unregister workspace shortcut:
│   ├── KeyboardShortcuts.disable(workspaceShortcut(for: id))
│   └── Remove from shortcut registry
├── selectedWorkspaceID = nil
└── NotificationCenter.post(.refreshStatusMenu)
```

### 8.5 Visual Feedback - UI Updates
**UI Updates:**
- Alert dismisses
- Workspace disappears from list with animation
- Menu bar updates (workspace removed from menu)
- No toast notification (deletion is immediate and obvious)

### 8.6 Completion
**Final State:**
- Workspace permanently deleted from storage
- Keyboard shortcut unregistered
- UI updated to reflect deletion
- CloudKit sync removes workspace from other devices

---

## 9. Import/Export Workflows

### 9.1 Export Flow

#### 9.1.1 Trigger
**User Action:** Click "Export Workspaces" button in General settings

#### 9.1.2 UI Response
**Immediate Visual Feedback:**
- Save dialog appears with suggested filename
- Default location: user's Documents folder
- File type: JSON (.json)

#### 9.1.3 Code Execution Path
```
GeneralSettingsView.exportWorkspaces():
├── isProcessing = true (disable UI)
├── NSSavePanel configuration:
│   ├── title = "Export Workspaces"
│   ├── allowedContentTypes = [.json]
│   ├── nameFieldStringValue = "Snapback Workspaces [date].json"
│   └── directoryURL = Documents folder
├── If user selects location:
│   └── WorkspaceImportExportService.shared.exportWorkspaces()
└── isProcessing = false
```

#### 9.1.4 Data Processing - Export Operation
```
WorkspaceImportExportService.exportWorkspaces():
├── Create WorkspaceExportData:
│   ├── version = "1.0"
│   ├── exportDate = Date()
│   └── workspaces = workspaceService.workspaces
├── JSONEncoder with pretty printing:
│   ├── dateEncodingStrategy = .iso8601
│   ├── outputFormatting = [.prettyPrinted, .sortedKeys]
│   └── encode(exportData)
├── Write data to selected URL
└── Background processing for large datasets
```

#### 9.1.5 Completion
**Final State:**
- JSON file created at selected location
- Success toast: "Export Successful - Workspaces exported to [filename]"
- File contains all workspace data with metadata

### 9.2 Import Flow

#### 9.2.1 Trigger
**User Action:** Click "Import Workspaces" button in General settings

#### 9.2.2 UI Response
**Immediate Visual Feedback:**
- Open dialog appears
- File type filter: JSON files only
- Browse to select workspace file

#### 9.2.3 Code Execution Path
```
GeneralSettingsView.importWorkspaces():
├── isProcessing = true
├── NSOpenPanel configuration:
│   ├── title = "Import Workspaces"
│   ├── allowedContentTypes = [.json]
│   ├── allowsMultipleSelection = false
│   └── canChooseFiles = true
├── If user selects file:
│   └── WorkspaceImportExportService.shared.importWorkspaces()
└── Handle import result
```

#### 9.2.4 Data Processing - Import with Conflict Detection
```
WorkspaceImportExportService.importWorkspaces():
├── Read and parse JSON file:
│   ├── Try WorkspaceExportData format (new)
│   ├── Fallback to [Workspace] format (legacy)
│   └── Validate data integrity
├── Detect conflicts with existing workspaces:
│   ├── Compare workspace names
│   ├── Compare workspace IDs
│   └── Create WorkspaceConflict objects
├── If conflicts found:
│   ├── Return ImportResult with conflicts
│   └── Present conflict resolution dialog
└── If no conflicts: proceed with import
```

### 9.2.5 Visual Feedback - Conflict Resolution
**UI Updates:**
- Conflict resolution alert appears
- Options: "Overwrite Existing", "Rename Imported", "Skip Conflicting"
- Clear explanation of conflicts found

#### Phase 4: Conflict Resolution
```
GeneralSettingsView.resolveConflicts(strategy:):
├── Switch on resolution strategy:
│   ├── .overwrite: Replace existing workspaces
│   ├── .rename: Append " (Imported)" to names
│   └── .skip: Import only non-conflicting workspaces
├── Process workspaces according to strategy
├── performImport(processedWorkspaces)
└── Show success toast with count
```

### 9.2.6 Completion
**Final State:**
- Workspaces imported according to resolution strategy
- Success toast: "Import Successful - Imported [N] workspace(s)"
- New workspaces available in menu and Workspace Manager
- Shortcuts registered for imported workspaces

---

## 10. Permission Handling Flow

### 10.1 Trigger
**System Event:** App launch or permission status change

### 10.2 UI Response
**Immediate Visual Feedback:**
- Permission request dialog (if needed)
- Status bar icon may be grayed out
- Limited functionality until permissions granted

### 10.3 Code Execution Path

#### Phase 1: Permission Check on Launch
```
AppDelegate.checkAndRequestPermissions():
├── PermissionManager.shared.forceCheckPermissions()
│   ├── AXIsProcessTrusted() // Check current status
│   ├── Compare with cached state
│   └── Update isAccessibilityPermissionGranted
├── If permissions not granted:
│   ├── Show permission request dialog
│   ├── Offer to open System Settings
│   └── Start monitoring timer
└── If permissions granted: initialize services
```

#### Phase 2: Permission Request Dialog
```
PermissionManager.requestAccessibilityPermission():
├── Create NSAlert with explanation
├── Alert message: "Snapback needs accessibility permissions..."
├── Buttons: "Open System Settings", "Cancel"
├── If user clicks "Open System Settings":
│   ├── NSWorkspace.shared.open(systemSettingsURL)
│   └── Start permission monitoring
└── If user cancels: continue with limited functionality
```

### 10.4 Data Processing - Permission Monitoring

#### Phase 3: Adaptive Permission Monitoring
```
PermissionManager.startAdaptiveTimer():
├── If permissions granted:
│   ├── Timer interval = 30 seconds (less frequent)
│   └── Monitor for permission revocation
├── If permissions not granted:
│   ├── Timer interval = 2 seconds (frequent checking)
│   └── Monitor for permission grant
└── Timer callback: checkPermissionStatus()
```

#### Phase 4: Permission Status Change
```
PermissionManager.checkPermissionStatus():
├── freshResult = AXIsProcessTrusted()
├── If status changed:
│   ├── Update isAccessibilityPermissionGranted
│   ├── NotificationCenter.post(permissionStatusChanged)
│   ├── Restart timer with appropriate interval
│   └── Log permission change
└── If permissions granted: trigger service initialization
```

### 10.5 Visual Feedback - Permission Grant
**UI Updates:**
- Welcome toast: "Snapback is Ready - Accessibility permissions granted"
- Status bar icon becomes fully active
- All features become available
- Menu populates with workspaces

### 10.6 Completion
**Final State:**
- Accessibility permissions granted and monitored
- All Snapback features fully functional
- Adaptive monitoring continues in background
- Services initialized and ready for use

---

## 11. Shortcut Recording and Conflict Resolution Flow

### 11.1 Trigger
**User Actions:**
- Click in shortcut recorder field in workspace save/edit forms
- Click shortcut recorder in settings
- Focus on shortcut input field

### 11.2 UI Response
**Immediate Visual Feedback:**
- Shortcut recorder field becomes active (highlighted border)
- Placeholder text: "Press shortcut keys..."
- Field ready to capture key combination

### 11.3 Code Execution Path

#### Phase 1: Shortcut Recording Activation
```
ShortcutRecorder.body (SwiftUI):
├── KeyboardShortcuts.Recorder(for: name) { shortcut in }
├── Field becomes first responder
├── System begins monitoring key events
└── Visual feedback: active state styling
```

#### Phase 2: Key Combination Capture
```
KeyboardShortcuts.Recorder key capture:
├── Monitor for modifier keys (⌘⌥⌃⇧)
├── Monitor for main key (letters, numbers, function keys)
├── Validate key combination:
│   ├── Require at least one modifier (usually)
│   ├── Reject modifier-only combinations
│   └── Accept valid key + modifier combinations
├── Display combination in real-time
└── Trigger onChange callback when complete
```

### 11.4 Data Processing - Conflict Detection

#### Phase 3: Real-time Validation
```
ShortcutRecorder onChange callback:
├── Convert to internal Shortcut format
├── ShortcutManager.shared.validateShortcut(shortcut)
│   ├── checkSystemConflicts(for: shortcut)
│   │   ├── MASShortcutValidator.isShortcutAlreadyTaken(bySystem:)
│   │   ├── Check against known system shortcuts
│   │   └── Return (hasConflict, explanation)
│   ├── checkApplicationConflicts(for: shortcut)
│   │   ├── ConflictingAppsChecker.getRunningConflictingApps()
│   │   ├── Check against other window managers
│   │   └── Return (hasConflict, appName)
│   └── Return ShortcutValidationResult
├── Update UI with validation result
└── Store shortcut if valid
```

### 11.5 Visual Feedback - Conflict Warnings
**UI Updates:**
- Valid shortcut: Green checkmark or normal styling
- System conflict: Red warning with explanation
- App conflict: Yellow warning with conflicting app name
- Real-time feedback as user types

#### Phase 4: Conflict Resolution Options
```
If conflicts detected:
├── Show conflict alert/warning
├── Options presented:
│   ├── "Use Anyway" (if "Allow any shortcut" enabled)
│   ├── "Choose Different Shortcut"
│   └── "Cancel"
├── If user chooses "Use Anyway":
│   ├── Store shortcut despite conflicts
│   └── Log warning about potential issues
└── If user chooses different: clear field and retry
```

### 11.6 Completion
**Final State:**
- Shortcut recorded and validated
- Conflicts resolved or acknowledged
- Shortcut stored in appropriate location (workspace, settings)
- Real-time registration if applicable

---

## 12. License Management and Upgrade Flow

### 12.1 Trigger
**User Actions:**
- Open License settings tab
- Attempt to create 4th workspace (freemium limit)
- App startup (automatic validation)

### 12.2 UI Response
**Immediate Visual Feedback:**
- License status displayed (Licensed/Unlicensed/Expired)
- License key input field (if unlicensed)
- Upgrade button/link (if freemium limits reached)

### 12.3 Code Execution Path

#### Phase 1: License Status Check
```
LicenseManager.init():
├── loadPersistedLicense() // Load from UserDefaults
├── If licenseKey exists:
│   └── Task { await validateLicenseWithCaching(silent: true) }
└── Set initial UI state based on cached status
```

#### Phase 2: License Validation
```
LicenseManager.validateLicense():
├── If FeatureFlags.isLicenseSystemDisabled: return early
├── Guard licenseKey not empty
├── isValidating = true (show loading state)
├── LicenseAPIService.validateLicense(key)
│   ├── Network request to validation server
│   ├── Check license status, expiration, device limits
│   └── Return LicenseValidationResponse
├── Process validation result:
│   ├── Update licenseStatus (.licensed/.expired/.invalid)
│   ├── Store licenseInfo (expiration, device count)
│   └── persistLicense() // Save to UserDefaults
└── isValidating = false
```

### 12.4 Data Processing - Freemium Enforcement

#### Phase 3: Workspace Limit Enforcement
```
WorkspaceService.addWorkspace():
├── Check LicenseManager.shared.licenseStatus
├── If unlicensed && workspaces.count >= 3:
│   ├── Show upgrade prompt toast
│   ├── "Upgrade to Pro for unlimited workspaces"
│   ├── Provide upgrade button/link
│   └── Prevent workspace creation
├── If licensed: allow unlimited workspaces
└── Add workspace normally
```

### 12.5 Visual Feedback - License States
**UI Updates:**
- **Unlicensed**: "Enter license key" field, upgrade prompts
- **Licensed**: Green checkmark, license info display
- **Expired**: Red warning, renewal prompt
- **Validating**: Loading spinner, "Validating license..."

#### Phase 4: Upgrade Flow
```
License upgrade process:
├── User clicks upgrade button/link
├── Open browser to purchase page (localhost:3001)
├── User completes purchase
├── User enters license key in app
├── LicenseManager.setLicenseKey(key)
│   ├── Format key for display
│   ├── validateLicenseWithCaching(forceServerCheck: true)
│   └── Update UI based on validation result
└── If valid: unlock all features immediately
```

### 12.6 Completion
**Final State:**
- License status validated and cached
- Features unlocked/locked based on license status
- Periodic revalidation scheduled
- User feedback provided for all license states

---

## 13. Toast Notification System

### 13.1 Trigger
**System Events:** Various operations throughout the app trigger toast notifications

### 13.2 UI Response
**Immediate Visual Feedback:**
- Toast window appears in top-right corner of main display
- Styled based on toast type (success/error/info/loading)
- Auto-dismiss timer starts (except for loading toasts)

### 13.3 Code Execution Path

#### Phase 1: Toast Request
```
ToastManager.shared.showSuccess/Error/Info/Loading():
├── Check DefaultsManager.shared.showToastNotifications
├── If disabled: return early (no toast shown)
├── show(type: toastType, title: title, message: message, duration: duration)
└── Create ToastData and update @Published currentToast
```

#### Phase 2: Toast Display
```
ToastManager.show():
├── Cancel any existing dismissTimer
├── currentToast = ToastData(type, title, message, duration)
├── @Published currentToast triggers UI update
├── ToastWindowController.shared displays toast
├── If duration > 0:
│   └── Schedule dismissTimer for auto-dismiss
└── If duration = 0: manual dismiss required (loading toasts)
```

### 13.4 Data Processing - Toast Types

#### Toast Type Behaviors:
```
ToastType behaviors:
├── .success: Green styling, auto-dismiss (3s default)
├── .error: Red styling, auto-dismiss (5s default)
├── .info: Blue styling, auto-dismiss (3s default)
└── .loading: Gray styling, no auto-dismiss, spinner animation
```

### 13.5 Visual Feedback - Toast Presentation
**UI Updates:**
- Toast slides in from top-right
- Icon based on type (checkmark, X, info, spinner)
- Title and message text
- Progress indicator for loading toasts
- Smooth fade-out on dismiss

#### Phase 3: Toast Completion
```
Loading toast completion:
├── ToastManager.shared.completeLoading()
├── Update existing toast to success/error type
├── Change styling and icon
├── Start auto-dismiss timer
└── Smooth transition from loading to completion state
```

### 13.6 Completion
**Final State:**
- Toast dismissed after duration expires
- User informed of operation status
- No persistent UI changes
- Ready for next toast notification

---

## Key Integration Patterns

### Cross-Flow Dependencies
1. **Permission Flow** → **All Other Flows**: Most functionality requires accessibility permissions
2. **License Flow** → **Workspace Creation**: Freemium limits enforced during workspace creation
3. **Settings Flow** → **All Flows**: Configuration changes affect behavior across all features
4. **Toast System** → **All Flows**: User feedback provided throughout all operations

### State Synchronization
- **@Published properties** ensure UI reactivity across all flows
- **NotificationCenter** coordinates between loosely coupled components
- **UserDefaults** provides persistent storage for all user preferences
- **CloudKit** synchronizes workspace data across devices

### Error Handling Patterns
- **Graceful degradation** when services unavailable
- **User feedback** via toast notifications for all operations
- **Retry mechanisms** for network operations and window positioning
- **Comprehensive logging** for debugging across all flows

This comprehensive documentation provides complete traceability from user action to system response across all major Snapback workflows, enabling developers to understand, debug, and extend the application's functionality effectively.
