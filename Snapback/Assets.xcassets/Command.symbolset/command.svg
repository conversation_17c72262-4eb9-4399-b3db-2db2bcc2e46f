<?xml version="1.0" encoding="UTF-8"?>
<!--Generator: Apple Native CoreSVG 341-->
<!DOCTYPE svg
PUBLIC "-//W3C//DTD SVG 1.1//EN"
       "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 3300 2200">
 <!--glyph: "100194", point size: 100.0, font version: "20.0d10e1", template writer version: "138.0.0"-->
 <style>.SFSymbolsPreviewWireframe {fill:none;opacity:1.0;stroke:black;stroke-width:0.5}
</style>
 <g id="Notes">
  <rect height="2200" id="artboard" style="fill:white;opacity:1" width="3300" x="0" y="0"/>
  <line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="292" y2="292"/>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 322)">Weight/Scale Variations</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 559.711 322)">Ultralight</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 856.422 322)">Thin</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1153.13 322)">Light</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1449.84 322)">Regular</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1746.56 322)">Medium</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2043.27 322)">Semibold</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2339.98 322)">Bold</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2636.69 322)">Heavy</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2933.4 322)">Black</text>
  <line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1903" y2="1903"/>
  <g transform="matrix(0.2 0 0 0.2 263 1933)">
   <path d="m46.2402 4.15039c21.7773 0 39.4531-17.627 39.4531-39.4043s-17.6758-39.4043-39.4531-39.4043c-21.7285 0-39.4043 17.627-39.4043 39.4043s17.6758 39.4043 39.4043 39.4043Zm0-7.42188c-17.6758 0-31.9336-14.3066-31.9336-31.9824s14.2578-31.9824 31.9336-31.9824 31.9824 14.3066 31.9824 31.9824-14.3066 31.9824-31.9824 31.9824Zm-17.9688-31.9824c0 2.14844 1.51367 3.61328 3.75977 3.61328h10.498v10.5957c0 2.19727 1.46484 3.71094 3.61328 3.71094 2.24609 0 3.71094-1.51367 3.71094-3.71094v-10.5957h10.5957c2.19727 0 3.71094-1.46484 3.71094-3.61328 0-2.19727-1.51367-3.71094-3.71094-3.71094h-10.5957v-10.5469c0-2.24609-1.46484-3.75977-3.71094-3.75977-2.14844 0-3.61328 1.51367-3.61328 3.75977v10.5469h-10.498c-2.24609 0-3.75977 1.51367-3.75977 3.71094Z"/>
  </g>
  <g transform="matrix(0.2 0 0 0.2 281.506 1933)">
   <path d="m58.5449 14.5508c27.4902 0 49.8047-22.3145 49.8047-49.8047s-22.3145-49.8047-49.8047-49.8047-49.8047 22.3145-49.8047 49.8047 22.3145 49.8047 49.8047 49.8047Zm0-8.30078c-22.9492 0-41.5039-18.5547-41.5039-41.5039s18.5547-41.5039 41.5039-41.5039 41.5039 18.5547 41.5039 41.5039-18.5547 41.5039-41.5039 41.5039Zm-22.6562-41.5039c0 2.39258 1.66016 4.00391 4.15039 4.00391h14.3555v14.4043c0 2.44141 1.66016 4.15039 4.05273 4.15039 2.44141 0 4.15039-1.66016 4.15039-4.15039v-14.4043h14.4043c2.44141 0 4.15039-1.61133 4.15039-4.00391 0-2.44141-1.70898-4.15039-4.15039-4.15039h-14.4043v-14.3555c0-2.49023-1.70898-4.19922-4.15039-4.19922-2.39258 0-4.05273 1.70898-4.05273 4.19922v14.3555h-14.3555c-2.49023 0-4.15039 1.70898-4.15039 4.15039Z"/>
  </g>
  <g transform="matrix(0.2 0 0 0.2 304.924 1933)">
   <path d="m74.8535 28.3203c35.1074 0 63.623-28.4668 63.623-63.5742s-28.5156-63.623-63.623-63.623-63.5742 28.5156-63.5742 63.623 28.4668 63.5742 63.5742 63.5742Zm0-9.08203c-30.127 0-54.4922-24.3652-54.4922-54.4922s24.3652-54.4922 54.4922-54.4922 54.4922 24.3652 54.4922 54.4922-24.3652 54.4922-54.4922 54.4922Zm-28.8574-54.4922c0 2.58789 1.85547 4.39453 4.58984 4.39453h19.7266v19.7754c0 2.68555 1.85547 4.58984 4.44336 4.58984 2.68555 0 4.54102-1.85547 4.54102-4.58984v-19.7754h19.7754c2.68555 0 4.58984-1.80664 4.58984-4.39453 0-2.73438-1.85547-4.58984-4.58984-4.58984h-19.7754v-19.7266c0-2.73438-1.85547-4.63867-4.54102-4.63867-2.58789 0-4.44336 1.9043-4.44336 4.63867v19.7266h-19.7266c-2.73438 0-4.58984 1.85547-4.58984 4.58984Z"/>
  </g>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 1953)">Design Variations</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1971)">Symbols are supported in up to nine weights and three scales.</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1989)">For optimal layout with text and other symbols, vertically align</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 2007)">symbols with the adjacent text.</text>
  <line style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="776" x2="776" y1="1919" y2="1933"/>
  <g transform="matrix(0.2 0 0 0.2 776 1933)">
   <path d="m16.5527 0.78125c2.58789 0 3.85742-0.976562 4.78516-3.71094l6.29883-17.2363h28.8086l6.29883 17.2363c0.927734 2.73438 2.19727 3.71094 4.73633 3.71094 2.58789 0 4.24805-1.5625 4.24805-4.00391 0-0.830078-0.146484-1.61133-0.537109-2.63672l-22.9004-60.9863c-1.12305-2.97852-3.125-4.49219-6.25-4.49219-3.02734 0-5.07812 1.46484-6.15234 4.44336l-22.9004 61.084c-0.390625 1.02539-0.537109 1.80664-0.537109 2.63672 0 2.44141 1.5625 3.95508 4.10156 3.95508Zm13.4766-28.3691 11.8652-32.8613h0.244141l11.8652 32.8613Z"/>
  </g>
  <line style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="792.836" x2="792.836" y1="1919" y2="1933"/>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 776 1953)">Margins</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1971)">Leading and trailing margins on the left and right side of each symbol</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1989)">can be adjusted by modifying the x-location of the margin guidelines.</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2007)">Modifications are automatically applied proportionally to all</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2025)">scales and weights.</text>
  <g transform="matrix(0.2 0 0 0.2 1289 1933)">
   <path d="m14.209 9.32617 8.49609 8.54492c4.29688 4.3457 9.22852 4.05273 13.8672-1.07422l53.4668-58.9355-4.83398-4.88281-53.0762 58.3984c-1.75781 2.00195-3.41797 2.49023-5.76172 0.146484l-5.85938-5.81055c-2.34375-2.29492-1.80664-4.00391 0.195312-5.81055l57.373-54.0039-4.88281-4.83398-57.959 54.4434c-4.93164 4.58984-5.32227 9.47266-1.02539 13.8184Zm32.0801-90.9668c-2.09961 2.05078-2.24609 4.93164-1.07422 6.88477 1.17188 1.80664 3.4668 2.97852 6.68945 2.14844 7.32422-1.70898 14.9414-2.00195 22.0703 2.68555l-2.92969 7.27539c-1.70898 4.15039-0.830078 7.08008 1.85547 9.81445l11.4746 11.5723c2.44141 2.44141 4.49219 2.53906 7.32422 2.05078l5.32227-0.976562 3.32031 3.36914-0.195312 2.7832c-0.195312 2.49023 0.439453 4.39453 2.88086 6.78711l3.80859 3.71094c2.39258 2.39258 5.46875 2.53906 7.8125 0.195312l14.5508-14.5996c2.34375-2.34375 2.24609-5.32227-0.146484-7.71484l-3.85742-3.80859c-2.39258-2.39258-4.24805-3.17383-6.64062-2.97852l-2.88086 0.244141-3.22266-3.17383 1.2207-5.61523c0.634766-2.83203-0.146484-5.0293-3.07617-7.95898l-10.9863-10.9375c-16.6992-16.6016-38.8672-16.2109-53.3203-1.75781Zm7.4707 1.85547c12.1582-8.88672 28.6133-7.37305 39.7461 3.75977l12.1582 12.0605c1.17188 1.17188 1.36719 2.09961 1.02539 3.80859l-1.61133 7.42188 7.51953 7.42188 4.93164-0.292969c1.26953-0.0488281 1.66016 0.0488281 2.63672 1.02539l2.88086 2.88086-12.207 12.207-2.88086-2.88086c-0.976562-0.976562-1.12305-1.36719-1.07422-2.68555l0.341797-4.88281-7.4707-7.42188-7.61719 1.26953c-1.61133 0.341797-2.34375 0.195312-3.56445-0.976562l-10.0098-10.0098c-1.26953-1.17188-1.41602-2.00195-0.634766-3.85742l4.39453-10.4492c-7.8125-7.27539-17.9688-10.4004-28.125-7.42188-0.78125 0.195312-1.07422-0.439453-0.439453-0.976562Z"/>
  </g>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 1289 1953)">Exporting</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1971)">Symbols should be outlined when exporting to ensure the</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1989)">design is preserved when submitting to Xcode.</text>
  <text id="template-version" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1933)">Template v.6.0</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1951)">Requires Xcode 16 or greater</text>
  <text id="descriptive-name" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1969)">Generated from command</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1987)">Typeset at 100.0 points</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 726)">Small</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1156)">Medium</text>
  <text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1586)">Large</text>
 </g>
 <g id="Guides">
  <g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 696)">
   <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
  </g>
  <line id="Baseline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="696" y2="696"/>
  <line id="Capline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="625.541" y2="625.541"/>
  <g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1126)">
   <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
  </g>
  <line id="Baseline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1126" y2="1126"/>
  <line id="Capline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1055.54" y2="1055.54"/>
  <g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1556)">
   <path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
  </g>
  <line id="Baseline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1556" y2="1556"/>
  <line id="Capline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1485.54" y2="1485.54"/>
  <line id="right-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2981.15" x2="2981.15" y1="600.785" y2="720.121"/>
  <line id="left-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2885.65" x2="2885.65" y1="600.785" y2="720.121"/>
  <line id="right-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1495.79" x2="1495.79" y1="600.785" y2="720.121"/>
  <line id="left-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1403.9" x2="1403.9" y1="600.785" y2="720.121"/>
  <line id="right-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="605.386" x2="605.386" y1="600.785" y2="720.121"/>
  <line id="left-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="514.036" x2="514.036" y1="600.785" y2="720.121"/>
 </g>
 <g id="Symbols">
  <g id="Black-S" transform="matrix(1 0 0 1 2885.65 696)">
   <path class="SFSymbolsPreviewWireframe" d="M31.0059-39.6484L31.0059-30.6641L26.4648-30.6152C17.2363-30.5176 9.76562-23.1934 9.76562-13.9648C9.76562-4.83398 17.1875 2.73438 26.5137 2.73438C35.6445 2.73438 43.1641-4.88281 43.1641-13.9648L43.1641-18.3105L52.3438-18.3105L52.3438-13.9648C52.3438-4.88281 59.8633 2.73438 68.9941 2.73438C78.3203 2.73438 85.7422-4.83398 85.7422-13.9648C85.7422-23.1934 78.2715-30.5176 69.043-30.6152L64.502-30.6641L64.502-39.6484L69.043-39.6973C78.2715-39.7949 85.7422-47.1191 85.7422-56.3477C85.7422-65.4785 78.3203-73.0469 68.9941-73.0469C59.8633-73.0469 52.3438-65.4297 52.3438-56.3477L52.3438-52.002L43.1641-52.002L43.1641-56.3477C43.1641-65.4297 35.6445-73.0469 26.5137-73.0469C17.1875-73.0469 9.76562-65.4785 9.76562-56.3477C9.76562-47.1191 17.2363-39.7949 26.4648-39.6973ZM27.0508-51.9043C24.1699-51.9043 22.168-53.8574 22.168-56.2988C22.168-58.7402 24.0723-60.6445 26.5137-60.6445C28.8574-60.6445 30.9082-58.6426 30.9082-55.7617L30.9082-51.9043ZM68.457-51.9043L64.5996-51.9043L64.5996-55.7617C64.5996-58.6426 66.6504-60.6445 68.9941-60.6445C71.4355-60.6445 73.3398-58.7402 73.3398-56.2988C73.3398-53.8574 71.3379-51.9043 68.457-51.9043ZM43.1641-30.6641L43.1641-39.6484L52.3438-39.6484L52.3438-30.6641ZM27.0508-18.4082L30.9082-18.4082L30.9082-14.5508C30.9082-11.6699 28.8574-9.66797 26.5137-9.66797C24.0723-9.66797 22.168-11.5723 22.168-14.0137C22.168-16.4551 24.1699-18.4082 27.0508-18.4082ZM68.457-18.4082C71.3379-18.4082 73.3398-16.4551 73.3398-14.0137C73.3398-11.5723 71.4355-9.66797 68.9941-9.66797C66.6504-9.66797 64.5996-11.6699 64.5996-14.5508L64.5996-18.4082Z"/>
  </g>
  <g id="Regular-S" transform="matrix(1 0 0 1 1403.9 696)">
   <path class="SFSymbolsPreviewWireframe" d="M30.9082-43.6035L30.9082-26.8555L23.7305-26.8555C16.0156-26.8555 9.76562-20.8008 9.76562-13.0371C9.76562-5.27344 16.0156 0.976562 23.7305 0.976562C31.4453 0.976562 37.6953-5.27344 37.6953-13.0371L37.6953-20.166L54.1992-20.166L54.1992-13.0371C54.1992-5.27344 60.4492 0.976562 68.1641 0.976562C75.8789 0.976562 82.1289-5.27344 82.1289-13.0371C82.1289-20.8008 75.8789-26.8555 68.1641-26.8555L61.0352-26.8555L61.0352-43.6035L68.1641-43.6035C75.8789-43.6035 82.1289-49.6582 82.1289-57.4219C82.1289-65.1855 75.8789-71.4355 68.1641-71.4355C60.4492-71.4355 54.1992-65.1855 54.1992-57.4219L54.1992-50.293L37.6953-50.293L37.6953-57.4219C37.6953-65.1855 31.4453-71.4355 23.7305-71.4355C16.0156-71.4355 9.76562-65.1855 9.76562-57.4219C9.76562-49.6582 16.0156-43.6035 23.7305-43.6035ZM23.7305-50.1953C19.8242-50.1953 16.5527-53.4668 16.5527-57.4219C16.5527-61.377 19.8242-64.5996 23.7305-64.5996C27.6367-64.5996 30.9082-61.377 30.9082-57.4219L30.9082-50.1953ZM68.1641-50.1953L61.0352-50.1953L61.0352-57.4219C61.0352-61.377 64.2578-64.5996 68.1641-64.5996C72.0703-64.5996 75.3418-61.377 75.3418-57.4219C75.3418-53.4668 72.0703-50.1953 68.1641-50.1953ZM37.6953-26.8555L37.6953-43.6035L54.1992-43.6035L54.1992-26.8555ZM23.7305-20.3125L30.9082-20.3125L30.9082-13.0859C30.9082-9.13086 27.6367-5.9082 23.7305-5.9082C19.8242-5.9082 16.5527-9.13086 16.5527-13.0859C16.5527-17.041 19.8242-20.3125 23.7305-20.3125ZM68.1641-20.3125C72.0703-20.3125 75.3418-17.041 75.3418-13.0859C75.3418-9.13086 72.0703-5.9082 68.1641-5.9082C64.2578-5.9082 61.0352-9.13086 61.0352-13.0859L61.0352-20.3125Z"/>
  </g>
  <g id="Ultralight-S" transform="matrix(1 0 0 1 514.036 696)">
   <path class="SFSymbolsPreviewWireframe" d="M32.1797-46.5097L32.1797-23.9947L22.0503-23.9947C15.3345-23.9947 9.76562-18.4395 9.76562-11.6294C9.76562-4.86475 15.3345 0.704104 22.0503 0.704104C28.8115 0.704104 34.335-4.86475 34.335-11.6294L34.335-21.8008L56.9692-21.8008L56.9692-11.6294C56.9692-4.86475 62.5381 0.704104 69.2993 0.704104C76.0151 0.704104 81.584-4.86475 81.584-11.6294C81.584-18.4395 76.0151-23.9947 69.2993-23.9947L59.1734-23.9947L59.1734-46.5097L69.2993-46.5097C76.0151-46.5097 81.584-52.0649 81.584-58.875C81.584-65.6396 76.0151-71.2085 69.2993-71.2085C62.5381-71.2085 56.9692-65.6396 56.9692-58.875L56.9692-48.7036L34.335-48.7036L34.335-58.875C34.335-65.6396 28.8115-71.2085 22.0503-71.2085C15.3345-71.2085 9.76562-65.6396 9.76562-58.875C9.76562-52.0649 15.3345-46.5097 22.0503-46.5097ZM22.0503-48.6968C16.5093-48.6968 11.9663-53.2852 11.9663-58.875C11.9663-64.4194 16.5093-69.0044 22.0503-69.0044C27.5913-69.0044 32.1797-64.4194 32.1797-58.875L32.1797-48.6968ZM69.2993-48.6968L59.1734-48.6968L59.1734-58.875C59.1734-64.4194 63.7583-69.0044 69.2993-69.0044C74.7949-69.0044 79.3833-64.4194 79.3833-58.875C79.3833-53.2852 74.7949-48.6968 69.2993-48.6968ZM34.335-23.9947L34.335-46.5097L56.9692-46.5097L56.9692-23.9947ZM22.0503-21.811L32.1797-21.811L32.1797-11.6328C32.1797-6.0884 27.5913-1.50345 22.0503-1.50345C16.5093-1.50345 11.9663-6.0884 11.9663-11.6328C11.9663-17.2227 16.5093-21.811 22.0503-21.811ZM69.2993-21.811C74.7949-21.811 79.3833-17.2227 79.3833-11.6328C79.3833-6.0884 74.7949-1.50345 69.2993-1.50345C63.7583-1.50345 59.1734-6.0884 59.1734-11.6328L59.1734-21.811Z"/>
  </g>
 </g>
</svg>
