import Carbon.HIToolbox  // For key code constants
import Combine
import Foundation
import KeyboardShortcuts
import SwiftUI

struct EditWorkspaceView: View {
    @EnvironmentObject var workspaceService: WorkspaceService
    @EnvironmentObject var windowDismissalManager: WindowDismissalManager
    @Environment(\.dismiss) var dismiss

    // Form State
    @State private var workspaceName: String
    @State private var customLayoutName: String = ""
    @FocusState private var isNameFieldFocused: Bool
    @State private var focusTimer = Timer.publish(every: 0.3, on: .main, in: .common).autoconnect()

    // App selection state
    @State private var appItems: [AppSelectionItem] = []
    @State private var selectedCount: Int = 0

    // Shortcut Recording State (not directly used but needed for the form)
    @State private var shortcutDisplayString: String = "None"
    @State private var recordedKeyCode: UInt16? = nil
    @State private var recordedModifiers: UInt? = nil

    // Workspace Settings
    @State private var closeNonWorkspaceWindows = false

    // The original workspace being edited
    let workspace: Workspace

    // Alert state
    @State private var showingConflictAlert = false
    @State private var conflictAlertMessage = ""

    // Create a separate view for the row
    struct EditWorkspaceAppRow: View {
        @Binding var appItem: AppSelectionItem
        @Binding var selectedCount: Int
        var appItems: [AppSelectionItem]

        var body: some View {
            HStack(spacing: 12) {
                Image(nsImage: appItem.appIcon)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 20, height: 20)

                appItem.displayNameView
                    .lineLimit(1)

                Spacer()

                Toggle(
                    "",
                    isOn: Binding(
                        get: { appItem.isSelected },
                        set: { newValue in
                            appItem.isSelected = newValue
                            selectedCount = appItems.filter { $0.isSelected }.count
                            print(
                                "Toggle changed for \(appItem.appName): \(newValue), Selected count: \(selectedCount)"
                            )
                        }
                    )
                )
                .labelsHidden()
                .toggleStyle(.switch)
            }
            .snapbackRowStyle()
            .contentShape(Rectangle())
            .onTapGesture {
                appItem.isSelected.toggle()
                selectedCount = appItems.filter { $0.isSelected }.count
                print(
                    "Row tapped for \(appItem.appName): \(appItem.isSelected), Selected count: \(selectedCount)"
                )
            }
        }
    }

    // Update the app list content
    private var appListContent: some View {
        VStack(spacing: 0) {
            ForEach(appItems.indices, id: \.self) { index in
                EditWorkspaceAppRow(
                    appItem: $appItems[index],
                    selectedCount: $selectedCount,
                    appItems: appItems
                )
                // .background(
                //     index % 2 == 0 ? Color.clear : Color.gray.opacity(0.05)
                // )
            }
        }
    }

    init(workspace: Workspace) {
        self.workspace = workspace
        // Initialize state from the passed workspace
        _workspaceName = State(initialValue: workspace.name)
    }

    var body: some View {
        WorkspaceFormView(
            workspace: workspace,
            workspaceName: $workspaceName,
            customLayoutName: $customLayoutName,
            appItems: $appItems,
            selectedCount: $selectedCount,
            shortcutDisplayString: $shortcutDisplayString,
            recordedKeyCode: $recordedKeyCode,
            recordedModifiers: $recordedModifiers,
            closeNonWorkspaceWindows: $closeNonWorkspaceWindows,
            onSave: saveChanges,
            onCancel: {
                // Use improved dismissal system with completion handler
                windowDismissalManager.dismissCurrentModal(windowTitle: "Edit Workspace") {
                    print("[EditWorkspaceView] Edit Workspace modal cancelled and dismissed")
                }
            },
            onUpdatePositions: updateCurrentPositions
        )
        .compatibleModalDismissal(windowTitle: "Edit Workspace")
        .frame(width: 600, height: 800)  // Match SaveWorkspaceView dimensions
        .onAppear {
            // Load preview on appear
            if appItems.isEmpty {
                prepareAppItems()
            }

            // Initialize workspace settings from existing workspace
            closeNonWorkspaceWindows = workspace.closeNonWorkspaceWindows ?? false
        }
        .onReceive(focusTimer) { _ in
            // Set focus on the name field when timer fires
            isNameFieldFocused = true
            // Cancel the timer after first fire
            focusTimer.upstream.connect().cancel()
        }
    }

    // Conflict checking
    struct ShortcutConflict {
        let type: String  // "workspace" or "action"
        let name: String  // Name of the conflicting item
    }

    // Check for conflicts with existing shortcuts
    private func checkForConflicts(shortcut: KeyboardShortcuts.Shortcut) {
        guard let key = shortcut.key else { return }

        // Convert for compatibility with existing code
        let modifiers = shortcut.modifiers.rawValue

        // Check for conflicts with other workspaces
        for otherWorkspace in workspaceService.workspaces {
            if let otherID = otherWorkspace.id, otherID != workspace.id {
                let otherShortcutName = KeyboardShortcuts.Name.workspaceShortcut(for: otherID)
                if let otherShortcut = KeyboardShortcuts.getShortcut(for: otherShortcutName),
                    otherShortcut.key?.rawValue == key.rawValue
                        && otherShortcut.modifiers.rawValue == modifiers
                {
                    // Found a conflict with another workspace - reset shortcut
                    if let id = workspace.id {
                        let shortcutName = KeyboardShortcuts.Name.workspaceShortcut(for: id)
                        KeyboardShortcuts.reset(shortcutName)
                    }
                    return
                }
            }
        }

        // Check for conflicts with predefined shortcuts
        for name in KeyboardShortcuts.Name.allPredefinedShortcuts {
            if let otherShortcut = KeyboardShortcuts.getShortcut(for: name),
                otherShortcut.key?.rawValue == key.rawValue
                    && otherShortcut.modifiers.rawValue == modifiers
            {
                // Found a conflict with a predefined shortcut - reset shortcut
                if let id = workspace.id {
                    let shortcutName = KeyboardShortcuts.Name.workspaceShortcut(for: id)
                    KeyboardShortcuts.reset(shortcutName)
                }
                return
            }
        }

    }

    // MARK: - App Selection Preparation
    private func prepareAppItems() {
        // Get Snapback's bundle ID
        let ownBundleID = Bundle.main.bundleIdentifier

        // Convert window infos to app selection items, excluding Snapback windows
        appItems = workspace.windowInfos
            .filter { windowInfo in
                // Filter out Snapback's own windows
                windowInfo.appBundleIdentifier != ownBundleID
            }
            .map { windowInfo in
                AppSelectionItem(windowInfo: windowInfo, isSelected: true)
            }

        // Sort by app name and then by display
        appItems.sort { item1, item2 in
            if item1.appName == item2.appName {
                // If same app, sort by display
                let display1 = item1.windowInfo.monitorID?.uuidString ?? ""
                let display2 = item2.windowInfo.monitorID?.uuidString ?? ""
                return display1 < display2
            }
            return item1.appName < item2.appName
        }

        // Initialize the selected count
        selectedCount = appItems.count
    }

    // Helper method to determine if screens are arranged vertically
    private func isVerticalArrangement(screens: [NSScreen]) -> Bool {
        guard screens.count > 1 else { return false }

        // Sort screens by Y position (bottom to top)
        let sortedScreens = screens.sorted(by: { $0.frame.minY < $1.frame.minY })

        // Check if screens are stacked vertically
        for i in 0..<(sortedScreens.count - 1) {
            let currentScreen = sortedScreens[i]
            let nextScreen = sortedScreens[i + 1]

            // Calculate horizontal overlap
            let horizontalOverlap =
                min(currentScreen.frame.maxX, nextScreen.frame.maxX)
                - max(currentScreen.frame.minX, nextScreen.frame.minX)

            // If there's significant horizontal overlap, screens are arranged vertically
            if horizontalOverlap > 0 {
                return true
            }
        }

        return false
    }

    // MARK: - Save Changes
    func saveChanges() {
        // Create a logger instance
        // Removed unused logger variables

        // Show loading toast
        ToastManager.shared.showLoading(
            title: "Saving Workspace",
            message: "Updating workspace information..."
        )

        // Get only the selected window infos
        let selectedWindowInfos =
            appItems
            .filter { $0.isSelected }
            .map { $0.windowInfo }

        // Create updated workspace with the new name and selected windows
        let trimmedWorkspaceName = workspaceName.trimmingCharacters(in: .whitespacesAndNewlines)
        var updatedWorkspace = workspace
        updatedWorkspace.name = trimmedWorkspaceName
        updatedWorkspace.windowInfos = selectedWindowInfos

        // Get the shortcut from KeyboardShortcuts
        if let id = workspace.id {
            print("🔍 EDIT DEBUG: Processing workspace '\(workspace.name)' with ID \(id.uuidString)")

            let shortcutName = KeyboardShortcuts.Name.workspaceShortcut(for: id)
            print("🔍 EDIT DEBUG: Looking for shortcut with name: \(shortcutName)")

            if let shortcut = KeyboardShortcuts.getShortcut(for: shortcutName) {
                print("🔍 EDIT DEBUG: Found shortcut in KeyboardShortcuts: \(shortcut)")

                if let key = shortcut.key {
                    // Update the workspace model with the shortcut data
                    let keyCode = UInt16(key.rawValue)
                    let modifiers = shortcut.modifiers.rawValue

                    updatedWorkspace.shortcutKeyCode = keyCode
                    updatedWorkspace.shortcutModifiers = modifiers

                    print(
                        "✅ EDIT DEBUG: Saved shortcut - Key: \(key.rawValue), Modifiers: \(shortcut.modifiers.rawValue)"
                    )
                    print("✅ EDIT DEBUG: Converted to keyCode: \(keyCode), modifiers: \(modifiers)")

                    // Check if we can convert this to a character for display
                    // Use the global formatShortcut function which calls keycodeToString internally
                    if let displayString = formatShortcut(keyCode: keyCode, modifiers: modifiers) {
                        print(
                            "✅ EDIT DEBUG: This corresponds to display string: '\(displayString)'")
                    } else {
                        print(
                            "⚠️ EDIT DEBUG: Could not convert keyCode \(keyCode) to display string")
                    }
                } else {
                    print("⚠️ EDIT DEBUG: Shortcut has no key: \(shortcut)")

                    // Clear the shortcut if key is missing
                    updatedWorkspace.shortcutKeyCode = nil
                    updatedWorkspace.shortcutModifiers = nil
                    print("⚠️ EDIT DEBUG: Cleared shortcut due to missing key")
                }
            } else {
                // Clear the shortcut if none is set
                updatedWorkspace.shortcutKeyCode = nil
                updatedWorkspace.shortcutModifiers = nil

                print("ℹ️ EDIT DEBUG: No shortcut found in KeyboardShortcuts, cleared shortcut data")
            }
        }

        // Update workspace settings
        updatedWorkspace.closeNonWorkspaceWindows = closeNonWorkspaceWindows

        // Update the workspace in the service
        print("🔍 EDIT DEBUG: Updating workspace in service")
        workspaceService.updateWorkspace(updatedWorkspace)

        // Trigger shortcut service to register the updated workspace shortcut immediately
        if let appDelegate = NSApp.delegate as? AppDelegate {
            appDelegate.shortcutService.registerWorkspaceShortcutImmediately(for: updatedWorkspace)
            print("🔍 EDIT DEBUG: Triggered immediate shortcut registration for updated workspace")
        }

        // Force menu refresh to show updated shortcut
        print("🔍 EDIT DEBUG: Posting notification to refresh menu")
        NotificationCenter.default.post(name: Notification.Name("RefreshStatusMenu"), object: nil)
        print("✅ EDIT DEBUG: Notification posted")

        // Show success toast
        ToastManager.shared.completeLoading(
            title: "Workspace Updated",
            message:
                "\(trimmedWorkspaceName) has been updated with \(selectedWindowInfos.count) apps.",
            duration: 3.0
        )

        // Close the window using improved dismissal system
        print("[EditWorkspaceView] Attempting to close window after save")
        windowDismissalManager.dismissCurrentModal(windowTitle: "Edit Workspace") {
            print("[EditWorkspaceView] Edit Workspace modal dismissed successfully")
        }
    }

    // Function to capture current window positions
    private func updateCurrentPositions() {
        // Create a logger instance
        // Removed unused logger variables

        // Show loading toast
        ToastManager.shared.showLoading(
            title: "Updating Positions",
            message: "Capturing current window positions..."
        )

        // Get current window positions from WindowCaptureService with normalized coordinates
        let currentWindowInfos = WindowCaptureService.getCurrentAppsAndWindowPositionsNormalized()

        // Get Snapback's bundle ID
        let ownBundleID = Bundle.main.bundleIdentifier

        // Filter out Snapback's own windows
        let filteredWindowInfos = currentWindowInfos.filter {
            $0.appBundleIdentifier != ownBundleID
        }

        // Create a fresh set of app items from the current window positions
        let newAppItems = filteredWindowInfos.map { windowInfo in
            // Check if this window's app was previously selected
            let wasSelected =
                appItems.first { item in
                    item.windowInfo.appBundleIdentifier == windowInfo.appBundleIdentifier
                }?.isSelected ?? true  // Default to selected if not found

            return AppSelectionItem(windowInfo: windowInfo, isSelected: wasSelected)
        }

        // Sort by app name and then by display (same as in prepareAppItems)
        let sortedAppItems = newAppItems.sorted { item1, item2 in
            if item1.appName == item2.appName {
                // If same app, sort by display
                let display1 = item1.windowInfo.monitorID?.uuidString ?? ""
                let display2 = item2.windowInfo.monitorID?.uuidString ?? ""
                return display1 < display2
            }
            return item1.appName < item2.appName
        }

        // Update the appItems array with the fresh data
        appItems = sortedAppItems

        // Update the selected count
        selectedCount = appItems.filter { $0.isSelected }.count

        // Log the update for debugging
        print(
            "[EditWorkspaceView] Updated positions with \(appItems.count) windows, \(selectedCount) selected"
        )

        // Show success toast
        ToastManager.shared.completeLoading(
            title: "Positions Updated",
            message: "Window positions have been updated.",
            duration: 2.0
        )
    }
}

#Preview {
    struct PreviewWrapper: View {
        @StateObject var workspaceService = WorkspaceService(
            snappingService: WindowSnappingService())
        @State private var workspaceToEdit: Workspace?
        @State var showSheet = true

        var body: some View {
            VStack { Text("Preview Host") }
                .frame(width: 200, height: 100)
                .onAppear {
                    // *** Cast kVK_ANSI_S to UInt16 ***
                    let sampleWorkspace = Workspace(
                        id: UUID(), name: "Sample Edit", windowInfos: [],
                        shortcutKeyCode: UInt16(kVK_ANSI_S),
                        shortcutModifiers: NSEvent.ModifierFlags.command.rawValue)  // Cmd+S
                    if workspaceService.workspaces.isEmpty {
                        workspaceService.workspaces = [sampleWorkspace]
                    }
                    workspaceToEdit = sampleWorkspace
                }
                .sheet(isPresented: $showSheet) {
                    if let workspace = workspaceToEdit {
                        EditWorkspaceView(workspace: workspace)
                            .environmentObject(workspaceService)
                    } else {
                        Text("Loading...")
                    }
                }
        }
    }
    return PreviewWrapper()
}
