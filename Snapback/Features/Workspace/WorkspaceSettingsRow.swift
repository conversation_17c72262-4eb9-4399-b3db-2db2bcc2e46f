import AppKit
import Carbon.HIToolbox
import Foundation
import KeyboardShortcuts
import <PERSON><PERSON>hor<PERSON>cut
import SwiftUI

struct WorkspaceSettingsRow: View {
    let workspace: Workspace
    let onSave: (String) -> Void
    let onDelete: () -> Void

    @EnvironmentObject var workspaceService: WorkspaceService
    @State private var isEditing = false
    @State private var editedName: String
    @FocusState private var isNameFieldFocused: Bool
    @State private var refreshTrigger = UUID()

    // Alert state
    @State private var showingDeleteAlert = false

    // Note: Removed conflict detection state - following professional app approach

    // Logger
    private let logger = LoggingService.shared
    private let serviceName = "WorkspaceSettingsRow"

    // Visual styling based on display status using existing DisplayChangeMonitorService
    private var visualStyle: WorkspaceVisualStyle {
        return WorkspaceVisualStyle(for: workspace)
    }

    init(
        workspace: Workspace,
        onSave: @escaping (String) -> Void,
        onDelete: @escaping () -> Void
    ) {
        self.workspace = workspace
        self.onSave = onSave
        self.onDelete = onDelete
        _editedName = State(initialValue: workspace.name)
    }

    var body: some View {
        VStack(spacing: 0) {
            HStack {
                // Name section
                if isEditing {
                    TextField("Workspace Name", text: $editedName)
                        .textFieldStyle(.roundedBorder)
                        .frame(minWidth: 150, idealWidth: 200, maxWidth: .infinity)
                        .focused($isNameFieldFocused)
                        .onSubmit {
                            if !editedName.isEmpty {
                                onSave(editedName)
                                isEditing = false
                            }
                        }
                        .onAppear {
                            isNameFieldFocused = true
                        }
                } else {
                    HStack(spacing: 8) {
                        Text(workspace.name)
                            .frame(
                                minWidth: 150, idealWidth: 200, maxWidth: .infinity,
                                alignment: .leading
                            )

                        // Status badge
                        if let badgeText = visualStyle.badgeText {
                            StatusBadge(text: badgeText, color: visualStyle.badgeColor)
                        }
                    }
                    .onTapGesture(count: 2) {
                        isEditing = true
                    }
                }

                // Shortcut section - Use KeyboardShortcuts.Recorder
                if let id = workspace.id {
                    let shortcutName = KeyboardShortcuts.Name.workspaceShortcut(for: id)

                    // Create the recorder with onChange handler
                    KeyboardShortcuts.Recorder(
                        for: shortcutName,
                        onChange: { newShortcut in
                            logger.debug(
                                "Shortcut changed for workspace '\(workspace.name)'",
                                service: serviceName,
                                category: .shortcuts
                            )

                            // Synchronize the shortcut with WorkspaceService
                            if let shortcut = newShortcut, let key = shortcut.key {
                                // Convert to our format for storage
                                let keyCode = UInt16(key.rawValue)
                                let modifiers = shortcut.modifiers.rawValue

                                // Update the workspace in the service
                                workspaceService.updateWorkspaceShortcut(
                                    id: id,
                                    keyCode: keyCode,
                                    modifiers: modifiers
                                )

                                // Force synchronize to ensure changes are written immediately
                                UserDefaults.standard.synchronize()

                                logger.debug(
                                    "Updated workspace shortcut - Key code: \(keyCode), Modifiers: \(modifiers)",
                                    service: serviceName,
                                    category: .shortcuts
                                )
                            } else if newShortcut == nil {
                                // If the shortcut was removed, update the workspace to remove the shortcut
                                workspaceService.updateWorkspaceShortcut(
                                    id: id,
                                    keyCode: 0,
                                    modifiers: 0
                                )

                                // Force synchronize to ensure changes are written immediately
                                UserDefaults.standard.synchronize()

                                logger.debug(
                                    "Cleared workspace shortcut",
                                    service: serviceName,
                                    category: .shortcuts
                                )
                            }

                            // Post notification to refresh menu
                            NotificationCenter.default.post(
                                name: Notification.Name("RefreshStatusMenu"), object: nil)
                        }
                    )
                    .frame(width: 150)
                    .disabled(isEditing)
                } else {
                    Text("No ID")
                        .foregroundColor(.secondary)
                        .frame(width: 150)
                }

                // Action buttons
                if isEditing {
                    Button("Save") {
                        if !editedName.isEmpty {
                            onSave(editedName)
                            isEditing = false
                        }
                    }
                    .keyboardShortcut(.return, modifiers: [])

                    Button("Cancel") {
                        editedName = workspace.name
                        isEditing = false
                    }
                    .keyboardShortcut(.escape, modifiers: [])
                } else {
                    // Menu button (3 vertical dots)
                    Menu {
                        Button {
                            workspaceService.triggerRestoreWorkspace(workspace: workspace)
                        } label: {
                            Label("Restore Workspace", systemImage: "arrow.clockwise")
                        }
                        .disabled(!visualStyle.canRestore)

                        Divider()

                        Button {
                            // Open edit window
                            openEditWindow(for: workspace)
                        } label: {
                            Label("Edit Workspace", systemImage: "pencil")
                        }

                        Button {
                            showingDeleteAlert = true
                        } label: {
                            Label("Delete Workspace", systemImage: "trash")
                        }
                    } label: {
                        ZStack {
                            Rectangle()
                                .fill(Color.gray.opacity(0.1))
                                .frame(width: 30, height: 30)
                                .cornerRadius(4)

                            Text("⋮")  // Vertical ellipsis character
                                .font(.system(size: 18, weight: .bold))
                                .foregroundColor(.secondary)
                        }
                    }
                    .menuStyle(.borderlessButton)
                    .menuIndicator(.hidden)
                    .fixedSize()  // This ensures the menu only activates when clicking on the icon
                }
            }
            .snapbackRowStyle()

            // Note: Removed conflict warning - following professional app approach
        }
        .onAppear {
            // Observe shortcut changes
            NotificationCenter.default.addObserver(
                forName: Notification.Name("KeyboardShortcuts_shortcutByNameDidChange"),
                object: nil,
                queue: .main
            ) { notification in
                guard let id = workspace.id,
                    let nameObj = notification.userInfo?["name"] as? KeyboardShortcuts.Name,
                    nameObj == KeyboardShortcuts.Name.workspaceShortcut(for: id)
                else {
                    return
                }

                // Force a refresh when this shortcut changes
                refreshTrigger = UUID()

                if KeyboardShortcuts.getShortcut(for: nameObj) != nil {

                    logger.debug(
                        "Validated shortcut after change notification for workspace '\(workspace.name)'",
                        service: serviceName,
                        category: .shortcuts
                    )
                } else {

                }
            }

            // Check if the current shortcut has conflicts
            if let id = workspace.id {
                let shortcutName = KeyboardShortcuts.Name.workspaceShortcut(for: id)
                if KeyboardShortcuts.getShortcut(for: shortcutName) != nil {

                    logger.debug(
                        "Validated existing shortcut for workspace '\(workspace.name)'",
                        service: serviceName,
                        category: .shortcuts
                    )
                }
            }
        }
        .opacity(visualStyle.opacity)
        .saturation(visualStyle.saturation)
        .help(visualStyle.statusDescription)
        .contextMenu {
            Button {
                workspaceService.triggerRestoreWorkspace(workspace: workspace)
            } label: {
                CompatLabel("Restore Workspace", systemImage: "arrow.clockwise")
            }
            .disabled(!visualStyle.canRestore)

            Button {
                // Open edit window
                openEditWindow(for: workspace)
            } label: {
                CompatLabel("Edit Workspace", systemImage: "pencil")
            }

            Button {
                showingDeleteAlert = true
            } label: {
                CompatLabel("Delete Workspace", systemImage: "trash")
            }
        }
        .alert("Delete Workspace", isPresented: $showingDeleteAlert) {
            Button("Delete", role: .destructive) {
                onDelete()
            }
            Button("Cancel", role: .cancel) {}
        } message: {
            Text("Are you sure you want to delete '\(workspace.name)'?")
        }
        .id(refreshTrigger)
        .onReceive(
            NotificationCenter.default.publisher(
                for: DisplayChangeMonitorService.displayConfigurationChanged)
        ) { _ in
            // Refresh UI when display configuration changes (using existing DisplayChangeMonitorService notification)
            refreshTrigger = UUID()
        }
        // Note: Removed conflict alert - following professional app approach
    }

    // Direct method to open edit window
    func openEditWindow(for workspace: Workspace) {
        print(
            "[WorkspaceSettingsRow] openEditWindow called for workspace: \(workspace.name)")

        // Create the window directly
        let editWorkspaceView = EditWorkspaceView(workspace: workspace)
            .environmentObject(workspaceService)
            .environmentObject(WindowDismissalManager.shared)

        let editWorkspaceWindow = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 600, height: 700),
            styleMask: [.titled, .closable, .miniaturizable, .resizable, .fullSizeContentView],
            backing: .buffered, defer: false
        )

        let hostingView = NSHostingView(rootView: editWorkspaceView)
        editWorkspaceWindow.contentView = hostingView
        editWorkspaceWindow.title = "Edit Workspace"
        editWorkspaceWindow.isReleasedWhenClosed = true
        editWorkspaceWindow.level = .floating  // Ensure window appears above other apps

        // Use intelligent positioning instead of simple center()
        WindowPositioningService.shared.positionWindow(
            editWorkspaceWindow,
            preferredSize: NSSize(width: 600, height: 700),
            context: .settingsRow
        )

        let windowController = NSWindowController(window: editWorkspaceWindow)
        windowController.showWindow(nil)

        editWorkspaceWindow.makeKeyAndOrderFront(nil)
        NSApp.activate(ignoringOtherApps: true)

        print("[WorkspaceSettingsRow] Edit window should now be visible")
    }
}
