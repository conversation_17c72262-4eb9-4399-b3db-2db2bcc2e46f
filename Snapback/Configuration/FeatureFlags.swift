import Foundation

/// Feature flags and configuration constants for Snapback
/// This file contains compile-time and runtime configuration options
public struct FeatureFlags {

    // MARK: - License System Configuration

    /// Controls whether the license system is enabled
    ///
    /// **Usage:**
    /// - Set to `false` for Setapp distribution or other scenarios where all features should be available without licensing
    /// - Set to `true` for direct sales where licensing is required
    ///
    /// **Implementation:**
    /// - When `false`: All app functionality is available without restrictions
    /// - When `true`: Standard license validation and restrictions apply
    /// - The license code remains intact but is bypassed when disabled
    ///
    /// **Distribution Scenarios:**
    /// - **Direct Sales**: `true` - Full license system with trials and paid licenses
    /// - **Setapp**: `false` - All features available, no license restrictions
    /// - **App Store**: `true` or `false` depending on business model
    ///
    /// **How to Change:**
    /// 1. For debug builds: Set environment variable `SNAPBACK_DISABLE_LICENSE=true`
    /// 2. For release builds: Change the `isLicenseSystemDisabled` value below
    /// 3. Clean and rebuild the project
    // static let isLicenseSystemDisabled: Bool = {
    //     #if DEBUG
    //     // In debug builds, check for environment variable or use default
    //     if let envValue = ProcessInfo.processInfo.environment["SNAPBACK_DISABLE_LICENSE"] {
    //         return envValue.lowercased() == "true" || envValue == "1"
    //     }
    //     return false // Default: license system enabled in debug
    //     #else
    //     // In release builds, use compile-time configuration
    //     // 🚨 CHANGE THIS VALUE FOR DIFFERENT DISTRIBUTIONS 🚨
    //     return false // Set to true for Setapp, false for direct sales
    //     #endif
    // }()

    static let isLicenseSystemDisabled: Bool = false
    // MARK: - Future Feature Flags

    /// Enable experimental features (for future use)
    static let enableExperimentalFeatures: Bool = false

    /// Enable advanced logging (for debugging)
    static let enableAdvancedLogging: Bool = {
        #if DEBUG
            return true
        #else
            return false
        #endif
    }()

    /// Master debug UI mode - when enabled, shows ALL normally hidden UI sections for testing
    /// This replaces individual debug toggles with a single centralized control
    /// Affects: Device Management, Purchase Interface, License Options, and other conditional UI
    static let debugUIMode: Bool = false
}

// MARK: - Usage Examples

/*

 ## How to Use Feature Flags

 ### In LicenseManager:
 ```swift
 var hasFullAccess: Bool {
     if FeatureFlags.isLicenseSystemDisabled {
         return true
     }
     return licenseStatus == .valid
 }
 ```

 ### In UI Components:
 ```swift
 var shouldShowLicenseTab: Bool {
     return !FeatureFlags.isLicenseSystemDisabled
 }
 ```

 ### In Service Classes:
 ```swift
 func performAction() {
     if FeatureFlags.isLicenseSystemDisabled || licenseManager.hasFullAccess {
         // Perform action
     } else {
         // Show license prompt
     }
 }
 ```

 ## Distribution Configuration

 ### For Setapp Distribution:
 1. Set `isLicenseSystemDisabled = true` in release builds
 2. All features become available without license checks
 3. License tab is hidden from Settings
 4. No trial or license prompts are shown

 ### For Direct Sales:
 1. Set `isLicenseSystemDisabled = false` in release builds
 2. Full license system with pro license validation
 3. License tab is visible in Settings
 4. Standard license prompts and restrictions apply

 ### For Development:
 1. Use environment variable `SNAPBACK_DISABLE_LICENSE=true` to test without licensing
 2. Use environment variable `SNAPBACK_DISABLE_LICENSE=false` or omit to test with licensing
 3. Set in Xcode scheme environment variables for easy testing

 */
