import SwiftUI

/// This file contains examples of how to use the SnapbackTheme
/// It's meant as a reference for developers working on the app
struct ThemeExamples: View {
    @State private var isSelected = false

    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                Text("Snapback Theme Examples")
                    .font(.title)
                    .padding(.bottom)

                // Section titles
                Group {
                    Text("Section Title Style")
                        .font(.headline)

                    Text("Example Section Title")
                        .snapbackSectionTitleStyle()
                        .padding(.bottom)
                }

                // Cards
                Group {
                    Text("Card Styles")
                        .font(.headline)

                    // Using the modifier
                    VStack(alignment: .leading) {
                        Text("Card with Modifier")
                            .font(.subheadline)

                        Toggle("Selected", isOn: $isSelected)
                            .padding(.bottom, SnapbackTheme.Padding.vertical)

                        Text("This card uses the .snapbackCardStyle() modifier")
                            .padding(SnapbackTheme.Padding.standard)
                            .frame(maxWidth: .infinity)
                            .snapbackCardStyle(isSelected: isSelected)
                    }
                    .padding(.bottom, SnapbackTheme.Padding.vertical)

                    // Using direct styling
                    VStack(alignment: .leading) {
                        Text("Card with Direct Styling")
                            .font(.subheadline)

                        SnapbackTheme.applyCardStyle(
                            to: Text("This card uses SnapbackTheme.applyCardStyle()")
                                .padding(SnapbackTheme.Padding.standard)
                                .frame(maxWidth: .infinity),
                            isSelected: isSelected
                        )
                    }
                    .padding(.bottom, SnapbackTheme.Padding.vertical)

                    // Using the constants directly
                    VStack(alignment: .leading) {
                        Text("Card with Direct Constants")
                            .font(.subheadline)

                        Text("This card uses theme constants directly")
                            .padding(SnapbackTheme.Padding.standard)
                            .frame(maxWidth: .infinity)
                            .background(
                                isSelected
                                    ? SnapbackTheme.Background.selectedCard
                                    : SnapbackTheme.Background.card
                            )
                            .clipShape(
                                RoundedRectangle(cornerRadius: SnapbackTheme.CornerRadius.card)
                            )
                            .overlay(
                                RoundedRectangle(cornerRadius: SnapbackTheme.CornerRadius.card)
                                    .strokeBorder(
                                        isSelected
                                            ? SnapbackTheme.Border.selectedCard
                                            : SnapbackTheme.Border.card,
                                        lineWidth: isSelected
                                            ? SnapbackTheme.BorderWidth.selected
                                            : SnapbackTheme.BorderWidth.normal
                                    )
                            )
                    }
                }

                // Typography
                Group {
                    Text("Typography")
                        .font(.headline)
                        .padding(.top)

                    VStack(alignment: .leading, spacing: 10) {
                        Text("Title Text")
                            .font(
                                .system(
                                    size: SnapbackTheme.FontSize.title,
                                    weight: SnapbackTheme.FontWeight.title))

                        Text("Heading Text")
                            .font(
                                .system(
                                    size: SnapbackTheme.FontSize.heading,
                                    weight: SnapbackTheme.FontWeight.heading))

                        Text("Body Text")
                            .font(
                                .system(
                                    size: SnapbackTheme.FontSize.body,
                                    weight: SnapbackTheme.FontWeight.body))

                        Text("Caption Text")
                            .font(
                                .system(
                                    size: SnapbackTheme.FontSize.caption,
                                    weight: SnapbackTheme.FontWeight.caption)
                            )
                            .foregroundColor(SnapbackTheme.Text.caption)
                    }
                    .padding()
                    .snapbackCardStyle()
                }

                // Row styling
                Group {
                    Text("Row Styling")
                        .font(.headline)
                        .padding(.top)

                    VStack(spacing: 0) {
                        HStack {
                            Text("Row Item 1")
                            Spacer()
                            Toggle("", isOn: .constant(true))
                                .labelsHidden()
                        }
                        .snapbackRowStyle()

                        Divider()

                        HStack {
                            Text("Row Item 2")
                            Spacer()
                            Toggle("", isOn: .constant(false))
                                .labelsHidden()
                        }
                        .snapbackRowStyle()

                        Divider()

                        VStack(alignment: .leading, spacing: 4) {
                            HStack {
                                Text("Row with Caption")
                                Spacer()
                                Toggle("", isOn: .constant(true))
                                    .labelsHidden()
                            }

                            Text("This is a caption that explains the setting in more detail.")
                                .snapbackCaptionStyle()
                        }
                        .snapbackRowStyle()
                    }
                    .snapbackCardStyle()
                }
            }
            .padding()
        }
    }
}

#Preview {
    ThemeExamples()
}
