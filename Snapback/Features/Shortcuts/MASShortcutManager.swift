import AppKit
import Carbon.HIToolbox
import Foundation
import MASShortcut

/// Implementation of ShortcutManagerProtocol using MASShortcut
public class MASShortcutManager: ShortcutManagerProtocol {
    /// The prefix for user defaults keys
    private let userDefaultsPrefix = "Snapback.Shortcuts."

    /// Logger
    private let logger = LoggingService.shared
    private let serviceName = "MASShortcutManager"

    /// The registered shortcuts
    private var registeredShortcuts: [String: MASShortcut] = [:]

    /// The default shortcuts
    private var defaultShortcuts: [String: Shortcut] = [:]

    /// The MASShortcutValidator instance
    private var validator: MASShortcutValidator? {
        return MASShortcutValidator.shared()
    }

    /// Initialize a new MASShortcutManager
    public init() {
        // Set up default shortcuts if needed
        setupDefaultShortcuts()

        logger.debug("MASShortcutManager initialized", service: serviceName, category: .shortcuts)
    }

    /// Set up default shortcuts
    private func setupDefaultShortcuts() {
        // You can add default shortcuts here
        // For example:
        defaultShortcuts["leftHalf"] = Shortcut(
            keyCode: Int(kVK_LeftArrow),
            modifiers: [.control, .option]
        )

        defaultShortcuts["rightHalf"] = Shortcut(
            keyCode: Int(kVK_RightArrow),
            modifiers: [.control, .option]
        )

        // Add more default shortcuts as needed
    }

    /// Convert a Shortcut to a MASShortcut
    /// - Parameter shortcut: The shortcut to convert
    /// - Returns: The MASShortcut
    private func toMASShortcut(_ shortcut: Shortcut) -> MASShortcut {
        return MASShortcut(keyCode: shortcut.keyCode, modifierFlags: shortcut.modifiers)
    }

    /// Convert a MASShortcut to a Shortcut
    /// - Parameter masShortcut: The MASShortcut to convert
    /// - Returns: The Shortcut
    private func fromMASShortcut(_ masShortcut: MASShortcut) -> Shortcut {
        return Shortcut(keyCode: Int(masShortcut.keyCode), modifiers: masShortcut.modifierFlags)
    }

    /// Get the user defaults key for a shortcut name
    /// - Parameter name: The shortcut name
    /// - Returns: The user defaults key
    private func userDefaultsKey(for name: String) -> String {
        return "\(userDefaultsPrefix)\(name)"
    }

    // MARK: - ShortcutManagerProtocol Implementation

    public func registerShortcut(
        _ shortcut: Shortcut, forName name: String, action: @escaping () -> Void
    ) {
        // Note: MASShortcut registration has been removed
        // We only store the shortcut in UserDefaults for persistence

        let masShortcut = toMASShortcut(shortcut)

        logger.debug(
            "Registering shortcut for '\(name)' - Key code: \(shortcut.keyCode), Modifiers: \(shortcut.modifiers)",
            service: serviceName,
            category: .shortcuts
        )

        // Save the shortcut to user defaults
        if let data = try? NSKeyedArchiver.archivedData(
            withRootObject: masShortcut, requiringSecureCoding: false)
        {
            UserDefaults.standard.set(data, forKey: userDefaultsKey(for: name))
        } else {
            logger.error(
                "Failed to archive shortcut data for '\(name)'",
                service: serviceName,
                category: .shortcuts
            )
        }

        // Post a notification that the shortcut changed
        NotificationCenter.default.post(
            name: Notification.Name("ShortcutManager.shortcutChanged"),
            object: nil,
            userInfo: ["name": name]
        )
    }

    public func unregisterShortcut(forName name: String) {
        // Note: MASShortcut unregistration has been removed
        // We only remove the shortcut from our internal tracking

        logger.debug(
            "Unregistering shortcut for '\(name)'",
            service: serviceName,
            category: .shortcuts
        )

        // Remove the shortcut from the registered shortcuts
        registeredShortcuts.removeValue(forKey: name)

        // Remove from UserDefaults if needed
        // UserDefaults.standard.removeObject(forKey: userDefaultsKey(for: name))
    }

    public func unregisterAllShortcuts() {
        // Note: MASShortcut unregistration has been removed
        // We only clear our internal tracking

        logger.debug(
            "Unregistering all shortcuts",
            service: serviceName,
            category: .shortcuts
        )

        // Clear the registered shortcuts
        registeredShortcuts.removeAll()

        // Could also clear from UserDefaults if needed
        // for (name, _) in registeredShortcuts {
        //     UserDefaults.standard.removeObject(forKey: userDefaultsKey(for: name))
        // }
    }

    public func getShortcut(forName name: String) -> Shortcut? {
        // Try to get the shortcut from user defaults
        if let data = UserDefaults.standard.data(forKey: userDefaultsKey(for: name)),
            let masShortcut = try? NSKeyedUnarchiver.unarchivedObject(
                ofClass: MASShortcut.self, from: data)
        {
            return fromMASShortcut(masShortcut)
        }

        // Fall back to default shortcut if available
        return defaultShortcuts[name]
    }

    public func setShortcut(_ shortcut: Shortcut?, forName name: String) {
        // Unregister any existing shortcut with the same name
        unregisterShortcut(forName: name)

        if let shortcut = shortcut {
            // Convert to MASShortcut
            let masShortcut = toMASShortcut(shortcut)

            logger.debug(
                "Setting shortcut for '\(name)' - Key code: \(shortcut.keyCode), Modifiers: \(shortcut.modifiers)",
                service: serviceName,
                category: .shortcuts
            )

            // Save the shortcut to user defaults
            if let data = try? NSKeyedArchiver.archivedData(
                withRootObject: masShortcut, requiringSecureCoding: false)
            {
                UserDefaults.standard.set(data, forKey: userDefaultsKey(for: name))
            } else {
                logger.error(
                    "Failed to archive shortcut data for '\(name)'",
                    service: serviceName,
                    category: .shortcuts
                )
            }
        } else {
            logger.debug(
                "Removing shortcut for '\(name)'",
                service: serviceName,
                category: .shortcuts
            )

            // Remove the shortcut from user defaults
            UserDefaults.standard.removeObject(forKey: userDefaultsKey(for: name))
        }

        // Post a notification that the shortcut changed
        NotificationCenter.default.post(
            name: Notification.Name("ShortcutManager.shortcutChanged"),
            object: nil,
            userInfo: ["name": name]
        )
    }

    public func resetShortcut(forName name: String) {
        // Get the default shortcut
        if let defaultShortcut = defaultShortcuts[name] {
            logger.debug(
                "Resetting shortcut for '\(name)' to default - Key code: \(defaultShortcut.keyCode), Modifiers: \(defaultShortcut.modifiers)",
                service: serviceName,
                category: .shortcuts
            )

            // Set the shortcut to the default
            setShortcut(defaultShortcut, forName: name)
        } else {
            logger.debug(
                "No default shortcut for '\(name)', removing shortcut",
                service: serviceName,
                category: .shortcuts
            )

            // Remove the shortcut if there's no default
            setShortcut(nil, forName: name)
        }
    }

    public func resetAllShortcuts() {
        logger.debug(
            "Resetting all shortcuts to defaults",
            service: serviceName,
            category: .shortcuts
        )

        // Reset all shortcuts to their defaults
        for (name, _) in defaultShortcuts {
            resetShortcut(forName: name)
        }
    }

}
