# Snapback

Snapback is a powerful macOS menu bar utility that enhances productivity by providing intuitive window management through keyboard shortcuts, menu bar actions, and savable workspace layouts.

![Snapback Menu Bar](./docs/images/menubar.png)

## Features

### 🎯 Window Snapping

- **Quick window positioning** to predefined layouts:
  - Halves (Left/Right/Top/Bottom)
  - Quarters (Top-Left/Top-Right/Bottom-Left/Bottom-Right)
  - Thirds (Left/Center/Right)
  - Two-Thirds (Left/Right)
  - Fullscreen and Almost Maximize
- **Global keyboard shortcuts** with customizable bindings
- **Drag-to-snap** functionality with visual indicators
- **Multi-display support** with intelligent window movement between screens
- **Subsequent execution modes** for repeated commands:
  - Move across displays
  - Resize on current display
  - Hybrid modes for different behaviors

### 💼 Workspaces

- **Save** current window layouts with:
  - Window positions and sizes
  - Associated applications
  - Monitor assignments
  - Fullscreen states
- **Restore** saved layouts with a single click or keyboard shortcut
- **Visual workspace preview** showing window arrangements
- **Custom keyboard shortcuts** per workspace
- **Multi-display support** with correct screen arrangement visualization

### 🔔 Notifications

- **Toast notifications** for system events and user actions
- **Visual feedback** for operations like workspace restoration
- **Status updates** during multi-step operations
- **Error handling** with user-friendly messages

### 🎨 User Interface

- **Consistent theme system** across the application
- **Modern SwiftUI interface** with macOS-native styling
- **Customizable appearance** options
- **Accessibility support** for all UI elements

### ⚙️ Settings

- **Start at login** option
- **Show/hide menu bar icon**
- **Default snap behavior** configuration
- **Keyboard shortcut** customization
- **Drag-to-snap** configuration
- **Comprehensive logging system** for troubleshooting

## Technical Overview

### Architecture

Snapback is built with modern Swift and SwiftUI, following a feature-oriented architecture:

```mermaid
graph TD
    A[AppDelegate] --> B[WindowManagement]
    A --> C[Workspace]
    A --> D[UI]
    B --> E[WindowMover]
    B --> F[WindowCalculation]
    B --> G[ScreenDetection]
    B --> H[DragToSnap]
    C --> I[WorkspacePreview]
    C --> J[WorkspaceRestoration]
    D --> K[Toast]
    D --> L[Theme]
```

### Key Components

1. **Window Management**

   - Uses macOS Accessibility APIs (`AXUIElement`)
   - Handles coordinate system conversions
   - Manages window states and transitions
   - Supports drag-to-snap with visual indicators

2. **Workspace Handling**

   - JSON-based persistence
   - Intelligent window restoration
   - Visual preview with multi-display support
   - Status notifications during operations

3. **User Interface**
   - SwiftUI views with consistent theming
   - Menu bar integration
   - Toast notification system
   - Accessibility support

### Core Technologies

- **Swift 5.9+**: Modern language features
- **SwiftUI**: Declarative UI framework
- **Combine**: Reactive state management
- **ApplicationServices**: Window manipulation
- **CoreGraphics**: Screen and window geometry
- **KeyboardShortcuts**: Global shortcut handling

## Development

### Prerequisites

- Xcode 15.0+
- macOS 12.4+
- Swift 5.9+

### Building

1. Clone the repository:

```bash
git clone https://github.com/yourusername/snapback.git
cd snapback
```

2. Open in Xcode:

```bash
xed .
```

3. Build and run:

- Select the Snapback target
- Choose your Mac as the run destination
- Press ⌘R to build and run

### Testing

Run the test suite:

```bash
xcodebuild test -project Snapback.xcodeproj -scheme Snapback
```

### Project Structure

Snapback follows a feature-based organization rather than the traditional MVC approach:

```
Snapback/
├── App/
│   ├── SnapbackApp.swift       # Entry point
│   └── AppDelegate.swift     # Core application logic
├── Features/
│   ├── WindowManagement/     # Window positioning and snapping
│   │   ├── WindowMover.swift
│   │   ├── WindowCalculation/
│   │   └── ScreenDetection/
│   ├── Workspace/            # Workspace management
│   │   ├── WorkspaceService.swift
│   │   ├── WorkspacePreview/
│   │   └── WorkspaceRestoration/
│   ├── UI/                   # User interface components
│   │   ├── Theme.swift
│   │   └── Components/
│   ├── Toast/                # Toast notification system
│   │   ├── ToastManager.swift
│   │   └── ToastView.swift
│   └── Logging/              # Logging system
│       ├── LoggingService.swift
│       └── LoggingManager.swift
└── Extensions/               # Swift extensions
    ├── CGRectExtensions.swift
    └── ViewExtensions.swift
```

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## License

This project is licensed under the GPL-3.0 License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- Inspired by [Rectangle](https://github.com/rxhanson/Rectangle) app's window management approach
- Uses [MASShortcut](https://github.com/shpakovski/MASShortcut) for shortcut recording
- Uses [KeyboardShortcuts](https://github.com/sindresorhus/KeyboardShortcuts) for global shortcut handling
- Incorporates code adapted from various open-source projects (see credits in source files)

## Support

- Report issues on GitHub
- Visit [snapback.app](https://snapback.app) for documentation
- Contact <NAME_EMAIL>

---

Made with ❤️ by Ricardo Ramirez
