//
//  ContentView.swift
//  Snapback
//
//  Created by <PERSON> on 23/01/25.
//

import SwiftUI

struct ContentView: View {
    var body: some View {
        VStack {
            Image.systemCompat("globe")
                .imageScale(.large)
                .foregroundStyle(.tint)
            Text("Hello, world!")
                .font(.system(size: SnapbackTheme.FontSize.body))
                .foregroundColor(SnapbackTheme.Text.primary)
        }
        .padding(SnapbackTheme.Padding.standard)
    }
}

#Preview {
    ContentView()
}
