import AppKit
import Foundation

/// Class to check for conflicting window management applications
class ConflictingAppsChecker {
    static let shared = ConflictingAppsChecker()

    /// Logger
    private let logger = LoggingService.shared
    private let serviceName = "ConflictingAppsChecker"

    // Known conflicting window management applications
    private let conflictingAppsIds: [String: String] = [
        "com.divisiblebyzero.Spectacle": "Spectacle",
        "com.crowdcafe.windowmagnet": "Magnet",
        "com.hegenberg.BetterSnapTool": "BetterSnapTool",
        "com.manytricks.Moom": "Moom",
        "com.knollsoft.Rectangle": "Rectangle",
    ]

    // Cache of running conflicting apps to avoid repeated checks
    private var runningConflictingApps: [String] = []
    private var lastCheckTime: Date = Date.distantPast

    // Known problematic applications with drag-to-snap
    private let problematicAppBundleIds: [String] = [
        "com.mathworks.matlab",
        "com.live2d.cubism.CECubismEditorApp",
        "com.aquafold.datastudio.DataStudio",
        "com.adobe.illustrator",
        "com.adobe.AfterEffects",
    ]

    // Java-based apps with dynamic bundle IDs
    private let problematicJavaAppNames: [String] = [
        "thinkorswim",
        "Trader Workstation",
    ]

    /// Check for other window management applications that might conflict with Snapback
    /// - Returns: Tuple with (found conflict, app name)
    func checkForConflictingApps() -> (Bool, String?) {
        let runningApps = NSWorkspace.shared.runningApplications

        for app in runningApps {
            guard let bundleId = app.bundleIdentifier else { continue }

            if let conflictingAppName = conflictingAppsIds[bundleId] {
                return (true, conflictingAppName)
            }
        }

        return (false, nil)
    }

    /// Check for applications known to have issues with drag-to-snap
    /// - Returns: Tuple with (found problematic app, app name)
    func checkForProblematicApps() -> (Bool, String?) {
        let runningApps = NSWorkspace.shared.runningApplications

        // Check for known problematic apps by bundle ID
        for app in runningApps {
            guard let bundleId = app.bundleIdentifier else { continue }

            if problematicAppBundleIds.contains(bundleId) {
                return (true, app.localizedName ?? bundleId)
            }

            // Check for Java-based apps by name
            if let appName = app.localizedName,
                problematicJavaAppNames.contains(where: { appName.contains($0) })
            {
                return (true, appName)
            }
        }

        return (false, nil)
    }

    /// Check for conflicts with macOS built-in tiling features (macOS 15+)
    /// - Returns: True if there's a conflict with macOS tiling
    @available(macOS 15.0, *)
    func checkForBuiltInTilingConflict() -> Bool {
        // This is a placeholder for macOS 15+ tiling conflict detection
        // In a real implementation, we would check system settings
        return false
    }

    /// Get a list of running conflicting window management applications
    /// - Returns: Array of app names that might conflict with Snapback
    func getRunningConflictingApps() -> [String] {
        // Only refresh the cache every 5 seconds to avoid performance issues
        let now = Date()
        let timeSinceLastCheck = now.timeIntervalSince(lastCheckTime)

        logger.debug(
            "🕒 [CACHE CHECK] Time since last check: \(String(format: "%.2f", timeSinceLastCheck))s (threshold: 5.0s)",
            service: serviceName,
            category: .shortcuts
        )

        if timeSinceLastCheck > 5.0 {
            logger.debug(
                "🔄 [CACHE REFRESH] Cache expired, refreshing running conflicting apps...",
                service: serviceName,
                category: .shortcuts
            )
            refreshRunningConflictingApps()
            lastCheckTime = now
        } else {
            logger.debug(
                "📋 [CACHE HIT] Using cached conflicting apps: \(runningConflictingApps.isEmpty ? "NONE" : runningConflictingApps.joined(separator: ", "))",
                service: serviceName,
                category: .shortcuts
            )
        }

        return runningConflictingApps
    }

    /// Refresh the cache of running conflicting apps
    private func refreshRunningConflictingApps() {
        logger.debug(
            "🔄 [CACHE REFRESH] Starting refresh of running conflicting apps...",
            service: serviceName,
            category: .shortcuts
        )

        let previousConflictingApps = runningConflictingApps
        runningConflictingApps = []
        let runningApps = NSWorkspace.shared.runningApplications

        logger.debug(
            "🔄 [CACHE REFRESH] Scanning \(runningApps.count) running applications...",
            service: serviceName,
            category: .shortcuts
        )

        // Log all known conflicting app bundle IDs for debugging
        logger.debug(
            "🔄 [CACHE REFRESH] Known conflicting app bundle IDs: \(conflictingAppsIds.keys.joined(separator: ", "))",
            service: serviceName,
            category: .shortcuts
        )

        for app in runningApps {
            guard let bundleId = app.bundleIdentifier else {
                logger.debug(
                    "🔄 [CACHE REFRESH] Skipping app with no bundle ID: \(app.localizedName ?? "Unknown")",
                    service: serviceName,
                    category: .shortcuts
                )
                continue
            }

            logger.debug(
                "🔄 [CACHE REFRESH] Checking app: \(app.localizedName ?? "Unknown") (\(bundleId))",
                service: serviceName,
                category: .shortcuts
            )

            if let conflictingAppName = conflictingAppsIds[bundleId] {
                runningConflictingApps.append(conflictingAppName)
                logger.info(
                    "✅ [CONFLICTING APP FOUND] \(conflictingAppName) is running (\(bundleId))",
                    service: serviceName,
                    category: .shortcuts
                )
            }
        }

        // Log the results of the refresh
        if runningConflictingApps.isEmpty {
            logger.info(
                "🔄 [CACHE REFRESH] No conflicting window management apps found running",
                service: serviceName,
                category: .shortcuts
            )
        } else {
            logger.info(
                "🔄 [CACHE REFRESH] Found \(runningConflictingApps.count) conflicting apps: \(runningConflictingApps.joined(separator: ", "))",
                service: serviceName,
                category: .shortcuts
            )
        }

        // Log changes from previous state
        let addedApps = Set(runningConflictingApps).subtracting(Set(previousConflictingApps))
        let removedApps = Set(previousConflictingApps).subtracting(Set(runningConflictingApps))

        if !addedApps.isEmpty {
            logger.info(
                "➕ [CACHE REFRESH] Newly detected conflicting apps: \(addedApps.joined(separator: ", "))",
                service: serviceName,
                category: .shortcuts
            )
        }

        if !removedApps.isEmpty {
            logger.info(
                "➖ [CACHE REFRESH] No longer running conflicting apps: \(removedApps.joined(separator: ", "))",
                service: serviceName,
                category: .shortcuts
            )
        }

        if addedApps.isEmpty && removedApps.isEmpty && !previousConflictingApps.isEmpty {
            logger.debug(
                "🔄 [CACHE REFRESH] No changes in running conflicting apps",
                service: serviceName,
                category: .shortcuts
            )
        }
    }

}
