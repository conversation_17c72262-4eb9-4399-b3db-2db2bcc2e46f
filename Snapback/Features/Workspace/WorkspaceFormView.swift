import AppKit
import Carbon.HIToolbox
import Foundation
import KeyboardShortcuts
import SwiftUI

/// A reusable form component for saving and editing workspaces
struct WorkspaceFormView: View {
    // MARK: - Properties

    @EnvironmentObject var workspaceService: WorkspaceService
    @Environment(\.dismiss) var dismiss

    // Logger
    private let logger = LoggingService.shared
    private let serviceName = "WorkspaceFormView"

    // Form State
    @Binding var workspaceName: String
    @Binding var customLayoutName: String
    @FocusState private var isNameFieldFocused: Bool

    // Validation State
    @State private var nameValidationError: String?

    // App selection state
    @Binding var appItems: [AppSelectionItem]
    @Binding var selectedCount: Int

    // Shortcut Recording State
    @Binding var shortcutDisplayString: String
    @Binding var recordedKeyCode: UInt16?
    @Binding var recordedModifiers: UInt?

    // Workspace Settings
    @Binding var closeNonWorkspaceWindows: Bool

    // Note: Removed conflict detection state - following professional app approach

    // Mode
    let isEditMode: Bool
    let existingWorkspace: Workspace?

    // Callbacks
    var onSave: () -> Void
    var onCancel: () -> Void
    var onUpdatePositions: () -> Void

    // MARK: - Display Arrangement Detection

    /// Check if the current display arrangement has critical changes that affect workspace functionality
    /// Only shows warnings for the 2 scenarios that actually break saved workspaces:
    /// 1. Display count decreased (display removed) - windows on removed displays lost
    /// 2. Main display changed (primary display switched) - coordinate system changed
    ///
    /// Display additions are NOT critical because existing displays remain unchanged
    /// Uses the global monitor service for consistent detection regardless of form visibility
    private var hasDisplayArrangementChanged: Bool {
        guard isEditMode, let workspace = existingWorkspace else {
            return false
        }

        // Use the global monitor service for consistent detection
        let hasCriticalChange = DisplayChangeMonitorService.shared.workspaceHasCriticalChanges(
            workspace)

        if hasCriticalChange {
            if let changeDescription = DisplayChangeMonitorService.shared
                .getCriticalChangeDescription(for: workspace)
            {
                logger.debug(
                    "🚨 CRITICAL display arrangement change detected: \(changeDescription)",
                    service: serviceName)
            }
        } else {
            logger.debug(
                "✅ No critical display arrangement changes detected (ignoring non-critical changes)",
                service: serviceName)
        }

        return hasCriticalChange
    }

    /// Get a specific warning message based on the type of critical display change
    private func getDisplayChangeWarningMessage() -> String {
        guard let workspace = existingWorkspace else {
            return "Display configuration has changed since this workspace was created."
        }

        if let changeDescription = DisplayChangeMonitorService.shared.getCriticalChangeDescription(
            for: workspace)
        {
            return
                "\(changeDescription) since this workspace was created. Windows may not restore to their original positions. Click \"Update Positions\" to capture the current window layout if needed."
        } else {
            return
                "Critical display configuration changes detected since this workspace was created. Windows may not restore to their original positions. Click \"Update Positions\" to capture the current window layout if needed."
        }
    }

    // MARK: - Validation

    /// Validate the workspace name for uniqueness
    /// - Returns: True if valid, false if there are validation errors
    private func validateWorkspaceName() -> Bool {
        let trimmedName = workspaceName.trimmingCharacters(in: .whitespacesAndNewlines)

        // Check if name is empty
        if trimmedName.isEmpty {
            nameValidationError = "Workspace name cannot be empty"
            isNameFieldFocused = true
            return false
        }

        // Check for uniqueness
        let excludingId = isEditMode ? existingWorkspace?.id : nil
        if !workspaceService.isWorkspaceNameUnique(trimmedName, excludingId: excludingId) {
            nameValidationError =
                "A workspace with this name already exists. Please choose a different name for your workspace."
            isNameFieldFocused = true
            return false
        }

        // Clear any existing error
        nameValidationError = nil
        return true
    }

    // MARK: - Initializers

    /// Initialize for creating a new workspace
    init(
        workspaceName: Binding<String>,
        customLayoutName: Binding<String>,
        appItems: Binding<[AppSelectionItem]>,
        selectedCount: Binding<Int>,
        shortcutDisplayString: Binding<String>,
        recordedKeyCode: Binding<UInt16?>,
        recordedModifiers: Binding<UInt?>,
        closeNonWorkspaceWindows: Binding<Bool>,
        onSave: @escaping () -> Void,
        onCancel: @escaping () -> Void,
        onUpdatePositions: @escaping () -> Void
    ) {
        self._workspaceName = workspaceName
        self._customLayoutName = customLayoutName
        self._appItems = appItems
        self._selectedCount = selectedCount
        self._shortcutDisplayString = shortcutDisplayString
        self._recordedKeyCode = recordedKeyCode
        self._recordedModifiers = recordedModifiers
        self._closeNonWorkspaceWindows = closeNonWorkspaceWindows
        self.isEditMode = false
        self.existingWorkspace = nil
        self.onSave = onSave
        self.onCancel = onCancel
        self.onUpdatePositions = onUpdatePositions
    }

    /// Initialize for editing an existing workspace
    init(
        workspace: Workspace,
        workspaceName: Binding<String>,
        customLayoutName: Binding<String>,
        appItems: Binding<[AppSelectionItem]>,
        selectedCount: Binding<Int>,
        shortcutDisplayString: Binding<String>,
        recordedKeyCode: Binding<UInt16?>,
        recordedModifiers: Binding<UInt?>,
        closeNonWorkspaceWindows: Binding<Bool>,
        onSave: @escaping () -> Void,
        onCancel: @escaping () -> Void,
        onUpdatePositions: @escaping () -> Void
    ) {
        self._workspaceName = workspaceName
        self._customLayoutName = customLayoutName
        self._appItems = appItems
        self._selectedCount = selectedCount
        self._shortcutDisplayString = shortcutDisplayString
        self._recordedKeyCode = recordedKeyCode
        self._recordedModifiers = recordedModifiers
        self._closeNonWorkspaceWindows = closeNonWorkspaceWindows
        self.isEditMode = true
        self.existingWorkspace = workspace
        self.onSave = onSave
        self.onCancel = onCancel
        self.onUpdatePositions = onUpdatePositions
    }

    // MARK: - View Body

    var body: some View {
        VStack(spacing: 0) {
            // Header
            HStack {
                Text(isEditMode ? "Edit Workspace" : "Save Workspace")
                    .font(.system(size: SnapbackTheme.FontSize.title, weight: .semibold))
                    .padding(.top, 20)
                    .padding(.bottom, 10)

                Spacer()
            }
            .padding(.horizontal, 20)

            // Main content
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // Basic info section
                    VStack(alignment: .leading, spacing: 15) {
                        // Workspace Name and Shortcut in the same row
                        Text("Workspace Information")
                            .snapbackSectionTitleStyle()

                        // FREEMIUM MODEL: Show workspace count for free users
                        if !isEditMode {
                            workspaceCountIndicator
                        }

                        GroupBox {
                            VStack(spacing: 0) {
                                VStack(alignment: .leading, spacing: 4) {
                                    // Inputs row
                                    HStack(alignment: .center, spacing: 15) {
                                        // Name input
                                        TextField("Enter workspace name", text: $workspaceName)
                                            .textFieldStyle(.plain)
                                            .frame(maxWidth: .infinity)
                                            .focused($isNameFieldFocused)
                                            .onChangeCompat(of: workspaceName) { _ in
                                                // Clear validation error when user starts typing
                                                if nameValidationError != nil {
                                                    nameValidationError = nil
                                                }
                                            }

                                        // Shortcut recorder (no label)
                                        if let id = existingWorkspace?.id, isEditMode {
                                            // For edit mode, use the workspace's ID for the shortcut
                                            let shortcutName = KeyboardShortcuts.Name
                                                .workspaceShortcut(
                                                    for: id)
                                            KeyboardShortcuts.Recorder(
                                                for: shortcutName,
                                                onChange: { newShortcut in
                                                    logger.debug(
                                                        "🎹 SHORTCUT CHANGE: Edit mode onChange callback triggered",
                                                        service: serviceName,
                                                        category: .shortcuts
                                                    )

                                                    if let shortcut = newShortcut {
                                                        logger.debug(
                                                            "🎹 SHORTCUT CHANGE: Edit mode - new shortcut recorded: \(shortcut)",
                                                            service: serviceName,
                                                            category: .shortcuts
                                                        )

                                                    } else {
                                                        logger.debug(
                                                            "🎹 SHORTCUT CHANGE: Edit mode - shortcut cleared",
                                                            service: serviceName,
                                                            category: .shortcuts
                                                        )
                                                        // Note: No conflict state to clear
                                                    }

                                                    logger.debug(
                                                        "🎹 SHORTCUT CHANGE: Edit mode onChange completed - no conflict detection",
                                                        service: serviceName,
                                                        category: .shortcuts
                                                    )
                                                }
                                            )
                                            .frame(maxWidth: .infinity, alignment: .trailing)
                                        } else {
                                            // For save mode, use a temporary shortcut
                                            let tempShortcutName = KeyboardShortcuts.Name(
                                                "temp_workspace_shortcut")
                                            KeyboardShortcuts.Recorder(
                                                for: tempShortcutName,
                                                onChange: { newShortcut in
                                                    logger.debug(
                                                        "🎹 SHORTCUT CHANGE: Save mode onChange callback triggered",
                                                        service: serviceName,
                                                        category: .shortcuts
                                                    )

                                                    if let shortcut = newShortcut {
                                                        logger.debug(
                                                            "🎹 SHORTCUT CHANGE: Save mode - new shortcut recorded: \(shortcut)",
                                                            service: serviceName,
                                                            category: .shortcuts
                                                        )

                                                    } else {
                                                        logger.debug(
                                                            "🎹 SHORTCUT CHANGE: Save mode - shortcut cleared",
                                                            service: serviceName,
                                                            category: .shortcuts
                                                        )
                                                        // Note: No conflict state to clear
                                                    }

                                                    logger.debug(
                                                        "🎹 SHORTCUT CHANGE: Save mode onChange completed - no conflict detection",
                                                        service: serviceName,
                                                        category: .shortcuts
                                                    )
                                                }
                                            )
                                            .frame(maxWidth: .infinity, alignment: .trailing)
                                        }
                                    }

                                    // Description text or validation error
                                    if let error = nameValidationError {
                                        Text(error)
                                            .font(.system(size: SnapbackTheme.FontSize.caption))
                                            .foregroundColor(SnapbackTheme.Text.error)
                                            .frame(maxWidth: 350, alignment: .leading)
                                            .fixedSize(horizontal: false, vertical: true)
                                    } else {
                                        Text(
                                            "Enter a name for your workspace and optionally assign a keyboard shortcut for quick access."
                                        )
                                        .snapbackCaptionStyle()
                                    }
                                }
                                .snapbackRowStyle()
                            }
                        }

                        // Note: Removed conflict warning - following professional app approach
                    }
                    // Workspace Settings section

                    GroupBox {
                        VStack(spacing: 0) {
                            VStack(alignment: .leading, spacing: 4) {
                                HStack {
                                    Text("Close non-workspace windows when restoring")
                                    Spacer()
                                    Toggle(
                                        "",
                                        isOn: $closeNonWorkspaceWindows
                                    )
                                    .toggleStyle(.switch)
                                    .labelsHidden()
                                }

                                Text(
                                    "When enabled, windows that are not part of this workspace will be closed when restoring this workspace."
                                )
                                .snapbackCaptionStyle()
                            }
                            .snapbackRowStyle()
                        }
                    }

                    // Preview section
                    VStack(alignment: .leading, spacing: SnapbackTheme.Padding.large) {
                        HStack {
                            Text("Preview")
                                .snapbackSectionTitleStyle()

                            Spacer()

                            // Update Positions button
                            Button(action: onUpdatePositions) {
                                HStack(spacing: SnapbackTheme.Padding.small) {
                                    Image(systemName: "arrow.clockwise")
                                        .font(.system(size: SnapbackTheme.FontSize.caption))
                                    Text("Update Positions")
                                        .font(.system(size: SnapbackTheme.FontSize.body))
                                }
                            }
                            .buttonStyle(.bordered)
                        }

                        // Workspace preview with consistent container styling
                        GroupBox {
                            WorkspacePreview(
                                windowInfos: appItems.filter { $0.isSelected }.map {
                                    $0.windowInfo
                                },
                                onWindowClose: { closedWindowInfo in
                                    // Find the app item that corresponds to the closed window
                                    if let index = appItems.firstIndex(where: {
                                        $0.windowInfo.appBundleIdentifier
                                            == closedWindowInfo.appBundleIdentifier
                                            && $0.windowInfo.frame.origin.x
                                                == closedWindowInfo.frame.origin.x
                                            && $0.windowInfo.frame.origin.y
                                                == closedWindowInfo.frame.origin.y
                                    }) {
                                        // Set the app item to not selected
                                        appItems[index].isSelected = false

                                        // Update the selected count
                                        selectedCount = appItems.filter { $0.isSelected }.count

                                        logger.debug(
                                            "Window closed from preview: \(appItems[index].appName), Selected count: \(selectedCount)",
                                            service: serviceName,
                                            category: .userInterface
                                        )
                                    }
                                }
                            )
                            .frame(height: 220)
                            .frame(maxWidth: .infinity)

                        }
                    }

                    // Display arrangement warning (only show in edit mode if arrangement changed)
                    if hasDisplayArrangementChanged {
                        VStack(alignment: .leading, spacing: 12) {
                            HStack(spacing: 8) {
                                Image(systemName: "exclamationmark.triangle.fill")
                                    .foregroundColor(.orange)
                                    .font(.system(size: 16))

                                Text("Display Settings Changed")
                                    .font(
                                        .system(
                                            size: SnapbackTheme.FontSize.body, weight: .semibold)
                                    )
                                    .foregroundColor(.primary)
                            }

                            Text(getDisplayChangeWarningMessage())
                                .font(.system(size: SnapbackTheme.FontSize.caption))
                                .foregroundColor(SnapbackTheme.Text.secondary)
                                .fixedSize(horizontal: false, vertical: true)
                        }
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding(15)
                        .background(SnapbackTheme.Background.warning)
                        .cornerRadius(SnapbackTheme.CornerRadius.card)
                        .overlay(
                            RoundedRectangle(cornerRadius: SnapbackTheme.CornerRadius.card)
                                .strokeBorder(SnapbackTheme.Border.warning, lineWidth: 1)
                        )
                    }

                    // App selection section
                    VStack(alignment: .leading, spacing: SnapbackTheme.Padding.large) {
                        Text("Applications")
                            .snapbackSectionTitleStyle()

                        // Description text
                        Text(
                            "Select which applications to include in this workspace. Only selected applications will be restored when you activate this workspace."
                        )
                        .snapbackCaptionStyle()

                        // Applications list with consistent container styling
                        GroupBox {
                            VStack(spacing: 0) {
                                // Calculate if we need scrolling based on number of items
                                // Each row is approximately 40 pixels high
                                let rowHeight: CGFloat = 40
                                let maxRowsBeforeScrolling = 6
                                let shouldScroll = appItems.count > maxRowsBeforeScrolling
                                let contentHeight =
                                    shouldScroll
                                    ? rowHeight * CGFloat(maxRowsBeforeScrolling)
                                    : rowHeight * CGFloat(appItems.count)

                                // Use ScrollView conditionally based on item count
                                Group {
                                    if shouldScroll {
                                        ScrollView {
                                            appListContent
                                        }
                                        .frame(height: contentHeight)
                                    } else {
                                        appListContent
                                    }
                                }

                            }
                        }

                        HStack {
                            Text(
                                "Selected: \(selectedCount) of \(appItems.count) apps"
                            )
                            .font(.system(size: SnapbackTheme.FontSize.caption))
                            .foregroundColor(SnapbackTheme.Text.secondary)

                            Spacer()

                            Button("Select All") {
                                // Set all items to selected
                                for index in appItems.indices {
                                    appItems[index].isSelected = true
                                }

                                // Update the selected count
                                selectedCount = appItems.count
                                logger.debug(
                                    "Select All pressed: \(selectedCount) apps selected",
                                    service: serviceName,
                                    category: .userInterface
                                )
                            }
                            .buttonStyle(.borderless)
                            .font(.system(size: SnapbackTheme.FontSize.caption))

                            Button("Deselect All") {
                                // Set all items to deselected
                                for index in appItems.indices {
                                    appItems[index].isSelected = false
                                }

                                // Update the selected count
                                selectedCount = 0
                                logger.debug(
                                    "Deselect All pressed: \(selectedCount) apps selected",
                                    service: serviceName,
                                    category: .userInterface
                                )
                            }
                            .buttonStyle(.borderless)
                            .font(.system(size: SnapbackTheme.FontSize.caption))
                        }
                        .padding(.top, SnapbackTheme.Padding.small)
                    }

                }
                .padding(.horizontal, 20)
                .padding(.vertical, 10)
            }

            // Footer with buttons using SettingsFooter for consistency
            SettingsFooter {
                HStack(spacing: SnapbackTheme.Padding.standard) {
                    Spacer()

                    Button("Cancel") {
                        logger.debug("Cancel button pressed", service: serviceName)
                        onCancel()

                        // Fallback dismissal mechanism in case the callback fails
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                            if NSApp.keyWindow?.title.contains("Workspace") == true {
                                logger.debug("Fallback dismissal triggered", service: serviceName)
                                WindowDismissalManager.shared.dismissCurrentModal()
                            }
                        }
                    }
                    .keyboardShortcut(.cancelAction)
                    .padding(.vertical, SnapbackTheme.Padding.vertical)
                    .padding(.horizontal, SnapbackTheme.Padding.horizontal)

                    Button(isEditMode ? "Save Changes" : "Save") {
                        logger.debug("Save button pressed", service: serviceName)

                        // Validate before saving
                        if validateWorkspaceName() {
                            onSave()

                            // Fallback dismissal mechanism in case the callback fails
                            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                                if NSApp.keyWindow?.title.contains("Workspace") == true {
                                    logger.debug(
                                        "Fallback dismissal triggered after save",
                                        service: serviceName)
                                    WindowDismissalManager.shared.dismissCurrentModal()
                                }
                            }
                        }
                    }
                    .keyboardShortcut(.defaultAction)
                    .padding(.vertical, SnapbackTheme.Padding.vertical)
                    .padding(.horizontal, SnapbackTheme.Padding.horizontal)
                    .disabled(isSaveButtonDisabled)
                }
            }
        }
        .onAppear {
            // Set focus on the name field
            isNameFieldFocused = true
        }
        // Note: Removed conflict alert - following professional app approach
    }

    // MARK: - Computed Properties

    /// Determines if the save button should be disabled
    private var isSaveButtonDisabled: Bool {
        // Basic form validation
        let isFormInvalid =
            workspaceName.isEmpty
            || (!appItems.isEmpty && appItems.filter { $0.isSelected }.isEmpty)

        // For edit mode, allow saving even at limit (editing existing workspaces)
        if isEditMode {
            return isFormInvalid
        }

        // For new workspaces, check workspace limit
        let licenseManager = LicenseManager.shared
        if licenseManager.isLicenseSystemEnabled && !licenseManager.hasUnlimitedWorkspaces {
            let currentCount = workspaceService.workspaces.count
            let maxCount = LicenseManager.maxFreeWorkspaces
            let isAtLimit = currentCount >= maxCount

            // Disable save if form is invalid OR at workspace limit
            return isFormInvalid || isAtLimit
        }

        // If license system disabled or user has unlimited workspaces, only check form validation
        return isFormInvalid
    }

    // MARK: - Helper Methods

    /// Open external purchase URL for license upgrade
    private func openPurchaseURL() {
        // Use centralized website configuration for purchase URL
        WebsiteConfiguration.shared.openPurchasePage()
    }

    // MARK: - Freemium UI Components

    /// Shows workspace count and limits for free users
    @ViewBuilder
    private var workspaceCountIndicator: some View {
        let licenseManager = LicenseManager.shared

        if licenseManager.isLicenseSystemEnabled && !licenseManager.hasUnlimitedWorkspaces {
            let currentCount = workspaceService.workspaces.count
            let maxCount = LicenseManager.maxFreeWorkspaces
            let isAtLimit = currentCount >= maxCount

            HStack {
                Image.systemCompat(isAtLimit ? "Warning" : "Info")
                    .foregroundColor(isAtLimit ? .orange : .blue)

                VStack(alignment: .leading, spacing: SnapbackTheme.Padding.extraSmall) {
                    Text("Workspace Usage: \(currentCount) of \(maxCount)")
                        .font(
                            .system(
                                size: SnapbackTheme.FontSize.body,
                                weight: SnapbackTheme.FontWeight.heading)
                        )
                        .foregroundColor(isAtLimit ? .orange : SnapbackTheme.Text.primary)

                    if isAtLimit {
                        Text("You've reached the free limit. Upgrade for unlimited workspaces.")
                            .font(.system(size: SnapbackTheme.FontSize.caption))
                            .foregroundColor(SnapbackTheme.Text.secondary)
                    } else {
                        Text("Free users can save up to \(maxCount) workspaces.")
                            .font(.system(size: SnapbackTheme.FontSize.caption))
                            .foregroundColor(SnapbackTheme.Text.secondary)
                    }
                }

                Spacer()

                if isAtLimit {
                    Button("Upgrade") {
                        openPurchaseURL()
                    }
                    .buttonStyle(.borderedProminent)
                }
            }
            .padding(SnapbackTheme.Padding.standard)
            .background(
                RoundedRectangle(cornerRadius: SnapbackTheme.CornerRadius.card)
                    .fill(
                        isAtLimit ? SnapbackTheme.Background.warning : SnapbackTheme.Background.info
                    )
            )
            .overlay(
                RoundedRectangle(cornerRadius: SnapbackTheme.CornerRadius.card)
                    .strokeBorder(
                        isAtLimit ? SnapbackTheme.Border.warning : SnapbackTheme.Border.info,
                        lineWidth: SnapbackTheme.BorderWidth.normal
                    )
            )
        }
    }

    // MARK: - App Row Views

    // Row view for edit mode
    struct EditWorkspaceAppRow: View {
        @Binding var appItem: AppSelectionItem
        @Binding var selectedCount: Int
        var appItems: [AppSelectionItem]

        var body: some View {
            HStack(spacing: SnapbackTheme.Padding.standard) {
                Image(nsImage: appItem.appIcon)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 20, height: 20)

                appItem.displayNameView
                    .lineLimit(1)

                Spacer()

                Toggle(
                    "",
                    isOn: Binding(
                        get: { appItem.isSelected },
                        set: { newValue in
                            appItem.isSelected = newValue
                            selectedCount = appItems.filter { $0.isSelected }.count
                            print(
                                "Toggle changed for \(appItem.appName): \(newValue), Selected count: \(selectedCount)"
                            )
                        }
                    )
                )
                .labelsHidden()
                .toggleStyle(.switch)
            }
            .snapbackRowStyle()
            .contentShape(Rectangle())
            .onTapGesture {
                appItem.isSelected.toggle()
                selectedCount = appItems.filter { $0.isSelected }.count
                print(
                    "Row tapped for \(appItem.appName): \(appItem.isSelected), Selected count: \(selectedCount)"
                )
            }
        }
    }

    // Row view for save mode
    struct WorkspaceAppRow: View {
        @Binding var appItem: AppSelectionItem
        @Binding var selectedCount: Int
        var appItems: [AppSelectionItem]
        let logger: LoggingService
        let serviceName: String

        var body: some View {
            HStack(spacing: 12) {
                Image(nsImage: appItem.appIcon)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 20, height: 20)

                appItem.displayNameView
                    .lineLimit(1)

                Spacer()

                Toggle(
                    "",
                    isOn: Binding(
                        get: { appItem.isSelected },
                        set: { newValue in
                            appItem.isSelected = newValue
                            selectedCount = appItems.filter { $0.isSelected }.count

                            logger.debug(
                                "Toggle changed for \(appItem.appName): \(newValue), Selected count: \(selectedCount)",
                                service: serviceName,
                                category: .userInterface
                            )
                        }
                    )
                )
                .labelsHidden()
                .toggleStyle(.switch)
            }
            .snapbackRowStyle()
            .contentShape(Rectangle())
            .onTapGesture {
                appItem.isSelected.toggle()
                selectedCount = appItems.filter { $0.isSelected }.count
                logger.debug(
                    "Row tapped for \(appItem.appName): \(appItem.isSelected), Selected count: \(selectedCount)",
                    service: serviceName,
                    category: .userInterface
                )
            }
        }
    }

    // MARK: - App List Content

    // Update the app list content
    private var appListContent: some View {
        VStack(spacing: 0) {
            ForEach(appItems.indices, id: \.self) { index in
                if isEditMode {
                    EditWorkspaceAppRow(
                        appItem: $appItems[index],
                        selectedCount: $selectedCount,
                        appItems: appItems
                    )

                } else {
                    WorkspaceAppRow(
                        appItem: $appItems[index],
                        selectedCount: $selectedCount,
                        appItems: appItems,
                        logger: logger,
                        serviceName: serviceName
                    )

                }
            }
        }
    }
}

// MARK: - Helper for vertical arrangement detection
extension WorkspaceFormView {
    func isVerticalArrangement(screens: [NSScreen]) -> Bool {
        // Handle single screen case
        if screens.count <= 1 {
            return false
        }

        // Calculate the total bounds of all screens
        var minX: CGFloat = .infinity
        var maxX: CGFloat = -.infinity
        var minY: CGFloat = .infinity
        var maxY: CGFloat = -.infinity

        for screen in screens {
            minX = min(minX, screen.frame.minX)
            maxX = max(maxX, screen.frame.maxX)
            minY = min(minY, screen.frame.minY)
            maxY = max(maxY, screen.frame.maxY)
        }

        let totalWidth = maxX - minX
        let totalHeight = maxY - minY

        // Check if screens are stacked vertically
        var hasVerticalStacking = false

        // For macOS, we need to be careful about detecting vertical stacking
        // because the coordinate system has (0,0) at the bottom-left
        if screens.count == 2 {
            // Handle two screens case
            let screen1 = screens[0]
            let screen2 = screens[1]

            // Check if one screen is completely above the other
            hasVerticalStacking =
                (screen1.frame.minY >= screen2.frame.maxY)
                || (screen2.frame.minY >= screen1.frame.maxY)
        } else {
            // Handle more than two screens case
            // For more than 2 screens, check if any screen is positioned below another screen
            for i in 0..<screens.count {
                for j in (i + 1)..<screens.count {
                    let screen1 = screens[i]
                    let screen2 = screens[j]

                    // Check if one screen is below the other
                    if (screen1.frame.minY >= screen2.frame.maxY)
                        || (screen2.frame.minY >= screen1.frame.maxY)
                    {
                        hasVerticalStacking = true
                        break
                    }
                }
                if hasVerticalStacking { break }
            }
        }

        // Consider it vertical if either:
        // 1. The total height is significantly larger than width
        // 2. We detected screens stacked on top of each other
        return (totalHeight > totalWidth * 1.2) || hasVerticalStacking
    }
}

// MARK: - Preview
#Preview {
    struct PreviewWrapper: View {
        @State var workspaceName = "My Workspace"
        @State var customLayoutName = ""
        @State var appItems: [AppSelectionItem] = [
            AppSelectionItem(
                windowInfo: WindowInfo(
                    frame: CGRect(x: 100, y: 100, width: 800, height: 600),
                    monitorID: UUID(),
                    appBundleIdentifier: "com.apple.Safari",
                    isFullscreen: false
                )
            ),
            AppSelectionItem(
                windowInfo: WindowInfo(
                    frame: CGRect(x: 1500, y: 200, width: 700, height: 500),
                    monitorID: UUID(),
                    appBundleIdentifier: "com.apple.mail",
                    isFullscreen: false
                )
            ),
        ]
        @State var selectedCount = 2
        @State var shortcutDisplayString = "⌘S"
        @State var recordedKeyCode: UInt16? = UInt16(kVK_ANSI_S)
        @State var recordedModifiers: UInt? = NSEvent.ModifierFlags.command.rawValue
        @State var closeNonWorkspaceWindows = false

        var body: some View {
            WorkspaceFormView(
                workspaceName: $workspaceName,
                customLayoutName: $customLayoutName,
                appItems: $appItems,
                selectedCount: $selectedCount,
                shortcutDisplayString: $shortcutDisplayString,
                recordedKeyCode: $recordedKeyCode,
                recordedModifiers: $recordedModifiers,
                closeNonWorkspaceWindows: $closeNonWorkspaceWindows,
                onSave: { print("Save clicked") },
                onCancel: { print("Cancel clicked") },
                onUpdatePositions: { print("Update positions clicked") }
            )
            .environmentObject(WorkspaceService(snappingService: WindowSnappingService()))
            .frame(width: 600, height: 800)
        }
    }

    return PreviewWrapper()
}
