import SwiftUI

/// Backward compatibility helpers for macOS version differences
/// This file provides compatibility wrappers for SwiftUI APIs that changed between macOS versions
///
/// Future macOS Compatibility Strategy:
/// - Use hardcoded colors instead of system colors to ensure consistency across all versions
/// - Maintain explicit availability checks only for APIs that provide significant benefits
/// - When new macOS versions are released, evaluate if new APIs should replace current implementations
/// - Avoid blanket "macOS X.0 and later" checks that become outdated

// MARK: - OnChange Compatibility

/// A backward-compatible wrapper for .onChange that works across macOS versions
/// - macOS 14.0+: Uses the new two-parameter syntax with oldValue and newValue
/// - macOS 12.4-13.x: Uses the single-parameter syntax and tracks old value manually
struct OnChangeModifier<T: Equatable>: ViewModifier {
    let value: T
    let action: (T, T) -> Void
    @State private var previousValue: T?

    init(value: T, action: @escaping (T, T) -> Void) {
        self.value = value
        self.action = action
    }

    func body(content: Content) -> some View {
        if #available(macOS 14.0, *) {
            // Use the new two-parameter syntax on macOS 14+
            content.onChange(of: value) { oldValue, newValue in
                action(oldValue, newValue)
            }
        } else {
            // Use the single-parameter syntax on macOS 12.4-13.x
            // Track the previous value manually for backward compatibility
            content.onChange(of: value) { newValue in
                let oldValue = previousValue ?? newValue
                previousValue = newValue
                action(oldValue, newValue)
            }
            .onAppear {
                // Initialize the previous value on first appearance
                if previousValue == nil {
                    previousValue = value
                }
            }
        }
    }
}

/// A backward-compatible wrapper for .onChange that only needs the new value
/// This is for cases where the old value isn't actually used
struct OnChangeCompatModifier<T: Equatable>: ViewModifier {
    let value: T
    let action: (T) -> Void

    init(value: T, action: @escaping (T) -> Void) {
        self.value = value
        self.action = action
    }

    func body(content: Content) -> some View {
        if #available(macOS 14.0, *) {
            // Use the new two-parameter syntax on macOS 14+ but ignore oldValue
            content.onChange(of: value) { _, newValue in
                action(newValue)
            }
        } else {
            // Use the single-parameter syntax on macOS 12.4-13.x
            content.onChange(of: value) { newValue in
                action(newValue)
            }
        }
    }
}

// MARK: - View Extensions

extension View {
    /// Backward-compatible onChange modifier that provides both old and new values on supported systems
    /// - Parameters:
    ///   - value: The value to observe for changes
    ///   - action: The action to perform when the value changes, receives (oldValue, newValue)
    /// - Returns: A view with the onChange modifier applied
    func onChangeCompat<T: Equatable>(of value: T, perform action: @escaping (T, T) -> Void)
        -> some View
    {
        self.modifier(OnChangeModifier(value: value, action: action))
    }

    /// Backward-compatible onChange modifier that only provides the new value
    /// - Parameters:
    ///   - value: The value to observe for changes
    ///   - action: The action to perform when the value changes, receives newValue only
    /// - Returns: A view with the onChange modifier applied
    func onChangeCompat<T: Equatable>(of value: T, perform action: @escaping (T) -> Void)
        -> some View
    {
        self.modifier(OnChangeCompatModifier(value: value, action: action))
    }
}

// MARK: - State Tracking for Old Values

/// A property wrapper that tracks the previous value for backward compatibility
/// This can be used when you need access to both old and new values on older macOS versions
@propertyWrapper
struct TrackedState<T: Equatable> {
    private var _value: T
    private var _previousValue: T

    var wrappedValue: T {
        get { _value }
        set {
            _previousValue = _value
            _value = newValue
        }
    }

    var projectedValue: (current: T, previous: T) {
        return (current: _value, previous: _previousValue)
    }

    init(wrappedValue: T) {
        self._value = wrappedValue
        self._previousValue = wrappedValue
    }
}

// MARK: - Text Content Type Compatibility

/// A backward-compatible wrapper for .textContentType(.emailAddress)
/// - macOS 14.0+: Uses .textContentType(.emailAddress)
/// - macOS 12.4-13.x: No-op (textContentType is not available)
struct EmailTextContentTypeModifier: ViewModifier {
    func body(content: Content) -> some View {
        if #available(macOS 14.0, *) {
            content.textContentType(.emailAddress)
        } else {
            content
        }
    }
}

// MARK: - Icon Compatibility Extensions

extension Image {
    /// Creates a compatibility-aware SF Symbol image that works across all macOS versions
    /// - Parameter systemName: The SF Symbol name or custom icon name
    /// - Returns: An Image using native SF Symbols or custom icons from Assets
    static func systemCompat(_ systemName: String) -> Image {
        // List of custom icon names (without Custom prefix)
        let customIconNames = [
            "LeftHalf", "RightHalf", "TopHalf", "BottomHalf",
            "CenterThird", "LeftTwoThirds", "CenterTwoThirds", "RightTwoThirds",
            "Workspace", "FullWidth",
            "Settings", "Command", "Info", "Question", "Check", "CheckFilled", "Warning", "Window",
            "Key", "Power", "Keyboard", "Search",
        ]

        // Check if this is a custom icon first
        if customIconNames.contains(systemName) {
            // Try to load as custom image from Assets
            if let customImage = NSImage(named: systemName) {
                // Ensure template rendering for custom icons
                customImage.isTemplate = true
                return Image(nsImage: customImage)
            }
        }

        // Fall back to SF Symbol system
        return Image(systemName: systemName)
    }
}

extension NSImage {
    /// Creates a compatibility-aware SF Symbol NSImage that works across all macOS versions
    /// - Parameters:
    ///   - systemName: The SF Symbol name or custom icon name
    ///   - accessibilityDescription: Accessibility description
    /// - Returns: An NSImage using native SF Symbols or custom icons from Assets
    static func systemCompat(_ systemName: String, accessibilityDescription: String? = nil)
        -> NSImage?
    {
        // List of custom icon names (without Custom prefix)
        let customIconNames = [
            "LeftHalf", "RightHalf", "TopHalf", "BottomHalf",
            "CenterThird", "LeftTwoThirds", "CenterTwoThirds", "RightTwoThirds",
            "Workspace", "FullWidth",
            "Settings", "Command", "Info", "Question", "Check", "CheckFilled", "Warning", "Window",
            "Key", "Power", "Keyboard", "Search",
        ]

        // Check if this is a custom icon first
        if customIconNames.contains(systemName) {
            // Try to load as custom image from Assets
            if let customImage = NSImage(named: systemName) {
                // Ensure template rendering for custom icons
                customImage.isTemplate = true
                return customImage
            }
        }

        // Fall back to SF Symbol system
        if #available(macOS 11.0, *) {
            return NSImage(
                systemSymbolName: systemName, accessibilityDescription: accessibilityDescription)
        }

        // Fallback for older macOS versions (though we target 12.4+)
        return NSImage(named: systemName)
    }
}

// MARK: - ContentUnavailableView Compatibility

/// A unified ContentUnavailableView that works across all macOS versions
struct UniversalContentUnavailableView: View {
    let title: String
    let systemImage: String
    let description: Text?
    let dynamicDescription: (() -> Text)?

    init(_ title: String, systemImage: String, description: Text? = nil) {
        self.title = title
        self.systemImage = systemImage
        self.description = description
        self.dynamicDescription = nil
    }

    init(_ title: String, systemImage: String, dynamicDescription: @escaping () -> Text) {
        self.title = title
        self.systemImage = systemImage
        self.description = nil
        self.dynamicDescription = dynamicDescription
    }

    var body: some View {
        let currentDescription = dynamicDescription?() ?? description

        // Future-proof approach: Use native ContentUnavailableView when available,
        // but with consistent custom icon sizing across all versions
        if #available(macOS 14.0, *) {
            // Use native ContentUnavailableView on macOS 14+ for proper system integration
            // but override with our custom icon sizing for consistency
            ContentUnavailableView {
                VStack(spacing: SnapbackTheme.Padding.standard) {
                    Image.systemCompat(systemImage)
                        .resizable()  // ✅ KEY: Allow resizing for template icons
                        .aspectRatio(contentMode: .fit)  // ✅ KEY: Maintain aspect ratio
                        .frame(width: 48, height: 48)  // ✅ Balanced size for empty state visibility
                        .foregroundColor(SnapbackTheme.Text.secondary)
                        .font(.system(size: 48, weight: .bold))  // Bold weight for emphasis

                    Text(title)
                        .font(
                            .system(
                                size: SnapbackTheme.FontSize.heading,
                                weight: SnapbackTheme.FontWeight.heading)
                        )
                        .foregroundColor(SnapbackTheme.Text.primary)
                }
            } description: {
                currentDescription
            }
        } else {
            // Custom implementation for macOS 12.4-13.x with identical styling
            VStack(spacing: SnapbackTheme.Padding.extraLarge) {
                Image.systemCompat(systemImage)
                    .resizable()  // ✅ KEY: Allow resizing for template icons
                    .aspectRatio(contentMode: .fit)  // ✅ KEY: Maintain aspect ratio
                    .frame(width: 48, height: 48)  // ✅ Balanced size for empty state visibility
                    .foregroundColor(SnapbackTheme.Text.secondary)
                    .font(.system(size: 48, weight: .bold))  // Bold weight for emphasis

                Text(title)
                    .font(
                        .system(
                            size: SnapbackTheme.FontSize.heading,
                            weight: SnapbackTheme.FontWeight.heading)
                    )
                    .foregroundColor(SnapbackTheme.Text.primary)

                if let currentDescription = currentDescription {
                    currentDescription
                        .font(.system(size: SnapbackTheme.FontSize.body))
                        .foregroundColor(SnapbackTheme.Text.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, SnapbackTheme.Padding.standard)
                }
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            // .background(Color.clear)  // Ensure transparent background for proper container handling
        }
    }
}

// MARK: - Label Compatibility

/// A Label view that uses compatible icons
struct CompatLabel: View {
    let title: String
    let systemImage: String

    init(_ title: String, systemImage: String) {
        self.title = title
        self.systemImage = systemImage
    }

    var body: some View {
        Label {
            Text(title)
        } icon: {
            Image.systemCompat(systemImage)
        }
    }
}

// MARK: - Window Dismissal Compatibility

/// A backward-compatible window dismissal system that works across all macOS versions
/// This addresses the issue where @Environment(\.dismiss) doesn't work properly with NSWindow-hosted SwiftUI views on macOS Monterey
class WindowDismissalManager: ObservableObject {
    static let shared = WindowDismissalManager()
    private init() {}

    private let logger = LoggingService.shared
    private let serviceName = "WindowDismissalManager"

    /// Dismisses the current modal window using a robust method that works across all macOS versions
    /// - Parameters:
    ///   - windowTitle: Optional window title to help identify the specific window to close
    ///   - completion: Optional completion handler called after dismissal attempt
    func dismissCurrentModal(windowTitle: String? = nil, completion: (() -> Void)? = nil) {
        logger.debug(
            "Attempting to dismiss modal with title: \(windowTitle ?? "nil")", service: serviceName)

        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            var dismissed = false

            // Method 1: Try to close the key window first (most reliable)
            if let keyWindow = NSApp.keyWindow {
                self.logger.debug(
                    "Found key window: '\(keyWindow.title)'", service: self.serviceName)

                if self.isTargetWindow(keyWindow, expectedTitle: windowTitle) {
                    self.logger.debug("Closing key window", service: self.serviceName)
                    keyWindow.close()
                    dismissed = true
                }
            }

            // Method 2: Look for windows by exact title match
            if !dismissed, let title = windowTitle {
                for window in NSApp.windows {
                    if window.title == title {
                        self.logger.debug(
                            "Found window by exact title match: '\(title)'",
                            service: self.serviceName)
                        window.close()
                        dismissed = true
                        break
                    }
                }
            }

            // Method 3: Look for any workspace-related modal windows
            if !dismissed {
                let modalTitles = ["Save Workspace", "Edit Workspace", "Custom Layout"]
                for window in NSApp.windows {
                    if modalTitles.contains(window.title) {
                        self.logger.debug(
                            "Found modal window by title: '\(window.title)'",
                            service: self.serviceName)
                        window.close()
                        dismissed = true
                        break
                    }
                }
            }

            // Method 4: Look for windows with workspace-related content
            if !dismissed {
                let potentialModals = NSApp.windows.filter { window in
                    self.isWorkspaceRelatedWindow(window)
                }

                if let modalWindow = potentialModals.first {
                    self.logger.debug(
                        "Found potential modal window: '\(modalWindow.title)'",
                        service: self.serviceName)
                    modalWindow.close()
                    dismissed = true
                }
            }

            // Method 5: Emergency fallback - close any floating window
            if !dismissed {
                let floatingWindows = NSApp.windows.filter { $0.level == .floating }
                if let floatingWindow = floatingWindows.first {
                    self.logger.debug(
                        "Emergency fallback: closing floating window: '\(floatingWindow.title)'",
                        service: self.serviceName)
                    floatingWindow.close()
                    dismissed = true
                }
            }

            if dismissed {
                self.logger.debug("Successfully dismissed modal window", service: self.serviceName)
            } else {
                self.logger.error(
                    "Failed to dismiss modal window - no suitable window found",
                    service: self.serviceName)
            }

            // Call completion handler after a brief delay to ensure window closure completes
            if let completion = completion {
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    completion()
                }
            }
        }
    }

    /// Checks if a window is the target window we want to close
    private func isTargetWindow(_ window: NSWindow, expectedTitle: String?) -> Bool {
        // If we have an expected title, check for exact match first
        if let title = expectedTitle, window.title == title {
            return true
        }

        // Check for workspace-related windows
        return isWorkspaceRelatedWindow(window)
    }

    /// Checks if a window is workspace-related
    private func isWorkspaceRelatedWindow(_ window: NSWindow) -> Bool {
        let title = window.title.lowercased()
        return title.contains("workspace") || title.contains("edit") || title.contains("save")
    }
}

/// A view modifier that provides backward-compatible modal dismissal
struct CompatibleModalDismissal: ViewModifier {
    let windowTitle: String?
    let onDismiss: (() -> Void)?

    init(windowTitle: String? = nil, onDismiss: (() -> Void)? = nil) {
        self.windowTitle = windowTitle
        self.onDismiss = onDismiss
    }

    func body(content: Content) -> some View {
        content
            .environmentObject(WindowDismissalManager.shared)
    }
}

extension View {
    /// Adds backward-compatible modal dismissal support
    /// - Parameters:
    ///   - windowTitle: Optional window title to help identify the window
    ///   - onDismiss: Optional callback when the modal is dismissed
    /// - Returns: A view with compatible modal dismissal support
    func compatibleModalDismissal(windowTitle: String? = nil, onDismiss: (() -> Void)? = nil)
        -> some View
    {
        modifier(CompatibleModalDismissal(windowTitle: windowTitle, onDismiss: onDismiss))
    }
}
