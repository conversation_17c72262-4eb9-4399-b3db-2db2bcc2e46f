import AppKit
import Foundation
import SwiftUI

// Manages the presentation of secondary windows (<PERSON><PERSON><PERSON>, Workspace Manager, etc.)
class WindowManager: NSObject, ObservableObject, NSWindowDelegate {
    private var aboutWindow: NSWindow?

    // Logger
    private let logger = LoggingService.shared
    private let serviceName = "WindowManager"

    // Dependencies (Injected)
    private let workspaceService: WorkspaceService
    private weak var appDelegate: AppDelegate?

    // Window instances
    private var settingsWindow: NSWindow
    // Trial email collection removed - freemium model provides 3 free workspaces without trials

    private lazy var workspaceManagerWindow: NSWindow = createWindow(
        title: "Manage Workspaces",
        width: 500,
        height: 400,
        styleMask: [.titled, .closable, .miniaturizable, .resizable, .fullSizeContentView],
        view: WorkspaceManagerView()
            .environmentObject(workspaceService)
    )

    // Keep track of the save popup window controller to prevent multiple instances
    // Using strong reference to ensure it's not deallocated prematurely
    private var saveWorkspaceWindowController: NSWindowController?

    // Keep track of the edit workspace window controller to prevent multiple instances
    // Using strong reference to ensure it's not deallocated prematurely
    private var editWorkspaceWindowController: NSWindowController?

    init(appDelegate: AppDelegate, workspaceService: WorkspaceService) {
        self.appDelegate = appDelegate
        self.workspaceService = workspaceService

        // Create settings window
        let settingsView = SettingsView()
            .environmentObject(appDelegate)
            .environmentObject(workspaceService)

        self.settingsWindow = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 800, height: 500),
            styleMask: [.titled, .closable, .miniaturizable],
            backing: .buffered,
            defer: false
        )

        super.init()

        // Configure the window styling at creation time
        self.settingsWindow.titlebarAppearsTransparent = true
        self.settingsWindow.titleVisibility = .visible
        self.settingsWindow.backgroundColor = NSColor.windowBackgroundColor

        // Set content view after configuring the window
        self.settingsWindow.contentView = NSHostingView(rootView: settingsView)
        self.settingsWindow.title = "Settings"
        self.settingsWindow.isReleasedWhenClosed = false

        // Configure titlebar height after content view is set
        if let titlebarView = self.settingsWindow.standardWindowButton(.closeButton)?.superview?
            .superview
        {
            titlebarView.frame.size.height = 32
        }

        logger.info("Initialized", service: serviceName)
    }

    // MARK: - Window Creation Helper

    private func createWindow<V: View>(
        title: String, width: CGFloat, height: CGFloat,
        styleMask: NSWindow.StyleMask = [.titled, .closable, .miniaturizable], view: V
    ) -> NSWindow {
        let window = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: width, height: height),
            styleMask: styleMask,
            backing: .buffered,
            defer: false
        )
        window.center()
        // Important: Inject EnvironmentObjects into the view *before* putting it in NSHostingView
        // This assumes the view passed in already has its environment objects set if needed.
        window.contentView = NSHostingView(rootView: view)
        window.title = title
        window.isReleasedWhenClosed = false  // Keep window instance alive by default
        logger.debug("Created window: \(title)", service: serviceName)
        return window
    }

    // MARK: - Public Presentation Methods

    func openSettings() {
        logger.info("Opening Settings window", service: serviceName)

        // Configure the window BEFORE making it visible
        // First, ensure the window has the correct size
        settingsWindow.setContentSize(NSSize(width: 800, height: 500))

        // Apply styling before showing the window
        settingsWindow.titlebarAppearsTransparent = true
        settingsWindow.titleVisibility = .visible
        settingsWindow.styleMask.remove(.resizable)
        settingsWindow.backgroundColor = NSColor.windowBackgroundColor

        // Set the title to "Settings" initially (will be updated by the view)
        settingsWindow.title = "Settings"

        // Add extra height to the title bar to match Xcode
        if let titlebarView = settingsWindow.standardWindowButton(.closeButton)?.superview?
            .superview
        {
            titlebarView.frame.size.height = 32
        }

        // Initialize the window but keep it invisible
        // This allows the SwiftUI view to initialize without showing the window
        settingsWindow.alphaValue = 0.0

        // Make the window key and order front but keep it invisible
        settingsWindow.makeKeyAndOrderFront(nil)
        NSApp.activate(ignoringOtherApps: true)

        // Position the window precisely in the center of the screen
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.05) {
            if let screen = NSScreen.main {
                // Get screen dimensions
                let screenWidth = screen.frame.width
                let screenHeight = screen.frame.height

                // Get window dimensions
                let windowWidth = self.settingsWindow.frame.width
                let windowHeight = self.settingsWindow.frame.height

                // Calculate center position
                let centerX = (screenWidth - windowWidth) / 2
                let centerY = (screenHeight - windowHeight) / 2

                self.logger.debug(
                    "Screen dimensions: \(screenWidth) x \(screenHeight)",
                    service: self.serviceName
                )
                self.logger.debug(
                    "Window dimensions: \(windowWidth) x \(windowHeight)",
                    service: self.serviceName
                )
                self.logger.debug(
                    "Calculated center: (\(centerX), \(centerY))",
                    service: self.serviceName
                )

                // Set window position
                self.settingsWindow.setFrameOrigin(NSPoint(x: centerX, y: centerY))

                self.logger.debug(
                    "Window position after setting: (\(self.settingsWindow.frame.origin.x), \(self.settingsWindow.frame.origin.y))",
                    service: self.serviceName
                )

                // Now make the window visible with a smooth fade-in
                NSAnimationContext.runAnimationGroup { context in
                    context.duration = 0.15
                    self.settingsWindow.animator().alphaValue = 1.0
                }
            } else {
                // Fallback to standard centering if no main screen
                self.settingsWindow.center()
                self.logger.debug(
                    "Used standard centering (no main screen available)",
                    service: self.serviceName
                )

                // Make the window visible
                self.settingsWindow.alphaValue = 1.0
            }
        }
    }

    func openWorkspaceManager() {
        logger.info("Opening Workspace Manager window", service: serviceName)
        workspaceManagerWindow.makeKeyAndOrderFront(nil)
        NSApp.activate(ignoringOtherApps: true)
    }

    func showSaveWorkspacePopup(windowInfos: [WindowInfo]) {
        logger.info(
            "showSaveWorkspacePopup called with \(windowInfos.count) window infos",
            service: serviceName,
            category: .userInterface
        )

        // Check if already open
        if let existingController = saveWorkspaceWindowController,
            existingController.window?.isVisible == true
        {
            logger.debug("Save popup already open, bringing to front", service: serviceName)
            existingController.window?.makeKeyAndOrderFront(nil)
            NSApp.activate(ignoringOtherApps: true)
            return
        }
        logger.debug("Creating new save popup window", service: serviceName)

        // Create the view instance first
        let saveWorkspaceView = SaveWorkspaceView(windowInfos: windowInfos)
            .environmentObject(workspaceService)  // Inject service here
            .environmentObject(WindowDismissalManager.shared)  // Inject dismissal manager

        // Create the window
        let saveWorkspaceWindow = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 600, height: 700),  // Match dimensions in SaveWorkspaceView
            styleMask: [.titled, .closable, .miniaturizable, .resizable, .fullSizeContentView],
            backing: .buffered, defer: false
        )

        // Create hosting view and assign
        let hostingView = NSHostingView(rootView: saveWorkspaceView)
        saveWorkspaceWindow.contentView = hostingView

        // Set window properties
        saveWorkspaceWindow.title = "Save Workspace"  // Set window title explicitly
        saveWorkspaceWindow.isReleasedWhenClosed = false  // Don't release when closed to maintain reference
        saveWorkspaceWindow.level = .floating  // Ensure window appears above other apps

        // Set this class as the window delegate to handle window closing
        saveWorkspaceWindow.delegate = self

        // Use intelligent positioning instead of simple center()
        WindowPositioningService.shared.positionWindow(
            saveWorkspaceWindow,
            preferredSize: NSSize(width: 600, height: 700),
            context: .menuBar
        )

        // Show using a controller
        let windowController = NSWindowController(window: saveWorkspaceWindow)
        windowController.showWindow(nil)
        self.saveWorkspaceWindowController = windowController  // Keep strong ref

        logger.debug(
            "Save workspace window controller created with ID: \(ObjectIdentifier(windowController))",
            service: serviceName)

        saveWorkspaceWindow.makeKeyAndOrderFront(nil)
        NSApp.activate(ignoringOtherApps: true)

        logger.info(
            "Save workspace window should now be visible. Window visible: \(saveWorkspaceWindow.isVisible), Window key: \(saveWorkspaceWindow.isKeyWindow)",
            service: serviceName,
            category: .userInterface
        )
    }

    func showEditWorkspacePopup(workspace: Workspace) {
        logger.debug(
            "showEditWorkspacePopup called for workspace: \(workspace.name)", service: serviceName)

        // Check if already open
        if let existingController = editWorkspaceWindowController,
            existingController.window?.isVisible == true
        {
            logger.debug(
                "Edit workspace popup already open, bringing to front", service: serviceName)
            existingController.window?.makeKeyAndOrderFront(nil)
            NSApp.activate(ignoringOtherApps: true)
            return
        }
        logger.debug("Creating new edit workspace popup window", service: serviceName)

        // Create the view instance first
        let editWorkspaceView = EditWorkspaceView(workspace: workspace)
            .environmentObject(workspaceService)  // Inject service here
            .environmentObject(WindowDismissalManager.shared)  // Inject dismissal manager

        // Create the window
        let editWorkspaceWindow = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 600, height: 700),  // Match dimensions in EditWorkspaceView
            styleMask: [.titled, .closable, .miniaturizable, .resizable, .fullSizeContentView],
            backing: .buffered, defer: false
        )

        logger.debug("Created edit workspace window", service: serviceName)

        // Create hosting view and assign
        let hostingView = NSHostingView(rootView: editWorkspaceView)
        editWorkspaceWindow.contentView = hostingView

        // Set window properties
        editWorkspaceWindow.title = "Edit Workspace"  // Set window title explicitly
        editWorkspaceWindow.isReleasedWhenClosed = false  // Don't release when closed to maintain reference
        editWorkspaceWindow.level = .floating  // Ensure window appears above other apps

        // Set this class as the window delegate to handle window closing
        editWorkspaceWindow.delegate = self

        // Use intelligent positioning instead of simple center()
        WindowPositioningService.shared.positionWindow(
            editWorkspaceWindow,
            preferredSize: NSSize(width: 600, height: 700),
            context: .menuBar
        )

        // Show using a controller
        let windowController = NSWindowController(window: editWorkspaceWindow)
        windowController.showWindow(nil)
        self.editWorkspaceWindowController = windowController  // Keep strong ref

        logger.debug(
            "Edit workspace window controller created with ID: \(ObjectIdentifier(windowController))",
            service: serviceName)

        editWorkspaceWindow.makeKeyAndOrderFront(nil)
        NSApp.activate(ignoringOtherApps: true)

        logger.debug("Window should now be visible", service: serviceName)
    }

    func openAbout() {
        if let existingWindow = aboutWindow {
            existingWindow.makeKeyAndOrderFront(nil)
            NSApp.activate(ignoringOtherApps: true)
            return
        }

        let window = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 280, height: 400),
            styleMask: [.titled, .closable],  // Only closable, remove other style masks
            backing: .buffered,
            defer: false
        )

        window.title = "About Snapback"
        window.center()
        window.contentView = NSHostingView(rootView: AboutView())
        window.isReleasedWhenClosed = false
        window.makeKeyAndOrderFront(nil)
        NSApp.activate(ignoringOtherApps: true)

        aboutWindow = window
    }

    // MARK: - NSWindowDelegate

    func windowWillClose(_ notification: Notification) {
        guard let closedWindow = notification.object as? NSWindow else { return }

        // Check if the closed window is the save workspace window
        if let saveController = saveWorkspaceWindowController,
            saveController.window === closedWindow
        {
            logger.debug(
                "Save workspace window is closing, clearing reference", service: serviceName)
            saveWorkspaceWindowController = nil
        }
        // Check if the closed window is the edit workspace window
        else if let editController = editWorkspaceWindowController,
            editController.window === closedWindow
        {
            logger.debug(
                "Edit workspace window is closing, clearing reference", service: serviceName)
            editWorkspaceWindowController = nil
        }
    }
}
