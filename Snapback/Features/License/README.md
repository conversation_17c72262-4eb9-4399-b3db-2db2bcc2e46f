# License Settings Module

This module provides comprehensive license management functionality for the Snapback macOS app, following the established UI design patterns and architecture.

## Overview

The License settings module consists of:

- **LicenseManager**: Core service for license validation and storage
- **LicenseSettingsView**: SwiftUI interface following Snapback's design system
- **Integration**: Seamless integration with the existing settings architecture

## Features

### 🔑 License Management

- License key input with auto-formatting (XXXX-XXXX-XXXX-XXXX format)
- Real-time validation with loading states
- Persistent storage using UserDefaults
- Error handling with user-friendly messages
- External purchase redirection for license acquisition

### 📊 License Status Display

- Visual status indicators with appropriate colors
- Detailed license information (type, user, expiration)
- Feature list for valid licenses
- Expiration warnings for time-limited licenses

### 🎨 UI Design

- Follows SnapbackTheme design system
- Consistent with General tab styling
- Uses established form patterns and spacing
- Includes appropriate SF Symbols icons

### 🔧 Integration

- Seamlessly integrated into existing settings tabs
- Uses SettingsFooter for consistent footer styling
- Follows established logging patterns
- Compatible with existing UserDefaults structure

## License Key Formats

### License Key Format

License keys use a standard format:

- Format: Standard 24-character alphanumeric format (same as regular licenses)
- Example: `ABCD-EFGH-JKMN-PQRS-TUVW-XYZ2`
- Duration: 14 days from activation
- Features: Full access to all Snapback features
- Distinguished by `licenseType: "trial"` in API responses, not by key format

### Regular License Keys

Regular license keys use the standard format:

- Format: `XXXX-XXXX-XXXX-XXXX` (4 groups of 4 characters)
- Example: `SNAP-BACK-2025-LIFE`
- Auto-formatted in UI with dashes for readability

## License Key Format

License keys are validated through the API and must be obtained through legitimate channels. The system supports both formatted and unformatted key input:

**Note**: Keys can be entered with or without dashes. The system automatically formats them for display and cleans them for validation.

## Architecture

### LicenseManager

```swift
class LicenseManager: ObservableObject {
    // Published properties for UI binding
    @Published var licenseStatus: LicenseStatus
    @Published var licenseKey: String
    @Published var userEmail: String
    @Published var licenseInfo: LicenseInfo?

    // Core methods
    func validateLicense(silent: Bool = false) async
    func setLicenseKey(_ key: String) async
    func setLicenseKeyAndEmail(_ key: String, email: String) async
    func clearLicense()

    // Computed properties
    var hasFullAccess: Bool // true for valid pro licenses only
}
```

### LicenseStatus

```swift
enum LicenseStatus: String, CaseIterable {
    case unlicensed = "unlicensed"
    case valid = "valid"
    case invalid = "invalid"
    case expired = "expired"
}
```

### LicenseInfo

```swift
struct LicenseInfo: Codable {
    let licenseType: String
    let registeredUser: String
    let email: String?
    let expirationDate: Date?
    let features: [String]
}
```

### LicenseAPIService

The API service handles license validation:

```swift
class LicenseAPIService {
    // License validation (purchase redirection handled via direct URL)
    func validateLicense(licenseKey: String, email: String) async throws -> ValidationResponse
}
```

**API Behavior:**

- Validation supports both formatted and unformatted license keys
- Automatic retry logic with exponential backoff for network errors

## Usage

### Adding to Settings

The License tab is automatically included in the settings interface:

```swift
// In SettingsView.swift
tabList.append(Tab(id: "license", title: "License", icon: "key"))

// In content switch
case "license":
    LicenseSettingsView()
```

### Accessing License Manager

```swift
// Get shared instance
let licenseManager = LicenseManager.shared

// Check license status
if licenseManager.hasFullAccess {
    // User has full access (valid license or active trial)
}

// Set license key with email (recommended)
await licenseManager.setLicenseKeyAndEmail("YOUR-LICENSE-KEY", email: "<EMAIL>")

// Set license key only (legacy support)
await licenseManager.setLicenseKey("YOUR-LICENSE-KEY")

// Check license status
if licenseManager.hasFullAccess {
    print("Pro license active")
}

// Clear license
licenseManager.clearLicense()
```

## Styling Consistency

The module follows Snapback's established design patterns:

### Theme Usage

- `SnapbackTheme.Background.card` for GroupBox backgrounds
- `SnapbackTheme.Text.error` for error messages
- `SnapbackTheme.Padding.section` for consistent spacing
- `snapbackSectionTitleStyle()` for section headers
- `snapbackRowStyle()` for form rows

### Icons

- `key` for the License tab icon
- `checkmark.circle.fill` for valid status
- `xmark.circle.fill` for invalid status
- `clock.badge.exclamationmark` for expired status
- `exclamationmark.triangle` for unlicensed status

### Layout

- Follows the same ScrollView + VStack pattern as General settings
- Uses GroupBox for section containers
- Implements SettingsFooter for consistent footer styling
- Maintains proper spacing and visual hierarchy

## Logging

The module integrates with Snapback's logging system:

```swift
private let logger = LoggingService.shared
private let serviceName = "LicenseManager"

// Example logging
logger.info("License validation successful", service: serviceName)
logger.warning("License validation failed: invalid key", service: serviceName)
logger.error("License validation error: \(error)", service: serviceName)
```

## Persistence

License information is stored using UserDefaults with consistent key naming:

### License Data

- `SnapbackLicenseKey`: The license key (formatted for display)
- `SnapbackUserEmail`: User's email address
- `SnapbackLicenseStatus`: Current license status
- `SnapbackLicenseInfo`: Encoded license information
- `SnapbackLastLicenseValidation`: Timestamp of last validation

### Legacy Data (Removed)

Trial-related UserDefaults keys have been removed in the simplified license system.

## Future Enhancements

Potential improvements for production use:

1. **Real API Integration**: Replace placeholder validation with actual license server
2. **Offline Validation**: Add cryptographic license validation for offline use
3. **Auto-Renewal**: Implement automatic license renewal for subscription licenses
4. **License Transfer**: Add functionality to transfer licenses between devices
5. **Usage Analytics**: Track feature usage based on license type
6. **Grace Period**: Implement grace period for expired licenses

## Testing

### License Key Testing

To test the license functionality:

1. Open Snapback Settings
2. Navigate to the License tab
3. Enter a valid license key obtained through legitimate channels
4. Observe the validation process and status updates
5. Test different license types and statuses
6. Verify persistence by restarting the app

### Trial System Testing

To test the trial functionality:

1. Start with an unlicensed state (clear any existing license)
2. Test freemium model: Create up to 3 workspaces without license restrictions
3. Test workspace limit: Attempt to create a 4th workspace and verify upgrade prompt
4. Test license key entry for unlimited workspaces
5. Test license validation and persistence
6. Test clearing license data with "Clear License"
7. Test existing trial license validation (for users who already have trials)

### UI Testing Checklist

- [ ] Cancel button closes modal immediately
- [ ] Success message shows for 2.5 seconds before auto-close
- [ ] License keys auto-format with dashes
- [ ] License settings UI updates reactively
- [ ] Clear license removes all data

The module provides comprehensive license management while maintaining consistency with Snapback's existing design and architecture patterns.
