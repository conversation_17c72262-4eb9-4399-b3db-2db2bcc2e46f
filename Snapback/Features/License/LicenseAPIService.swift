import AppKit
import CryptoKit
import Foundation

/// API service for simplified license operations including validation and device management
/// Implements the simplified Snapback License Management API with external purchase redirection
class LicenseAPIService {
    static let shared = LicenseAPIService()

    private let configuration = LicenseAPIConfiguration.shared
    private let networkManager: LicenseAPINetworkManager

    /// Logger for API-related events
    private let logger = LoggingService.shared
    private let serviceName = "LicenseAPIService"

    private init() {
        // Always use real network manager - mock API removed for production
        self.networkManager = LicenseAPINetworkManager.shared
        logger.info(
            "🚀 REAL_API: LicenseAPIService initialized with production network manager",
            service: serviceName)
    }

    // MARK: - Configuration Properties

    /// Current API base URL (delegates to configuration)
    var baseURL: String {
        return configuration.baseURL
    }

    /// Current environment
    var currentEnvironment: LicenseAPIConfiguration.Environment {
        return configuration.currentEnvironment
    }

    // MARK: - Environment Management

    /// Override the current environment (useful for testing)
    func setEnvironmentOverride(_ environment: LicenseAPIConfiguration.Environment?) {
        configuration.setEnvironmentOverride(environment)
        // Note: This requires app restart to take effect due to network manager initialization
    }

    /// Get current configuration summary for debugging
    func getConfigurationSummary() -> [String: Any] {
        return configuration.getConfigurationSummary()
    }

    // MARK: - API Endpoints

    /// API endpoints enum to avoid hardcoded strings and ensure consistency
    private enum APIEndpoint {
        // License management endpoints
        case validateLicense
        case licenseStatus(String)  // licenseKey
        case resendLicense

        // Device management endpoints
        case removeDevice(String)  // deviceId
        case updateDeviceMetadata

        var path: String {
            switch self {
            // License management endpoints
            case .validateLicense:
                return "/licenses/validate"
            case .licenseStatus(let licenseKey):
                return "/licenses/status/\(licenseKey)"
            case .resendLicense:
                return "/licenses/resend"

            // Device management endpoints
            case .removeDevice(let deviceId):
                return "/licenses/devices/\(deviceId)"
            case .updateDeviceMetadata:
                return "/licenses/devices/metadata"
            }
        }

        /// Authentication requirement based on API documentation
        var requiresAuth: Bool {
            switch self {
            // Public endpoints
            case .validateLicense, .licenseStatus, .resendLicense:
                return false

            // Device token required endpoints
            case .removeDevice, .updateDeviceMetadata:
                return true
            }
        }

        /// HTTP method based on API documentation
        var method: HTTPMethod {
            switch self {
            case .licenseStatus:
                return .GET
            case .validateLicense, .resendLicense:
                return .POST
            case .updateDeviceMetadata:
                return .PUT
            case .removeDevice:
                return .DELETE
            }
        }
    }

    // MARK: - API Models

    // MARK: License Request Models
    struct LicenseValidationRequest: Codable {
        let licenseKey: String
        let deviceId: String
        let appVersion: String
        let deviceMetadata: DeviceMetadata?
    }

    struct LicenseCreationRequest: Codable {
        let email: String
        let licenseType: String
        let deviceId: String?
        let deviceMetadata: DeviceMetadata?
    }

    struct DeviceMetadata: Codable {
        let deviceName: String?
        let deviceType: String?
        let deviceModel: String?
        let operatingSystem: String?
        let architecture: String?
        let screenResolution: String?
        let totalMemory: String?
        let userNickname: String?
        let location: String?
        let notes: String?
    }

    struct ResendLicenseRequest: Codable {
        let email: String
    }

    // MARK: Purchase Redirection Models - REMOVED
    // Purchase redirection now handled via direct URL redirect to marketing website

    // MARK: Legacy Models (for backward compatibility)
    // Trial creation models removed - simplified license system with external purchase flow

    /// Request model for license validation (legacy)
    struct ValidationRequest: Codable {
        let licenseKey: String
        let email: String
        let deviceId: String
    }

    /// Response model for license validation (legacy)
    struct ValidationResponse: Codable {
        let valid: Bool
        let licenseType: String?
        let registeredUser: String?
        let email: String?
        let expirationDate: String?  // ISO 8601 format
        let features: [String]?
        let message: String?
        let error: String?
    }

    // Legacy pricing response - simplified for pro-only model
    struct PricingResponse: Codable {
        let pro: PricingTier

        struct PricingTier: Codable {
            let price: Int  // Price in cents
            let maxDevices: Int
            let duration: String
        }
    }

    // MARK: License Response Models

    /// New nested license object structure
    struct LicenseObject: Codable {
        let id: String
        let licenseKey: String
        let email: String
        let licenseType: String
        let maxDevices: Int
        let expiresAt: Date?
        let createdAt: Date
        let updatedAt: Date
        let stripePaymentIntentId: String?
        let devicesUsed: Int
        let devices: [DeviceInfo]
    }

    // Trial information structure removed - no longer needed in simplified license system

    /// Updated license validation response with nested structure and backward compatibility
    struct LicenseValidationResponse: Codable {
        let valid: Bool
        let message: String?

        // New nested structure
        let license: LicenseObject?

        // Legacy fields for backward compatibility
        let deviceToken: String?
        let licenseKey: String?
        let licenseType: String?
        let expiresAt: Date?
        let devicesUsed: Int?
        let maxDevices: Int?
        // Trial-related fields removed - no longer needed in simplified license system

        // Computed properties for easy access with backward compatibility
        var effectiveLicenseKey: String? {
            return license?.licenseKey ?? licenseKey
        }

        var effectiveLicenseType: String? {
            return license?.licenseType ?? licenseType
        }

        var effectiveExpiresAt: Date? {
            return license?.expiresAt ?? expiresAt
        }

        var effectiveDevicesUsed: Int? {
            return license?.devicesUsed ?? devicesUsed
        }

        var effectiveMaxDevices: Int? {
            return license?.maxDevices ?? maxDevices
        }

        var effectiveEmail: String? {
            return license?.email
        }

        var effectiveLicenseId: String? {
            return license?.id
        }

        var effectiveCreatedAt: Date? {
            return license?.createdAt
        }

        var effectiveUpdatedAt: Date? {
            return license?.updatedAt
        }

        // Trial-related computed properties removed - no longer needed
    }

    struct LicenseCreationResponse: Codable {
        let message: String?

        // New nested structure
        let license: LicenseObject?

        // Legacy fields for backward compatibility
        let licenseKey: String?
        let licenseType: String?
        let email: String?
        let maxDevices: Int?
        let expiresAt: Date?
        let createdAt: Date?
        // Trial-related fields removed - no longer needed in simplified license system

        // Computed properties for easy access with backward compatibility
        var effectiveLicenseKey: String? {
            return license?.licenseKey ?? licenseKey
        }

        var effectiveLicenseType: String? {
            return license?.licenseType ?? licenseType
        }

        var effectiveEmail: String? {
            return license?.email ?? email
        }

        var effectiveMaxDevices: Int? {
            return license?.maxDevices ?? maxDevices
        }

        var effectiveExpiresAt: Date? {
            return license?.expiresAt ?? expiresAt
        }

        var effectiveCreatedAt: Date? {
            return license?.createdAt ?? createdAt
        }

        var effectiveLicenseId: String? {
            return license?.id
        }

        var effectiveUpdatedAt: Date? {
            return license?.updatedAt
        }

        // Trial-related computed properties removed - no longer needed
    }

    struct LicenseStatusResponse: Codable {
        let licenseKey: String
        let licenseType: String
        let email: String
        let createdAt: Date
        let expiresAt: Date?
        let maxDevices: Int
        let devicesUsed: Int
        let isExpired: Bool
        let isActive: Bool
        let devices: [DeviceInfo]
        // Trial-related fields removed - no longer needed in simplified license system
    }

    struct DeviceInfo: Codable {
        let id: String
        let firstSeen: Date
        let lastSeen: Date
        let appVersion: String?  // Made optional to match new API structure
        let isActive: Bool

        // Enhanced device metadata for better user experience
        let deviceName: String?
        let deviceType: String?
        let deviceModel: String?
        let operatingSystem: String?
        let architecture: String?
        let screenResolution: String?
        let totalMemory: String?
        let userNickname: String?
        let location: String?
        let notes: String?
    }

    struct LicenseInfo: Codable {
        let licenseKey: String
        let licenseType: String
        let maxDevices: Int
        let expiresAt: Date?
        let email: String
    }

    struct ErrorResponse: Codable {
        let error: String
        let code: String?
        let details: String?
        let retryAfter: Int?
    }

    struct EmptyResponse: Codable {
        // Empty response for endpoints that return no data
    }

    struct UpdateDeviceMetadataRequest: Codable {
        let deviceId: String
        let deviceMetadata: DeviceMetadata
    }

    struct UpdateDeviceMetadataResponse: Codable {
        let message: String
        let device: DeviceInfo
    }

    // MARK: - API Methods

    // MARK: Purchase Redirection Endpoints - REMOVED
    // Purchase redirection now handled via direct URL redirect to marketing website

    // MARK: - Date Decoding Helper

    /// Custom date decoding strategy that handles both ISO8601 with and without fractional seconds
    private func createFlexibleDateDecodingStrategy() -> JSONDecoder.DateDecodingStrategy {
        return .custom { decoder in
            let container = try decoder.singleValueContainer()
            let dateString = try container.decode(String.self)

            // Try ISO8601 with fractional seconds first
            let iso8601WithFractionalSeconds = ISO8601DateFormatter()
            iso8601WithFractionalSeconds.formatOptions = [
                .withInternetDateTime, .withFractionalSeconds,
            ]
            if let date = iso8601WithFractionalSeconds.date(from: dateString) {
                self.logger.debug(
                    "DATE_PARSING: Successfully parsed date with fractional seconds: \(dateString) -> \(date)",
                    service: self.serviceName)
                return date
            }

            // Fallback to standard ISO8601 without fractional seconds
            let iso8601Standard = ISO8601DateFormatter()
            iso8601Standard.formatOptions = [.withInternetDateTime]
            if let date = iso8601Standard.date(from: dateString) {
                self.logger.debug(
                    "DATE_PARSING: Successfully parsed date without fractional seconds: \(dateString) -> \(date)",
                    service: self.serviceName)
                return date
            }

            self.logger.error(
                "DATE_PARSING: Failed to parse date string: \(dateString)",
                service: self.serviceName)
            throw DecodingError.dataCorruptedError(
                in: container, debugDescription: "Invalid date format: \(dateString)")
        }
    }

    // MARK: License Endpoints

    // Trial license creation removed - simplified license system with external purchase flow

    /// Validate license and optionally register device (new API format)
    func validateLicenseAndRegisterDevice(
        licenseKey: String, deviceId: String? = nil, appVersion: String? = nil,
        includeDeviceMetadata: Bool = true
    ) async throws -> LicenseValidationResponse {
        let metadataStatus = includeDeviceMetadata ? "with metadata" : "without metadata"
        logger.info(
            "🚀 REAL_API: validateLicenseAndRegisterDevice called - key: \(licenseKey.prefix(8))... (\(metadataStatus))",
            service: serviceName)

        let request = LicenseValidationRequest(
            licenseKey: licenseKey,
            deviceId: deviceId ?? getDeviceIdentifier(),
            appVersion: appVersion ?? getAppVersion(),
            deviceMetadata: includeDeviceMetadata ? generateDeviceMetadata() : nil
        )

        // Use network manager with endpoint enum for consistency
        let endpoint = APIEndpoint.validateLicense
        let response: LicenseValidationResponse = try await networkManager.post(
            endpoint: endpoint.path,
            body: request,
            requiresAuth: endpoint.requiresAuth
        )

        // Store device token if provided
        if let deviceToken = response.deviceToken {
            configuration.storeDeviceToken(deviceToken)
            logger.info(
                "LICENSE_API: Device token stored from network manager response",
                service: serviceName)
        }

        return response
    }

    /// Get license status
    func getLicenseStatus(licenseKey: String) async throws -> LicenseStatusResponse {
        let startTime = Date()
        logger.info(
            "🚀 REAL_API: getLicenseStatus called - key: \(licenseKey.prefix(8))... at \(startTime)",
            service: serviceName)

        // Use network manager with endpoint enum for consistency
        let endpoint = APIEndpoint.licenseStatus(licenseKey)
        let result: LicenseStatusResponse = try await networkManager.get(
            endpoint: endpoint.path,
            requiresAuth: endpoint.requiresAuth
        )

        let endTime = Date()
        let duration = endTime.timeIntervalSince(startTime)
        logger.info(
            "🚀 REAL_API: getLicenseStatus completed in \(String(format: "%.3f", duration))s",
            service: serviceName)

        return result
    }

    /// Resend license email
    func resendLicenseEmail(email: String) async throws -> Bool {
        let request = ResendLicenseRequest(email: email)

        // Use network manager with endpoint enum for consistency
        let endpoint = APIEndpoint.resendLicense
        let _: EmptyResponse = try await networkManager.post(
            endpoint: endpoint.path,
            body: request,
            requiresAuth: endpoint.requiresAuth
        )

        return true
    }

    /// Remove device from license
    func removeDevice(deviceId: String) async throws -> Bool {
        logger.info(
            "🚀 REAL_API: removeDevice called - deviceId: \(deviceId.prefix(8))...",
            service: serviceName)

        // Use network manager with endpoint enum for consistency
        let endpoint = APIEndpoint.removeDevice(deviceId)
        let _: EmptyResponse = try await networkManager.delete(
            endpoint: endpoint.path,
            requiresAuth: endpoint.requiresAuth
        )

        return true
    }

    // MARK: License upgrade functionality removed
    // License upgrades are no longer supported in the simplified license system
    // Users must purchase separate pro licenses for additional devices

    /// Update device metadata for better user experience and device management
    func updateDeviceMetadata(deviceId: String? = nil) async throws -> UpdateDeviceMetadataResponse
    {
        let startTime = Date()
        logger.info(
            "🚀 REAL_API: updateDeviceMetadata called - deviceId: \(deviceId?.prefix(8) ?? getDeviceIdentifier().prefix(8))... at \(startTime)",
            service: serviceName)

        let request = UpdateDeviceMetadataRequest(
            deviceId: deviceId ?? getDeviceIdentifier(),
            deviceMetadata: generateDeviceMetadata()
        )

        // Use network manager with endpoint enum for consistency
        let endpoint = APIEndpoint.updateDeviceMetadata
        let response: UpdateDeviceMetadataResponse = try await networkManager.put(
            endpoint: endpoint.path,
            body: request,
            requiresAuth: endpoint.requiresAuth
        )

        let endTime = Date()
        let duration = endTime.timeIntervalSince(startTime)
        logger.info(
            "🚀 REAL_API: updateDeviceMetadata completed in \(String(format: "%.3f", duration))s - deviceType: \(response.device.deviceType ?? "nil"), OS: \(response.device.operatingSystem ?? "nil"), memory: \(response.device.totalMemory ?? "nil")",
            service: serviceName)

        return response
    }

    // MARK: Legacy Methods (for backward compatibility)
    // Trial creation methods removed - simplified license system with external purchase flow

    /// Validate a license key with email (legacy)
    func validateLicense(licenseKey: String, email: String) async throws -> ValidationResponse {
        let deviceId = getDeviceIdentifier()

        let request = ValidationRequest(
            licenseKey: licenseKey,
            email: email,
            deviceId: deviceId
        )

        // Always use real API - mock API disabled for production use
        return try await performValidationRequest(request)
    }

    // MARK: - Production Implementation

    // Trial request implementation removed - simplified license system with external purchase flow

    /// Perform actual validation request to production API
    private func performValidationRequest(_ request: ValidationRequest) async throws
        -> ValidationResponse
    {
        let url = URL(string: "\(baseURL)/licenses/validate")!

        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
        urlRequest.httpBody = try JSONEncoder().encode(request)

        let (data, response) = try await URLSession.shared.data(for: urlRequest)

        guard let httpResponse = response as? HTTPURLResponse,
            httpResponse.statusCode == 200
        else {
            throw APIError.networkError("Failed to validate license")
        }

        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = createFlexibleDateDecodingStrategy()
        return try decoder.decode(ValidationResponse.self, from: data)
    }

    // MARK: Production API Methods

    /// Perform actual create license request to production API
    private func performCreateLicense(_ request: LicenseCreationRequest) async throws
        -> LicenseCreationResponse
    {
        let url = URL(string: "\(baseURL)/licenses/create")!

        logger.info(
            "🚀 REAL_API: performCreateLicense starting - email: \(request.email), type: \(request.licenseType), deviceId: \(request.deviceId ?? "nil")",
            service: serviceName)
        logger.info("🚀 REAL_API: Making request to URL: \(url)", service: serviceName)

        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")

        do {
            urlRequest.httpBody = try JSONEncoder().encode(request)
        } catch {
            logger.error(
                "🚀 REAL_API: performCreateLicense failed to encode request: \(error)",
                service: serviceName)
            throw APIError.networkError("Failed to encode license creation request")
        }

        let (data, response) = try await URLSession.shared.data(for: urlRequest)

        // Log raw response for debugging
        if let rawResponse = String(data: data, encoding: .utf8) {
            logger.info(
                "🚀 REAL_API: performCreateLicense raw response: \(rawResponse)",
                service: serviceName)
        } else {
            logger.warning(
                "🚀 REAL_API: performCreateLicense unable to decode raw response as UTF-8 string",
                service: serviceName)
        }

        guard let httpResponse = response as? HTTPURLResponse else {
            logger.error(
                "🚀 REAL_API: performCreateLicense invalid HTTP response type", service: serviceName)
            throw APIError.networkError("Invalid HTTP response")
        }

        logger.info(
            "🚀 REAL_API: performCreateLicense HTTP status code: \(httpResponse.statusCode)",
            service: serviceName)

        guard httpResponse.statusCode == 201 else {
            logger.warning(
                "🚀 REAL_API: performCreateLicense unexpected status code \(httpResponse.statusCode), expected 201",
                service: serviceName)
            throw APIError.networkError(
                "Failed to create license - HTTP \(httpResponse.statusCode)")
        }

        do {
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = createFlexibleDateDecodingStrategy()
            let parsedResponse = try decoder.decode(LicenseCreationResponse.self, from: data)

            logger.info(
                "🚀 REAL_API: performCreateLicense success - licenseKey: \(parsedResponse.effectiveLicenseKey?.prefix(8) ?? "nil")..., type: \(parsedResponse.effectiveLicenseType ?? "nil")",
                service: serviceName)

            return parsedResponse
        } catch {
            logger.error(
                "🚀 REAL_API: performCreateLicense failed to parse JSON response: \(error)",
                service: serviceName)
            throw APIError.networkError(
                "Failed to parse license creation response: \(error.localizedDescription)")
        }
    }

    /// Perform actual validate license and register device request to production API
    private func performValidateLicenseAndRegisterDevice(_ request: LicenseValidationRequest)
        async throws -> LicenseValidationResponse
    {
        let url = URL(string: "\(baseURL)/licenses/validate")!

        logger.info(
            "🚀 REAL_API: performValidateLicenseAndRegisterDevice starting - key: \(request.licenseKey.prefix(8))..., deviceId: \(request.deviceId)",
            service: serviceName)
        logger.info("🚀 REAL_API: Making request to URL: \(url)", service: serviceName)

        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")

        do {
            urlRequest.httpBody = try JSONEncoder().encode(request)
        } catch {
            logger.error(
                "🚀 REAL_API: performValidateLicenseAndRegisterDevice failed to encode request: \(error)",
                service: serviceName)
            throw APIError.networkError("Failed to encode license validation request")
        }

        let (data, response) = try await URLSession.shared.data(for: urlRequest)

        // Log raw response for debugging
        if let rawResponse = String(data: data, encoding: .utf8) {
            logger.info(
                "🚀 REAL_API: performValidateLicenseAndRegisterDevice raw response: \(rawResponse)",
                service: serviceName)
        } else {
            logger.warning(
                "🚀 REAL_API: performValidateLicenseAndRegisterDevice unable to decode raw response as UTF-8 string",
                service: serviceName)
        }

        guard let httpResponse = response as? HTTPURLResponse else {
            logger.error(
                "🚀 REAL_API: performValidateLicenseAndRegisterDevice invalid HTTP response type",
                service: serviceName)
            throw APIError.networkError("Invalid HTTP response")
        }

        logger.info(
            "🚀 REAL_API: performValidateLicenseAndRegisterDevice HTTP status code: \(httpResponse.statusCode)",
            service: serviceName)

        guard httpResponse.statusCode == 200 else {
            logger.warning(
                "🚀 REAL_API: performValidateLicenseAndRegisterDevice unexpected status code \(httpResponse.statusCode), expected 200",
                service: serviceName)
            throw APIError.networkError(
                "Failed to validate license - HTTP \(httpResponse.statusCode)")
        }

        do {
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = createFlexibleDateDecodingStrategy()
            let parsedResponse = try decoder.decode(LicenseValidationResponse.self, from: data)

            if parsedResponse.valid {
                logger.info(
                    "🚀 REAL_API: performValidateLicenseAndRegisterDevice success - key: \(request.licenseKey.prefix(8))..., type: \(parsedResponse.effectiveLicenseType ?? "nil"), devices: \(parsedResponse.effectiveDevicesUsed ?? 0)/\(parsedResponse.effectiveMaxDevices ?? 0)",
                    service: serviceName)
            } else {
                logger.warning(
                    "🚀 REAL_API: performValidateLicenseAndRegisterDevice validation failed - key: \(request.licenseKey.prefix(8))..., message: \(parsedResponse.message ?? "no message")",
                    service: serviceName)
            }

            return parsedResponse
        } catch {
            logger.error(
                "🚀 REAL_API: performValidateLicenseAndRegisterDevice failed to parse JSON response: \(error)",
                service: serviceName)
            throw APIError.networkError(
                "Failed to parse license validation response: \(error.localizedDescription)")
        }
    }

    // MARK: - Helper Methods

    /// Get device identifier
    private func getDeviceIdentifier() -> String {
        return LicenseKeyFormatter.generateDeviceID()
    }

    /// Get app version
    private func getAppVersion() -> String {
        return LicenseKeyFormatter.getAppVersion()
    }

    /// Validate email format
    private func isValidEmail(_ email: String) -> Bool {
        return LicenseKeyFormatter.isValidEmail(email)
    }

    /// Generate device metadata for license operations
    private func generateDeviceMetadata() -> DeviceMetadata {
        // Generate a user-friendly device name based on device type
        let deviceTypeString = getDeviceType() ?? "Mac"
        let deviceName = "\(deviceTypeString)"

        return DeviceMetadata(
            deviceName: deviceName,
            deviceType: getDeviceType(),
            deviceModel: getDeviceModel(),
            operatingSystem: getOperatingSystemVersion(),
            architecture: getArchitecture(),
            screenResolution: getScreenResolution(),
            totalMemory: getTotalMemory(),
            userNickname: deviceName,  // Use device type as default nickname
            location: "Unknown",  // Default location
            notes: "Automatically registered device"  // Default notes
        )
    }

    /// Test function to debug device type detection (DEBUG ONLY)
    func debugDeviceTypeDetection() {
        let deviceType = getDeviceType()
        let deviceModel = getDeviceModel()
        let os = getOperatingSystemVersion()
        let arch = getArchitecture()

        print("🔍 DEBUG DEVICE TYPE DETECTION:")
        print("  Device Type: \(deviceType ?? "nil")")
        print("  Device Model: \(deviceModel ?? "nil")")
        print("  OS: \(os ?? "nil")")
        print("  Architecture: \(arch ?? "nil")")

        logger.info(
            "🔍 DEBUG: Device Type: \(deviceType ?? "nil"), Model: \(deviceModel ?? "nil")",
            service: serviceName)
    }

    /// Get device type (e.g., "MacBook Pro", "iMac", "Mac Studio")
    private func getDeviceType() -> String? {
        var size = 0
        sysctlbyname("hw.model", nil, &size, nil, 0)
        var model = [CChar](repeating: 0, count: size)
        sysctlbyname("hw.model", &model, &size, nil, 0)
        let modelString = String(cString: model).trimmingCharacters(in: .whitespacesAndNewlines)

        logger.debug(
            "🔍 [DEVICE_TYPE] Raw hardware model string: '\(modelString)' (length: \(modelString.count))",
            service: serviceName)

        // Map hardware model to user-friendly device type
        // Using case-insensitive matching and checking for common patterns
        let lowercaseModel = modelString.lowercased()
        logger.debug(
            "🔍 [DEVICE_TYPE] Lowercase model for matching: '\(lowercaseModel)'",
            service: serviceName)

        let deviceType: String

        // Map specific hardware model identifiers to detailed device information
        // Handle both newer Mac##,# format and older MacBookPro##,# format
        deviceType = mapHardwareModelToDeviceType(modelString, lowercaseModel: lowercaseModel)

        logger.debug(
            "🎯 [DEVICE_TYPE] Final mapped device type: '\(deviceType)'", service: serviceName)
        return deviceType
    }

    /// Map hardware model identifier to detailed device type information
    private func mapHardwareModelToDeviceType(_ modelString: String, lowercaseModel: String)
        -> String
    {
        // Handle newer Mac##,# format (Apple Silicon era)
        switch modelString {
        // MacBook Pro models (Apple Silicon) - M4 generation
        case "Mac16,8":
            logger.debug(
                "✅ [DEVICE_TYPE] Matched MacBook Pro M4 14-inch (Mac16,8)", service: serviceName)
            return "MacBook Pro\n14-Inch, Nov 2024"
        case "Mac16,9":
            logger.debug(
                "✅ [DEVICE_TYPE] Matched MacBook Pro M4 16-inch (Mac16,9)", service: serviceName)
            return "MacBook Pro\n16-Inch, Nov 2024"

        // MacBook Pro M3 generation
        case "Mac15,3":
            logger.debug(
                "✅ [DEVICE_TYPE] Matched MacBook Pro M3 14-inch (Mac15,3)", service: serviceName)
            return "MacBook Pro\n14-Inch, Nov 2023"
        case "Mac15,6", "Mac15,8", "Mac15,9", "Mac15,10", "Mac15,11":
            logger.debug(
                "✅ [DEVICE_TYPE] Matched MacBook Pro M3 16-inch (Mac15,x)", service: serviceName)
            return "MacBook Pro\n16-Inch, Nov 2023"

        // MacBook Pro M2 generation
        case "Mac14,5", "Mac14,6":
            logger.debug(
                "✅ [DEVICE_TYPE] Matched MacBook Pro M2 13-inch (Mac14,x)", service: serviceName)
            return "MacBook Pro\n13-Inch, Jun 2022"
        case "Mac14,9", "Mac14,10":
            logger.debug(
                "✅ [DEVICE_TYPE] Matched MacBook Pro M2 Pro 14-inch (Mac14,x)", service: serviceName
            )
            return "MacBook Pro\n14-Inch, Jan 2023"
        case "Mac14,7":
            logger.debug(
                "✅ [DEVICE_TYPE] Matched MacBook Pro M2 Pro/Max 16-inch (Mac14,7)",
                service: serviceName)
            return "MacBook Pro\n16-Inch, Jan 2023"

        // MacBook Air models (Apple Silicon)
        case "Mac15,12":
            logger.debug(
                "✅ [DEVICE_TYPE] Matched MacBook Air M3 13-inch (Mac15,12)", service: serviceName)
            return "MacBook Air\n13-Inch, Mar 2024"
        case "Mac15,13":
            logger.debug(
                "✅ [DEVICE_TYPE] Matched MacBook Air M3 15-inch (Mac15,13)", service: serviceName)
            return "MacBook Air\n15-Inch, Mar 2024"
        case "Mac14,2":
            logger.debug(
                "✅ [DEVICE_TYPE] Matched MacBook Air M2 13-inch (Mac14,2)", service: serviceName)
            return "MacBook Air\n13-Inch, Jul 2022"
        case "Mac14,15":
            logger.debug(
                "✅ [DEVICE_TYPE] Matched MacBook Air M2 15-inch (Mac14,15)", service: serviceName)
            return "MacBook Air\n15-Inch, Jun 2023"
        case "Mac13,1":
            logger.debug(
                "✅ [DEVICE_TYPE] Matched MacBook Air M1 13-inch (Mac13,1)", service: serviceName)
            return "MacBook Air\n13-Inch, Nov 2020"

        // Mac Studio models
        case "Mac13,2":
            logger.debug(
                "✅ [DEVICE_TYPE] Matched Mac Studio M1 Ultra (Mac13,2)", service: serviceName)
            return "Mac Studio\nM1 Ultra, Mar 2022"
        case "Mac14,13":
            logger.debug(
                "✅ [DEVICE_TYPE] Matched Mac Studio M2 Max (Mac14,13)", service: serviceName)
            return "Mac Studio\nM2 Max, Jun 2023"
        case "Mac14,14":
            logger.debug(
                "✅ [DEVICE_TYPE] Matched Mac Studio M2 Ultra (Mac14,14)", service: serviceName)
            return "Mac Studio\nM2 Ultra, Jun 2023"

        // Mac mini models
        case "Mac14,3":
            logger.debug("✅ [DEVICE_TYPE] Matched Mac mini M2 (Mac14,3)", service: serviceName)
            return "Mac mini\nM2, Jan 2023"
        case "Mac14,12":
            logger.debug("✅ [DEVICE_TYPE] Matched Mac mini M2 Pro (Mac14,12)", service: serviceName)
            return "Mac mini\nM2 Pro, Jan 2023"

        // iMac models
        case "Mac15,4", "Mac15,5":
            logger.debug("✅ [DEVICE_TYPE] Matched iMac M3 24-inch (Mac15,x)", service: serviceName)
            return "iMac\n24-Inch, Nov 2023"

        default:
            break
        }

        // Fallback to legacy pattern matching for older models
        return mapLegacyHardwareModel(lowercaseModel: lowercaseModel, originalModel: modelString)
    }

    /// Handle legacy hardware model patterns (pre-Apple Silicon)
    private func mapLegacyHardwareModel(lowercaseModel: String, originalModel: String) -> String {
        if lowercaseModel.contains("macbookpro") {
            logger.debug("✅ [DEVICE_TYPE] Matched legacy MacBook Pro pattern", service: serviceName)
            return "MacBook Pro"
        } else if lowercaseModel.contains("macbookair") {
            logger.debug("✅ [DEVICE_TYPE] Matched legacy MacBook Air pattern", service: serviceName)
            return "MacBook Air"
        } else if lowercaseModel.contains("imacpro") {
            logger.debug("✅ [DEVICE_TYPE] Matched legacy iMac Pro pattern", service: serviceName)
            return "iMac Pro"
        } else if lowercaseModel.contains("imac") {
            logger.debug("✅ [DEVICE_TYPE] Matched legacy iMac pattern", service: serviceName)
            return "iMac"
        } else if lowercaseModel.contains("macmini") {
            logger.debug("✅ [DEVICE_TYPE] Matched legacy Mac mini pattern", service: serviceName)
            return "Mac mini"
        } else if lowercaseModel.contains("macpro") {
            logger.debug("✅ [DEVICE_TYPE] Matched legacy Mac Pro pattern", service: serviceName)
            return "Mac Pro"
        } else if lowercaseModel.contains("macstudio") {
            logger.debug("✅ [DEVICE_TYPE] Matched legacy Mac Studio pattern", service: serviceName)
            return "Mac Studio"
        } else if lowercaseModel.hasPrefix("mac") {
            logger.debug(
                "⚠️ [DEVICE_TYPE] Using generic Mac fallback for: \(lowercaseModel)",
                service: serviceName)
            return "Mac"
        } else {
            logger.debug(
                "❌ [DEVICE_TYPE] No pattern matched, using raw model: \(lowercaseModel)",
                service: serviceName)
            return originalModel.isEmpty ? "Unknown Mac" : originalModel
        }
    }

    /// Get hardware model identifier (e.g., "MacBookPro18,3")
    private func getDeviceModel() -> String? {
        var size = 0
        sysctlbyname("hw.model", nil, &size, nil, 0)
        var model = [CChar](repeating: 0, count: size)
        sysctlbyname("hw.model", &model, &size, nil, 0)
        let modelString = String(cString: model)

        // Return the raw hardware model identifier
        return modelString.isEmpty ? nil : modelString
    }

    /// Get operating system version
    private func getOperatingSystemVersion() -> String? {
        let version = ProcessInfo.processInfo.operatingSystemVersion
        return "macOS \(version.majorVersion).\(version.minorVersion).\(version.patchVersion)"
    }

    /// Get CPU architecture (helps distinguish Intel vs Apple Silicon)
    private func getArchitecture() -> String? {
        #if arch(arm64)
            return "Apple Silicon (arm64)"
        #elseif arch(x86_64)
            return "Intel (x86_64)"
        #else
            var size = 0
            sysctlbyname("hw.targettype", nil, &size, nil, 0)
            var machine = [CChar](repeating: 0, count: size)
            sysctlbyname("hw.targettype", &machine, &size, nil, 0)
            let machineString = String(cString: machine)
            return machineString.isEmpty ? nil : machineString
        #endif
    }

    /// Get primary screen resolution (helps identify display setup)
    private func getScreenResolution() -> String? {
        guard let mainScreen = NSScreen.main else { return nil }
        let frame = mainScreen.frame
        return "\(Int(frame.width)) × \(Int(frame.height))"
    }

    /// Get total system memory (helps distinguish different configurations)
    private func getTotalMemory() -> String? {
        var size = MemoryLayout<UInt64>.size
        var physicalMemory: UInt64 = 0
        let result = sysctlbyname("hw.memsize", &physicalMemory, &size, nil, 0)

        guard result == 0 else { return nil }

        let memoryGB = Double(physicalMemory) / (1024 * 1024 * 1024)
        return String(format: "%.0f GB", memoryGB)
    }

}

// MARK: - API Errors

/// API error types matching the Snapback License Management API specification
enum APIError: Error, LocalizedError {
    case validationError(String)
    case unauthorized
    case invalidToken
    case notFound(String)
    case licenseNotFound
    case deviceNotRegistered
    case licenseExpired
    case maxDevicesReached
    case rateLimitExceeded(retryAfter: Int)
    case internalError
    case networkError(String)
    case invalidResponse
    case invalidEmail
    case serverError(String)

    var errorDescription: String? {
        switch self {
        case .validationError(let message):
            return message
        case .unauthorized:
            return "Authentication failed. Please re-validate your license."
        case .invalidToken:
            return "Device token is invalid or expired"
        case .notFound(let resource):
            return "\(resource) not found"
        case .licenseNotFound:
            return "License key not found"
        case .deviceNotRegistered:
            return "Device not registered with license"
        case .licenseExpired:
            return "License has expired. Please renew your license."
        case .maxDevicesReached:
            return "Maximum number of devices reached for this license."
        case .rateLimitExceeded(let retryAfter):
            return "Too many requests. Please wait \(retryAfter) seconds."
        case .internalError:
            return "Server internal error. Please try again later."
        case .networkError(let message):
            return message
        case .invalidResponse:
            return "Invalid response from server"
        case .invalidEmail:
            return "Invalid email address"
        case .serverError(let message):
            return message
        }
    }

    /// HTTP status code associated with the error
    var httpStatusCode: Int {
        switch self {
        case .validationError:
            return 400
        case .unauthorized, .invalidToken:
            return 401
        case .notFound, .licenseNotFound, .deviceNotRegistered:
            return 404
        case .maxDevicesReached:
            return 409
        case .licenseExpired:
            return 410
        case .rateLimitExceeded:
            return 429
        case .internalError:
            return 500
        default:
            return 0  // Client-side errors
        }
    }
}
