import AppKit
import Carbon.HIToolbox
import Foundation
import KeyboardShortcuts
import MA<PERSON>hortcut

/// Bridge between ShortcutManager and KeyboardShortcuts
public class KeyboardShortcutsBridge {
    /// Shared instance of the bridge
    public static let shared = KeyboardShortcutsBridge()

    /// The shortcut manager
    private let shortcutManager = ShortcutManager.shared

    /// Map of KeyboardShortcuts.Name to string names
    private var nameMap: [KeyboardShortcuts.Name: String] = [:]

    /// Map of string names to default shortcuts
    private var defaultShortcuts: [String: Shortcut] = [:]

    /// Flag to prevent infinite notification loops
    private var isUpdatingShortcut = false

    /// Initialize a new bridge
    private init() {
        // Set up notification observers
        setupNotificationObservers()
    }

    /// Set up notification observers
    private func setupNotificationObservers() {
        // Observe shortcut changes from KeyboardShortcuts
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleKeyboardShortcutChanged(_:)),
            name: Notification.Name("KeyboardShortcuts_shortcutByNameDidChange"),
            object: nil
        )

        // Observe shortcut changes from ShortcutManager
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleShortcutManagerChanged(_:)),
            name: Notification.Name("ShortcutManager.shortcutChanged"),
            object: nil
        )
    }

    /// Handle a shortcut change from KeyboardShortcuts
    /// - Parameter notification: The notification
    @objc private func handleKeyboardShortcutChanged(_ notification: Notification) {
        // Prevent infinite notification loops
        if isUpdatingShortcut {
            return
        }

        guard let name = notification.userInfo?["name"] as? KeyboardShortcuts.Name,
            let stringName = nameMap[name]
        else { return }

        // Set the flag to prevent recursive updates
        isUpdatingShortcut = true

        // Get the shortcut from KeyboardShortcuts
        if let keyboardShortcut = KeyboardShortcuts.getShortcut(for: name),
            let shortcut = ShortcutConverter.fromKeyboardShortcut(keyboardShortcut)
        {
            // Update the shortcut in ShortcutManager
            shortcutManager.setShortcut(shortcut, forName: stringName)
        } else {
            // Remove the shortcut from ShortcutManager
            shortcutManager.setShortcut(nil, forName: stringName)
        }

        // Reset the flag
        isUpdatingShortcut = false

        // Notify the app to refresh the menu
        NotificationCenter.default.post(
            name: .refreshStatusMenu,
            object: nil
        )
    }

    /// Handle a shortcut change from ShortcutManager
    /// - Parameter notification: The notification
    @objc private func handleShortcutManagerChanged(_ notification: Notification) {
        // Prevent infinite notification loops
        if isUpdatingShortcut {
            return
        }

        guard let stringName = notification.userInfo?["name"] as? String else { return }

        // Set the flag to prevent recursive updates
        isUpdatingShortcut = true

        // Find the KeyboardShortcuts.Name for the string name
        for (name, mappedName) in nameMap where mappedName == stringName {
            // Get the shortcut from ShortcutManager
            if let shortcut = shortcutManager.getShortcut(forName: stringName),
                let keyboardShortcut = ShortcutConverter.toKeyboardShortcut(shortcut)
            {
                // Update the shortcut in KeyboardShortcuts
                KeyboardShortcuts.setShortcut(keyboardShortcut, for: name)
            } else {
                // Remove the shortcut from KeyboardShortcuts
                KeyboardShortcuts.setShortcut(nil, for: name)
            }

            break
        }

        // Reset the flag
        isUpdatingShortcut = false

        // Notify the app to refresh the menu
        NotificationCenter.default.post(
            name: .refreshStatusMenu,
            object: nil
        )
    }

    /// Register a KeyboardShortcuts.Name with a string name
    /// - Parameters:
    ///   - name: The KeyboardShortcuts.Name
    ///   - stringName: The string name
    ///   - defaultShortcut: The default shortcut, if any
    public func registerName(
        _ name: KeyboardShortcuts.Name, stringName: String, defaultShortcut: Shortcut? = nil
    ) {
        nameMap[name] = stringName

        if let defaultShortcut = defaultShortcut {
            defaultShortcuts[stringName] = defaultShortcut
        }
    }

    /// Register a shortcut handler
    /// - Parameters:
    ///   - name: The KeyboardShortcuts.Name
    ///   - action: The action to perform when the shortcut is triggered
    public func registerShortcutHandler(
        for name: KeyboardShortcuts.Name, action: @escaping () -> Void
    ) {
        // Register the handler with KeyboardShortcuts
        KeyboardShortcuts.onKeyDown(for: name) {
            // Execute the action on the main thread to avoid any threading issues
            DispatchQueue.main.async {
                action()
            }
        }

        // We're only using KeyboardShortcuts for registration, not MASShortcuts
        // MASShortcuts is only used for validation purposes
        // The following code is intentionally removed:
        /*
        // Also register with ShortcutManager if we have a mapping
        if let stringName = nameMap[name],
            let shortcut = getShortcutForName(name)
        {
            shortcutManager.registerShortcut(shortcut, forName: stringName, action: action)
        }
        */
    }

    /// Get the shortcut for a KeyboardShortcuts.Name
    /// - Parameter name: The KeyboardShortcuts.Name
    /// - Returns: The shortcut, if any
    public func getShortcutForName(_ name: KeyboardShortcuts.Name) -> Shortcut? {
        // Try to get the shortcut from KeyboardShortcuts
        if let keyboardShortcut = KeyboardShortcuts.getShortcut(for: name) {
            return ShortcutConverter.fromKeyboardShortcut(keyboardShortcut)
        }

        // Try to get the shortcut from ShortcutManager
        if let stringName = nameMap[name] {
            return shortcutManager.getShortcut(forName: stringName)
        }

        return nil
    }

    /// Set a shortcut for a KeyboardShortcuts.Name
    /// - Parameters:
    ///   - shortcut: The shortcut to set
    ///   - name: The KeyboardShortcuts.Name
    public func setShortcut(_ shortcut: Shortcut?, for name: KeyboardShortcuts.Name) {
        // Set the flag to prevent recursive updates
        isUpdatingShortcut = true

        // Set the shortcut in KeyboardShortcuts
        if let shortcut = shortcut,
            let keyboardShortcut = ShortcutConverter.toKeyboardShortcut(shortcut)
        {
            KeyboardShortcuts.setShortcut(keyboardShortcut, for: name)
        } else {
            KeyboardShortcuts.setShortcut(nil, for: name)
        }

        // Also set in ShortcutManager if we have a mapping
        if let stringName = nameMap[name] {
            shortcutManager.setShortcut(shortcut, forName: stringName)
        }

        // Reset the flag
        isUpdatingShortcut = false
    }

    /// Reset a shortcut to its default value
    /// - Parameter name: The KeyboardShortcuts.Name
    public func resetShortcut(for name: KeyboardShortcuts.Name) {
        // Set the flag to prevent recursive updates
        isUpdatingShortcut = true

        // First, clear the shortcut
        KeyboardShortcuts.reset(name)

        // Then, set it back to its default value if we have one
        if let stringName = nameMap[name],
            let defaultShortcut = defaultShortcuts[stringName]
        {

            // Convert to KeyboardShortcuts.Shortcut
            if let keyboardShortcut = ShortcutConverter.toKeyboardShortcut(defaultShortcut) {
                // Set the shortcut to its default value
                KeyboardShortcuts.setShortcut(keyboardShortcut, for: name)

                // Also set in ShortcutManager
                shortcutManager.setShortcut(defaultShortcut, forName: stringName)

                print(
                    "Reset shortcut for \(name) to default: \(defaultShortcut.displayString ?? "Unknown")"
                )
            }
        } else {
            // If no default shortcut, just reset in ShortcutManager
            if let stringName = nameMap[name] {
                shortcutManager.resetShortcut(forName: stringName)
            }
        }

        // Reset the flag
        isUpdatingShortcut = false

        // Notify the app to refresh the menu
        NotificationCenter.default.post(
            name: .refreshStatusMenu,
            object: nil
        )
    }

    /// Reset all shortcuts to their default values
    public func resetAllShortcuts() {
        // Set the flag to prevent recursive updates
        isUpdatingShortcut = true

        // First, reset all shortcuts to nil to clear any existing values
        KeyboardShortcuts.resetAll()

        // Then, reset each shortcut to its default value
        for (name, stringName) in nameMap {
            // Check if we have a default shortcut for this name
            if let defaultShortcut = defaultShortcuts[stringName] {

                // Convert to KeyboardShortcuts.Shortcut
                if let keyboardShortcut = ShortcutConverter.toKeyboardShortcut(defaultShortcut) {
                    // Set the shortcut to its default value
                    KeyboardShortcuts.setShortcut(keyboardShortcut, for: name)

                    // Also set in ShortcutManager
                    shortcutManager.setShortcut(defaultShortcut, forName: stringName)

                    print(
                        "Reset shortcut for \(name) to default: \(defaultShortcut.displayString ?? "Unknown")"
                    )
                }
            }
        }

        // Reset the flag
        isUpdatingShortcut = false

        // Notify the app to refresh the menu
        NotificationCenter.default.post(
            name: .refreshStatusMenu,
            object: nil
        )
    }


}
