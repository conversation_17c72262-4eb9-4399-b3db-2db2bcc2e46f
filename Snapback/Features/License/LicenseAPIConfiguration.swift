import Foundation
import Security

/// Configuration manager for the Snapback License Management API
/// Handles environment switching, API endpoints, and secure credential storage
class LicenseAPIConfiguration {
    static let shared = LicenseAPIConfiguration()

    private init() {}

    // MARK: - Environment Configuration

    /// Current API environment
    enum Environment: String, CaseIterable {
        case development = "development"
        case production = "production"

        var displayName: String {
            switch self {
            case .development:
                return "Development Server"
            case .production:
                return "Production Server"
            }
        }
    }

    /// Current environment (can be overridden for testing)
    var currentEnvironment: Environment {
        // Check for environment override first
        if let override = environmentOverride {
            return override
        }

        // Check for environment variable
        if let envString = ProcessInfo.processInfo.environment["SNAPBACK_API_ENV"],
            let env = Environment(rawValue: envString)
        {
            return env
        }

        // Default based on build configuration
        #if DEBUG
            return .development  // Use development server in debug builds
        #else
            return .production
        #endif
    }

    /// Environment override for testing (not persisted)
    private var environmentOverride: Environment?

    /// Override the current environment (useful for testing)
    func setEnvironmentOverride(_ environment: Environment?) {
        environmentOverride = environment
    }

    // MARK: - API Endpoints

    /// Base URL for the current environment
    var baseURL: String {
        switch currentEnvironment {
        case .development:
            return developmentBaseURL
        case .production:
            return productionBaseURL
        }
    }

    /// Development server base URL
    private var developmentBaseURL: String {
        // Allow override via environment variable
        return ProcessInfo.processInfo.environment["SNAPBACK_DEV_API_URL"]
            ?? "http://localhost:3000/api"
    }

    /// Production server base URL
    private var productionBaseURL: String {
        // Allow override via environment variable for staging/testing
        return ProcessInfo.processInfo.environment["SNAPBACK_PROD_API_URL"]
            ?? "https://api.snapbackapp.com/api"
    }

    // MARK: - API Configuration

    /// Request timeout interval in seconds
    var requestTimeout: TimeInterval {
        return 30.0
    }

    /// Maximum number of retry attempts for failed requests
    var maxRetryAttempts: Int {
        return 3
    }

    /// Base delay for exponential backoff (in seconds)
    var baseRetryDelay: TimeInterval {
        return 1.0
    }

    /// User Agent string for API requests
    var userAgent: String {
        let appVersion =
            Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0"
        let buildNumber = Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "1"
        return "SnapbackApp/\(appVersion) (\(buildNumber); macOS)"
    }

    // MARK: - Security Configuration

    /// Keychain service identifier for storing API credentials
    private let keychainService = "com.snapbackapp.license-api"

    /// Store device token securely in keychain
    func storeDeviceToken(_ token: String) {
        storeSecureValue(token, forKey: "device_token")
    }

    /// Retrieve device token from keychain
    func getDeviceToken() -> String? {
        return getSecureValue(forKey: "device_token")
    }

    /// Clear device token from keychain
    func clearDeviceToken() {
        deleteSecureValue(forKey: "device_token")
    }

    /// Store API key securely in keychain (if needed for future use)
    func storeAPIKey(_ apiKey: String) {
        storeSecureValue(apiKey, forKey: "api_key")
    }

    /// Retrieve API key from keychain
    func getAPIKey() -> String? {
        return getSecureValue(forKey: "api_key")
    }

    /// Clear API key from keychain
    func clearAPIKey() {
        deleteSecureValue(forKey: "api_key")
    }

    // MARK: - Request Signing (Optional)

    /// Request signing secret (if enabled)
    private var requestSigningSecret: String? {
        return ProcessInfo.processInfo.environment["SNAPBACK_REQUEST_SIGNING_SECRET"]
    }

    /// Whether request signing is enabled
    var isRequestSigningEnabled: Bool {
        return requestSigningSecret != nil
    }

    // MARK: - Private Keychain Helpers

    private func storeSecureValue(_ value: String, forKey key: String) {
        let data = value.data(using: .utf8)!

        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: keychainService,
            kSecAttrAccount as String: key,
            kSecValueData as String: data,
        ]

        // Delete existing item first
        SecItemDelete(query as CFDictionary)

        // Add new item
        let status = SecItemAdd(query as CFDictionary, nil)
        if status != errSecSuccess {
            print("Failed to store secure value for key \(key): \(status)")
        }
    }

    private func getSecureValue(forKey key: String) -> String? {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: keychainService,
            kSecAttrAccount as String: key,
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitOne,
        ]

        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)

        guard status == errSecSuccess,
            let data = result as? Data,
            let value = String(data: data, encoding: .utf8)
        else {
            return nil
        }

        return value
    }

    private func deleteSecureValue(forKey key: String) {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: keychainService,
            kSecAttrAccount as String: key,
        ]

        SecItemDelete(query as CFDictionary)
    }

    // MARK: - Debug Information

    /// Get current configuration summary for debugging
    func getConfigurationSummary() -> [String: Any] {
        return [
            "environment": currentEnvironment.rawValue,
            "baseURL": baseURL,
            "requestTimeout": requestTimeout,
            "maxRetryAttempts": maxRetryAttempts,
            "userAgent": userAgent,
            "hasDeviceToken": getDeviceToken() != nil,
            "hasAPIKey": getAPIKey() != nil,
            "requestSigningEnabled": isRequestSigningEnabled,
        ]
    }
}
